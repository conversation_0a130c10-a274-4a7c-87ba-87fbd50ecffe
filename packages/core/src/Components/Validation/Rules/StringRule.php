<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;

/**
 * Class StringRule
 *
 * @package Core\Components\Validation\Rules
 */
class StringRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'exact_length' => '{label} must be exactly %d character(s) long',
            'min_length' => '{label} must be longer than or equal to {length} character(s)',
            'max_length' => '{label} must be shorter than or equal to {length} character(s)'
        ];
    }

    /**
     * Trim field
     *
     * @param mixed $field
     * @return bool
     */
    #[RuleAttribute('trim')]
    public function trim(mixed &$field): bool
    {
        $field = trim((string) $field);
        return true;
    }

    /**
     * Verify length of field matches specified length exactly
     *
     * @param mixed $field
     * @param string|int $param
     * @return bool|array
     */
    #[RuleAttribute('exact_length')]
    public function exactLength(mixed $field, string|int $param): bool|array
    {
        if (strlen((string) $field) === (int) $param) {
            return true;
        }
        return ['exact_length', ['length' => $param]];
    }

    /**
     * Verify length of field meets defined minimum
     *
     * @param mixed $field
     * @param string|int $param
     * @return bool|array
     */
    #[RuleAttribute('min_length')]
    public function minLength(mixed $field, string|int $param): bool|array
    {
        if (strlen((string) $field) >= (int) $param) {
            return true;
        }
        return ['min_length', ['length' => $param]];
    }

    /**
     * Verify length of field meets defined minimum
     *
     * @param mixed $field
     * @param string|int $param
     * @return bool|array
     */
    #[RuleAttribute('max_length')]
    public function maxLength(mixed $field, string|int $param): bool|array
    {
        if (mb_strlen((string) $field) <= (int)$param) {
            return true;
        }
        return ['max_length', ['length' => $param]];
    }
}
