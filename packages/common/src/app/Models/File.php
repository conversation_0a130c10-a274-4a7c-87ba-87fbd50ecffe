<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class File extends HistoryEntityModel implements Interfaces\FileInterface
{
    use Common\FileCommon;

    protected $table = 'files';
    protected $primaryKey = 'fileID';
    protected $dates = ['startedAt', 'finishedAt'];
    protected $fillable = [
        'fileID', 'companyID', 'type', 'status', 'name', 'description', 'contentType', 'extension', 'size', 'data',
        'contentHash', 'time', 'isDataDeleted', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
