<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\Project;
use Common\Models\Task;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;

trait ProjectScheduleCommon
{
    use ScopeSearchTrait;

    protected $castMap = [
        'projectID' => 'int',
        'status' => 'int',
        'source' => 'int',
        'scheduledUserID' => 'int',
        'isAllDay' => 'bool',
        'sequence' => 'int',
        'isPending' => 'bool',
        'updateCount' => 'int',
        'sendNotifications' => 'bool',
        'isSyncable' => 'bool',
        'isCalendarPushInProgress' => 'bool',
        'isSyncInProgress' => 'bool',
        'completedByUserID' => 'int',
        'replacedByUserID' => 'int',
        'replacedByProjectScheduleID' => 'int',
        'cancelledByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['scheduledStart', 'scheduledEnd', 'start', 'end', 'pendingAt', 'completedAt', 'replacedAt', 'cancelledAt'];
    protected $searchColumns = ['description'];

    public function project()
    {
        return $this->belongsTo(Project::class, 'projectID', 'projectID');
    }

    public function scheduledUser()
    {
        return $this->belongsTo(User::class, 'scheduledUserID', 'userID');
    }

    public function scopeWithProperty($query)
    {
        $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        return $query;
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        $query->join('customer', 'customer.customerID', '=', 'property.customerID');
        $query->join('companies', 'companies.companyID', '=', 'customer.companyID');
        $query->join('timezones', 'timezones.timezoneID', '=', 'companies.timezoneID');
        return $query->where('companies.companyID', $company);
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        $query->join('customer', 'customer.customerID', '=', 'property.customerID');
        $query->join('companies', 'companies.companyID', '=', 'customer.companyID');
        $query->join('timezones', 'timezones.timezoneID', '=', 'companies.timezoneID');
        return $query->where(function ($query) use ($user) {
            $query->where("{$this->table}.scheduledUserID", $user)
                ->orWhere("project.projectSalesperson", $user)
                ->orWhere("project.createdByUserID", $user);
        });
    }


    public function tasks()
    {
        return $this->morphMany(Task::class, 'tasks', 'associationType', 'associationID');
    }
}
