<?php

use App\Http\Controllers\ApiV1;
use App\Http\Controllers\App\API\Integrations\Wisetack\Webhooks\WisetackWebhookHandler;
use App\Http\Controllers\App\API\Integrations\Wisetack\WisetackController;
use App\Http\Controllers\App\API\LeadFormController;
use Core\Components\Http\StaticAccessors\Route;

// webhooks
Route::any('api/integration/google-calendar/event-webhook')
    ->to(App\Http\Controllers\App\API\Integrations\GoogleCalendarController::class, 'eventWebhook');

Route::post('api/integration/mailgun/event-webhook')
    ->to(App\Http\Controllers\App\API\Integrations\MailgunController::class, 'eventWebhook');

Route::post('api/integration/mailgun/unmonitored-webhook')
    ->to(App\Http\Controllers\App\API\Integrations\MailgunController::class, 'unmonitoredWebhook');

Route::post('api/integration/twilio/message-status-webhook/{id}')
    ->to(App\Http\Controllers\App\API\Integrations\TwilioController::class, 'messageStatusWebhook')
    ->bind('id', '[a-f0-9\-]{36}')
    ->name('api.integration.twilio.message-status-webhook');

Route::post('api/integration/twilio/incoming-message-webhook')
    ->to(App\Http\Controllers\App\API\Integrations\TwilioController::class, 'incomingMessageWebhook');

Route::post('api/integration/wisetack/webhooks/signup-link')
        ->to(WisetackWebhookHandler::class, 'signup')->name('wisetack.webhooks.signup-link');

Route::post('api/integration/wisetack/webhooks/prequal-link')
    ->to(WisetackWebhookHandler::class, 'prequal')->name('wisetack.webhooks.prequal-link');

Route::post('api/integration/wisetack/webhooks/transactions')
    ->to(WisetackWebhookHandler::class, 'transaction')->name('wisetack.webhooks.transactions');


// api v1 routes
Route::any('api/v1/ping')->to(App\Http\Controllers\ApiV1Controller::class, 'ping');
Route::group(function () {
    Route::post('authenticate')->to(ApiV1\AuthController::class, 'handle');
    Route::group(function () {
        Route::apiResource('app-notification-distribution', ApiV1\AppNotificationDistributionController::class);
        Route::get('app-notification/me')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'fetch');

        Route::apiResource('additional-costs', ApiV1\AdditionalCostController::class);
        Route::put('batch')->to(ApiV1\BatchController::class, 'handle');
        Route::apiResource('bid/content', ApiV1\Bid\ContentController::class);
        Route::get('bid/forms')->to(ApiV1\Bid\FormController::class, 'index');
        Route::apiResource('bid/items', ApiV1\Bid\ItemController::class, [
            'upgrade' => [
                'path' => '/{id}/upgrade',
                'method' => 'put',
                'action' => 'upgrade',
                'name' => '.upgrade'
            ],
            'custom-drawing-upload' => [
                'path' => '/{id}/custom-drawing-upload',
                'method' => 'put',
                'action' => 'customDrawingUpload',
                'name' => '.custom-drawing-upload'
            ]
        ]);
        Route::apiResource('companies', ApiV1\CompanyController::class, [
            'delete' => [
                'disable' => true
            ],
            'image-modify' => [
                'path' => '/{id}/image',
                'method' => 'put',
                'action' => 'modifyImage',
                'name' => '.image.modify'
            ],
            'settings' => [
                'path' => '/{id}/settings',
                'method' => 'get',
                'action' => 'allSettings',
                'name' => '.settings.all'
            ],
            'setting-modify' => [
                'path' => '/{id}/settings',
                'method' => 'put',
                'action' => 'modifySettings',
                'name' => '.settings.modify'
            ],
            'subscription-options' => [
                'path' => '/{id}/subscription-options',
                'method' => 'get',
                'action' => 'getSubscriptionOptions',
                'name' => '.subscription-options'
            ],
            'subscription-modify' => [
                'path' => '/{id}/modify-subscription',
                'method' => 'put',
                'action' => 'modifySubscription',
                'name' => '.modify-subscription'
            ],
            'subscription-create' => [
                'path' => '/{id}/create-subscription',
                'method' => 'post',
                'action' => 'createSubscription',
                'name' => '.subscription-create'
            ]
        ]);
        Route::apiResource('company/custom-reports', ApiV1\Company\CustomReportController::class, [
            'store' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ],
            'run' => [
                'path' => '/{id}/run',
                'method' => 'put',
                'action' => 'run',
                'name' => '.run'
            ]
        ]);
        Route::apiResource('company/custom-report/results', ApiV1\Company\CustomReport\ResultController::class, [
            'store' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ]
        ]);
        Route::apiResource('company/form/categories', ApiV1\Company\Form\CategoryController::class);
        Route::apiResource('company/form/items', ApiV1\Company\Form\ItemController::class, [
            'partial-update' => [
                'disable' => true
            ],
            'categories' => [
                'path' => '/{id}/categories',
                'method' => 'put',
                'action' => 'categories',
                'name' => '.categories'
            ],
            'preview' => [
                'path' => '/preview',
                'method' => 'post',
                'action' => 'preview',
                'name' => '.preview'
            ],
            'renderPreview' => [
                'path' => '/preview/{id}',
                'method' => 'post',
                'action' => 'renderPreview',
                'name' => '.render-preview'
            ],
            'archive' => [
                'path' => '/{id}/archive',
                'method' => 'put',
                'action' => 'archive',
                'name' => '.archive'
            ]
        ]);
        Route::apiResource('company/form/categories-items', ApiV1\Company\Form\CategoryItemController::class);
        Route::apiResource('company/invoices', ApiV1\Company\InvoiceController::class, [
            'store' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ],
            'pay' => [
                'path' => '/{id}/pay',
                'method' => 'put',
                'action' => 'pay',
                'name' => '.pay'
            ]
        ]);
        Route::apiResource('company/invoice-credits', ApiV1\Company\InvoiceCreditController::class, [
            'delete' => [
                'disable' => true
            ]
        ]);
        Route::apiResource('company/payment-methods', ApiV1\Company\PaymentMethodController::class);
        Route::apiResource('company/subscriptions', ApiV1\Company\SubscriptionController::class, [
            'store' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ],
            'settle' => [
                'path' => '/{id}/settle',
                'method' => 'put',
                'action' => 'settle',
                'name' => '.settle'
            ]
        ]);
        Route::apiResource('company/subscription/price-adjustments', ApiV1\Company\Subscription\PriceAdjustmentController::class);
        Route::apiResource('content-partials', ApiV1\ContentPartialController::class);
        Route::apiResource('content-templates', ApiV1\ContentTemplateController::class);
        Route::apiResource('customers', ApiV1\CustomerController::class);
        Route::put('drawings/sync')->to(ApiV1\DrawingController::class, 'sync');
        Route::apiResource('drawings', ApiV1\DrawingController::class, [
            'image-modify' => [
                'path' => '/{id}/image',
                'method' => 'put',
                'action' => 'modifyImage',
                'name' => '.image.modify'
            ],
            'lock' => [
                'path' => '/{id}/lock',
                'method' => 'put',
                'action' => 'lock',
                'name' => '.lock'
            ],
            'unlock' => [
                'path' => '/{id}/lock',
                'method' => 'delete',
                'action' => 'unlock',
                'name' => '.unlock'
            ],
            'finalize' => [
                'path' => '/{id}/finalize',
                'method' => 'put',
                'action' => 'finalize',
                'name' => '.finalize'
            ]
        ]);
        Route::apiResource('email-templates', ApiV1\EmailTemplateController::class);
        Route::apiResource('files', ApiV1\FileController::class, [
            'image-modify' => [
                'path' => '/{id}/image',
                'method' => 'put',
                'action' => 'modifyImage',
                'name' => '.image.modify'
            ],
        ]);
        Route::apiResource('form/item/entries', ApiV1\Form\Item\EntryController::class, [
            'all' => [
                'disable' => true
            ],
            'store' => [
                'disable' => true
            ],
            'retrieve' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ],
            'upload' => [
                'path' => '/{id}/upload',
                'method' => 'put',
                'action' => 'upload',
                'name' => '.upload'
            ]
        ]);
        Route::apiResource('industry/product/items', ApiV1\Industry\Product\ItemController::class);
        Route::apiResource('leads', ApiV1\LeadController::class);

        Route::get('lead-forms-action/info')->to(LeadFormController::class, 'getCompanyMarketingInfo');
        Route::post('lead-forms-action/activate')->to(LeadFormController::class, 'activateForm');
        Route::apiResource('lead-forms', LeadFormController::class);

        Route::get('lead-forms-action/current')->to(LeadFormController::class, 'retrieveCurrent')
            ->name('lead-form.retrieve-current');

        Route::apiResource('materials', ApiV1\MaterialController::class);
        Route::apiResource('media', ApiV1\MediaController::class, [
            'generate-public-url' => [
                'path' => '/{id}/generate-public-url',
                'method' => 'get',
                'action' => 'generatePublicUrl',
                'name' => '.generate-public-url'
            ]
        ]);
        Route::apiResource('product/categories', ApiV1\Product\CategoryController::class);
        Route::apiResource('product/items', ApiV1\Product\ItemController::class);
        Route::apiResource('product/item/meta', ApiV1\Product\Item\MetaController::class);
        Route::apiResource('product/categories-items', ApiV1\Product\CategoryItemController::class);
        Route::apiResource('projects', ApiV1\ProjectController::class);
        Route::apiResource('project/types', ApiV1\ProjectTypeController::class);
        Route::apiResource('project/contacts', ApiV1\Project\ContactsController::class);
        Route::apiResource('project/events', ApiV1\Project\EventController::class, [
            'delete' => [
                'disable' => true
            ]
        ]);
        Route::apiResource('project/files', ApiV1\Project\FileController::class);
        Route::apiResource('project/notes', ApiV1\Project\NoteController::class);
        Route::apiResource('properties', ApiV1\PropertyController::class);
        Route::get('search')->to(ApiV1\SearchController::class, 'handle');
        Route::apiResource('system/form/categories', ApiV1\System\Form\CategoryController::class, [
            'store' => [
                'disable' => true
            ],
            'retrieve' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ]
        ]);
        Route::apiResource('system/form/items', ApiV1\System\Form\ItemController::class, [
            'store' => [
                'disable' => true
            ],
            'update' => [
                'disable' => true
            ],
            'partial-update' => [
                'disable' => true
            ],
            'delete' => [
                'disable' => true
            ]
        ]);
        Route::apiResource('tasks', ApiV1\TaskController::class);
        Route::apiResource('financing/prequals', ApiV1\Financing\WisetackPrequalController::class);
        Route::apiResource('financing/transactions', ApiV1\Financing\WisetackTransactionController::class);
        Route::apiResource('units', ApiV1\UnitController::class);
        Route::apiResource('users', ApiV1\UserController::class, [
            'delete' => [
                'disable' => true
            ],
            'image-modify' => [
                'path' => '/{id}/image',
                'method' => 'put',
                'action' => 'modifyImage',
                'name' => '.image.modify'
            ],
            'change-password' => [
                'path' => '/{id}/change-password',
                'method' => 'put',
                'action' => 'changePassword',
                'name' => '.change-password'
            ],
            'send-access-link' => [
                'path' => '/{id}/send-access-link',
                'method' => 'put',
                'action' => 'sendAccessLink',
                'name' => '.send-access-link'
            ]
        ]);
        // Import API endpoints
        Route::post('import/products')
            ->to(ApiV1\ImportController::class, 'importProducts')
            ->name('api.import.products');
        Route::post('import/materials')
            ->to(ApiV1\ImportController::class, 'importMaterials')
            ->name('api.import.materials');
        Route::post('import/additional-costs')
            ->to(ApiV1\ImportController::class, 'importAdditionalCosts')
            ->name('api.import.additional-costs');
    })->middleware('api-auth');
})->prefix('api/v1/')->name('api.v1')->middleware('api-log');


Route::post('lead-forms-action/ingest')->to(LeadFormController::class, 'ingest')
    ->name('lead-forms-action.ingest');


Route::get('media/{slug}/{version}/{id}')->to(App\Http\Controllers\MediaController::class, 'serve')
    ->middleware('domain')
    ->middleware('media-auth');
// handle legacy files via media controller
Route::get('image.php')->to(App\Http\Controllers\MediaController::class, 'serveLegacy')
    ->middleware('media-auth');

Route::group(function () {
    Route::get('admin/impersonation/current')->to(App\Http\Controllers\App\API\Admin\ImpersonationController::class, 'current');
    Route::get('admin/impersonation/search')->to(App\Http\Controllers\App\API\Admin\ImpersonationController::class, 'search');
    Route::patch('admin/reseller/code/{id}')->to(App\Http\Controllers\App\API\ResellerRegistrationProfileController::class, 'update')
        ->bind('id', '[0-9]+');;
    Route::post('admin/reseller/code')->to(App\Http\Controllers\App\API\ResellerRegistrationProfileController::class, 'create');
    Route::put('admin/user/{id}/access-link')->to(\App\Http\Controllers\App\API\UserController::class, 'sendAccessLink')
        ->bind('id', '[0-9]+');
    Route::put('admin/user/{id}/update')->to(\App\Http\Controllers\App\API\UserController::class, 'update')
        ->bind('id', '[0-9]+');
    Route::post('admin/impersonation/impersonate')->to(App\Http\Controllers\App\API\Admin\ImpersonationController::class, 'impersonate');
    Route::put('bid/{id}/reset-final-packet')->to(\App\Http\Controllers\App\API\BidController::class, 'resetFinalPacket')
        ->bind('id', '[0-9]+');
    Route::put('bid/{id}/uncancel')->to(\App\Http\Controllers\App\API\BidController::class, 'uncancel')
        ->bind('id', '[0-9]+');
    Route::get('calendar/resources')->to(App\Http\Controllers\App\API\CalendarController::class, 'resources');
    Route::post('calendar/resources/order')->to(App\Http\Controllers\App\API\CalendarController::class, 'resourcesOrder');
    Route::get('customer/duplicates')->to(App\Http\Controllers\App\API\CustomerController::class, 'duplicateCustomer');
    Route::get('customers/search')->to(App\Http\Controllers\App\API\CustomerController::class, 'search');
    Route::post('customers')->to(App\Http\Controllers\App\API\CustomerController::class, 'add');
    Route::post('customers/from-property')->to(App\Http\Controllers\App\API\CustomerController::class, 'addFromProperty');
    Route::put('customers')->to(App\Http\Controllers\App\API\CustomerController::class, 'update');
    Route::post('errors')->to(App\Http\Controllers\App\API\ErrorController::class, 'ingest');
    Route::get('export/customers')->to(App\Http\Controllers\App\API\ExportController::class, 'customers');
    Route::get('export/products')->to(App\Http\Controllers\App\API\ExportController::class, 'products');
    Route::get('export/properties')->to(App\Http\Controllers\App\API\ExportController::class, 'properties');
    Route::get('export/projects')->to(App\Http\Controllers\App\API\ExportController::class, 'projects');
    Route::get('export/bid-line-items')->to(App\Http\Controllers\App\API\ExportController::class, 'bidLineItems');
    Route::get('export/tasks')->to(App\Http\Controllers\App\API\ExportController::class, 'tasks');
    Route::get('export/leads')->to(App\Http\Controllers\App\API\ExportController::class, 'leads');
    Route::get('export/materials')->to(App\Http\Controllers\App\API\ExportController::class, 'materials');
    Route::get('export/additional-costs')->to(App\Http\Controllers\App\API\ExportController::class, 'additionalCosts');
    Route::get('export/financing-prequalifications')->to(App\Http\Controllers\App\API\ExportController::class, 'financingPrequalifications');
    Route::get('export/financing-transactions')->to(App\Http\Controllers\App\API\ExportController::class, 'financingTransactions');
    Route::get('export/financing-opportunities')->to(App\Http\Controllers\App\API\ExportController::class, 'financingOpportunities');

    Route::get('company/profile/integrations')->to(App\Http\Controllers\App\API\CompanyController::class, 'integrations');
    Route::post('company/profile/sync-quickbooks')->to(App\Http\Controllers\App\API\CompanyController::class, 'syncQuickbooks');
    Route::post('company/profile/delete-quickbooks')->to(App\Http\Controllers\App\API\CompanyController::class, 'deleteQuickbooks');
    Route::get('company/profile/total-users')->to(App\Http\Controllers\App\API\CompanyController::class, 'totalUsers');
    Route::get('company/marketing-sources')->to(App\Http\Controllers\App\API\CompanyController::class, 'marketingSources');
    Route::put('company/{id}/subscription/cancel')->to(\App\Http\Controllers\App\API\CompanyController::class, 'cancel')
        ->bind('id', '[0-9]+');
    Route::put('company/{id}/subscription/change-trial-date')->to(\App\Http\Controllers\App\API\CompanyController::class, 'changeTrialDate')
        ->bind('id', '[0-9]+');
    Route::post('company/{id}/invoice/create')->to(\App\Http\Controllers\App\API\CompanyController::class, 'createInvoice')
        ->bind('id', '[0-9]+');
    Route::get('pricing/features')->to(App\Http\Controllers\App\API\CorporateSiteController::class, 'pricingFeatures');
    Route::get('intake/options')->to(App\Http\Controllers\App\API\IntakeController::class, 'options');

    Route::get('app-notification')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'all');
    Route::get('app-notification/{id}')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'show');
    Route::post('app-notification')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'create');
    Route::post('app-notification/{id}')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'update')
        ->bind('id', '[A-F0-9\-]{32}');

    Route::delete('app-notification/{id}')->to(\App\Http\Controllers\App\API\AppNotificationController::class, 'destroy');

    // google
    Route::get('integration/google/oauth/auth-code')->to(App\Http\Controllers\App\API\Integrations\Google\OAuthController::class, 'oauthAuthCode')
        ->name('google.oauth.auth-code');
    Route::get('integration/google/oauth/disconnect')->to(App\Http\Controllers\App\API\Integrations\Google\OAuthController::class, 'disconnect')
        ->name('google.oauth.disconnect');
    Route::get('integration/google/calendar/connect')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'connect')
        ->name('google.calendar.connect');
    Route::get('integration/google/calendar')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'all');
    Route::group(function () {
        Route::get('calendar/list-available')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'availableCalendars');
        Route::post('calendar')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'addCalendar');
        Route::put('calendar/sync')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'syncAll');
        Route::get('calendar/{id}')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'retrieve')
            ->bind('id', '[a-f0-9\-]{36}');
        Route::put('calendar/{id}/sync')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'sync')
            ->bind('id', '[a-f0-9\-]{36}');
        Route::delete('calendar/{id}')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'remove')
            ->bind('id', '[a-f0-9\-]{36}');
        Route::get('calendar/disconnect')->to(App\Http\Controllers\App\API\Integrations\Google\CalendarController::class, 'disconnect')
            ->name('google.calendar.disconnect');
    })->prefix('integration/google/')->middleware('google-calendar');

    // quickbooks
    Route::get('integration/quickbooks/oauth/connect')->to(App\Http\Controllers\App\API\Integrations\Quickbooks\OAuthController::class, 'connect')
        ->name('quickbooks.oauth.connect');
    Route::get('integration/quickbooks/oauth/auth-code')->to(App\Http\Controllers\App\API\Integrations\Quickbooks\OAuthController::class, 'oauthAuthCode')
        ->name('quickbooks.oauth.auth-code');
    Route::get('integration/quickbooks/oauth/external-disconnect')->to(App\Http\Controllers\App\API\Integrations\Quickbooks\OAuthController::class, 'externalDisconnect');
    Route::get('integration/quickbooks/oauth/disconnect')->to(App\Http\Controllers\App\API\Integrations\Quickbooks\OAuthController::class, 'disconnect')
        ->name('quickbooks.oauth.disconnect');
    Route::get('integration/quickbooks/settings')->to(App\Http\Controllers\App\API\Integrations\QuickbooksController::class, 'settings');
    Route::get('integration/quickbooks/customers')->to(App\Http\Controllers\App\API\Integrations\QuickbooksController::class, 'customers');
    Route::get('integration/quickbooks/invoices')->to(App\Http\Controllers\App\API\Integrations\QuickbooksController::class, 'invoices');

    Route::post('projects')->to(App\Http\Controllers\App\API\ProjectController::class, 'add');
    Route::get('project/info')->to(App\Http\Controllers\App\API\ProjectController::class, 'info');
    Route::post('project/events')->to(App\Http\Controllers\App\API\Project\EventController::class, 'add');
    Route::put('project/events')->to(App\Http\Controllers\App\API\Project\EventController::class, 'reschedule');
    Route::put('project/event/calendar-push')->to(App\Http\Controllers\App\API\Project\EventController::class, 'calendarPush');
    Route::put('project/event/resend-notifications')->to(App\Http\Controllers\App\API\Project\EventController::class, 'resendNotifications');
    Route::put('project/event/cancel')->to(App\Http\Controllers\App\API\Project\EventController::class, 'cancel');
    Route::put('project/event/complete')->to(App\Http\Controllers\App\API\Project\EventController::class, 'complete');
    Route::delete('project/event')->to(App\Http\Controllers\App\API\Project\EventController::class, 'active');
    Route::patch('project/event')->to(App\Http\Controllers\App\API\Project\EventController::class, 'edit');
    Route::post('properties')->to(App\Http\Controllers\App\API\PropertyController::class, 'add');
    Route::post('properties/move')->to(App\Http\Controllers\App\API\PropertyController::class, 'move');
    Route::post('service/email-validation')->to(App\Http\Controllers\App\API\ServiceController::class, 'emailValidation');
    Route::post('service/image-download-save')->to(App\Http\Controllers\App\API\ServiceController::class, 'imageDownloadSave');
    Route::post('service/stylesheet-scraper')->to(App\Http\Controllers\App\API\ServiceController::class, 'stylesheetScraper');
    Route::post('service/website-scraper')->to(App\Http\Controllers\App\API\ServiceController::class, 'websiteScraper');
    Route::get('setup-wizard/all')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'getAll');
    Route::put('setup-wizard/general')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'saveGeneral');
    Route::put('setup-wizard/additional-services')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'saveAdditionalServicesData');
    Route::put('setup-wizard/quickbooks-online')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'saveQuickbooksOnlineSettings');
    Route::put('setup-wizard/complete')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'completeSetupWizard');
    Route::put('setup-wizard/next')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'saveStep');
    Route::put('setup-wizard/skip')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'skipStep');
    Route::put('setup-wizard/company-step')->to(App\Http\Controllers\App\API\SetupWizardController::class, 'saveCompanyStep');
    Route::put('signup/address')->to(\App\Http\Controllers\App\API\SignupController::class, 'saveAddress');
    Route::put('signup/intake')->to(\App\Http\Controllers\App\API\SignupController::class, 'saveIntake');
    Route::put('signup/subscription')->to(\App\Http\Controllers\App\API\SignupController::class, 'saveSubscription');
    Route::get('training/content')->to(\App\Http\Controllers\App\API\TrainingController::class, 'content');
    Route::get('training/completed-modules')->to(\App\Http\Controllers\App\API\TrainingController::class, 'completedModules');
    Route::get('training/module/{id}')->to(\App\Http\Controllers\App\API\TrainingController::class, 'module')
        ->bind('id', '[0-9]+');
    Route::put('training/enable-user/{id}')->to(\App\Http\Controllers\App\API\TrainingController::class, 'enableUser')
        ->bind('id', '[0-9]+');
    Route::put('training/enable-company/{id}')->to(\App\Http\Controllers\App\API\TrainingController::class, 'enableCompanyUsers')
        ->bind('id', '[0-9]+');
    Route::get('training/complete')->to(\App\Http\Controllers\App\API\TrainingController::class, 'complete');

    // Wisetack
    Route::get('integration/wisetack/merchant-by-company')->to(WisetackController::class, 'getMerchantByCompany');
    Route::post('integration/wisetack/merchant')->to(WisetackController::class, 'createMerchant');
    Route::delete('integration/wisetack/merchant')->to(WisetackController::class, 'disconnectMerchant');

    Route::get('integration/wisetack/promo')->to(WisetackController::class, 'generatePromotionalMessaging');

    Route::get('integration/wisetack/merchant/{id}/prequalification-by-project-id/{project_id}')
        ->to(WisetackController::class, 'getPrequalificationsByProjectId')
        ->bind('id', '[A-F0-9\-]{32}');

    Route::post('integration/wisetack/merchant/{id}/prequalification')
        ->to(WisetackController::class, 'createPrequalification')
        ->bind('id', '[A-F0-9\-]{32}');


    Route::get('integration/wisetack/transaction-bid/{id}/initiate')->to(WisetackController::class, 'initiateTransaction')
        ->bind('id', '[A-F0-9\-]{32}');

    Route::get('integration/wisetack/transaction-bid/{id}')->to(WisetackController::class, 'getTransactionByBidItemId')
        ->bind('id', '[A-F0-9]{32}');

    Route::get('integration/wisetack/report')->to(WisetackController::class, 'getMetricsReport')
        ->bind('id', '[A-F0-9\-]{32}');


})->middlewareGroup('app-api')->prefix('api/');

Route::get('api/integration/wisetack/prequal-project/{id}/redirect')->to(WisetackController::class, 'redirectPrequalificationByProjectUuid')
    ->bind('id', '[A-Fa-f0-9\-]{32,36}')
    ->name('wisetack.prequals.project.redirect');

Route::get('api/integration/wisetack/transaction-bid/{id}/redirect')->to(WisetackController::class, 'redirectTransactionByBidItemId')
    ->bind('id', '[A-Fa-f0-9\-]{32,36}')
    ->name('wisetack.transactions.bid.redirect');


Route::post('api/service/password-strength')->to(App\Http\Controllers\App\API\ServiceController::class, 'passwordStrength');

// public routes
Route::group(function () {
    Route::post('service/url-validation')->to(App\Http\Controllers\App\API\ServiceController::class, 'urlValidation');
    Route::get('user/password-resets/{id}')->to(App\Http\Controllers\App\API\User\PasswordResetController::class, 'retrieve')
        ->bind('id', '[a-f0-9\-]{36}');
    Route::put('user/password-resets/{id}/complete')->to(App\Http\Controllers\App\API\User\PasswordResetController::class, 'complete')
        ->bind('id', '[a-f0-9\-]{36}');
})->prefix('api/');

// route older api without middleware
Route::any('api/{path}')->to(App\Http\Controllers\MainController::class, 'page')->bind('path', '.*')
    ->middleware('domain');

