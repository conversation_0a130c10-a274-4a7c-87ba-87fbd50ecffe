<?php

use App\Classes\Acl;
use App\Classes\Log;
use App\Resources\Bid\ItemResource;
use App\Resources\CompanyResource;
use App\Resources\ProjectResource;
use App\Services\CompanyFeatureService;
use App\Services\CompanySettingService;
use App\Services\TimeService;
use App\Services\Wisetack\Exceptions\EntityNotFoundException;
use App\Services\WisetackService;
use Common\Models\BidItem;
use Common\Models\BidItemContent;
use Common\Models\Company;
use Common\Models\Feature;
use Common\Models\WisetackMerchant;
use Core\Components\Http\StaticAccessors\URI;
use Ramsey\Uuid\Uuid;

if (empty($_GET['id'])) {
    throw new \Core\Components\Http\Exceptions\HttpResponseException(404);
}

$bidID = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

/** @var TimeService $time_service */
$time_service = new TimeService();

$currentDate = date('n/j/Y');
$viewBidButtons = NULL;
$drawingDisplay = NULL;
$firstName = null;
$lastName = null;
$logoUrl = null;
$logoImage = null;
$companyColor = null;
$companyColorHover = null;
$project_evaluated_by_name = null;
$project_evaluated_by_phone = null;
$project_evaluated_by_email = null;
$wisetack_prequal_section = null;
$wisetack_promo_section = null;
$name_display = null;
$unavailable_bid_content = null;

include_once(__DIR__ . '/includes/classes/class_FindEvaluation.php');

$object = new Bid();
$object->setBid($bidID);
$object->getEvaluation();
$bidArray = $object->getResults();
if ($bidArray === null) {
    throw new \Core\Components\Http\Exceptions\HttpResponseException(404);
}

//Find Evaluation
$evaluationID = $bidArray['evaluationID'];
$bidNumber = $bidArray['bidNumber'];
$companyID = $bidArray['companyID'];
$customEvaluation = $bidArray['customEvaluation'];
$bidItemID = $bidArray['bidItemID'];

if (empty($evaluationID)) {
    $viewBidContentDisplay = '
        <div class="row">
            <div class="medium-12 columns">
                <p class="text-center" style="margin-top:2rem;">
                    <strong>Bid Does Not Exist</strong>
                </p>
            </div>
        </div>';
} else {
    $company = Company::query()->select([
        'companyUUID',
        'companyID', 'name', 'address', 'address2', 'city', 'state', 'zip', 'website', 'logoFileID', 'color',
        'colorHover', 'timezoneID'
    ])->where('companyID', $companyID)->first();

    //Company
    $companyID = $company['companyID'];
    $companyName = $company['name'];
    $companyAddress1 = $company['address'];
    $companyAddress2 = $company['address2'];
    $companyCity = $company['city'];
    $companyState = $company['state'];
    $companyZip = $company['zip'];
    $companyWebsite = $company['website'];
    $logoFileID = $company['logoFileID'];
    $companyColor = $company['color'];
    $companyColorHover = $company['colorHover'];

    $time_service->setTimezone($company->_timezone->timezone);

    if (!empty($logoFileID)) {
        $companyResource = CompanyResource::make(Acl::make());
        $companyResource->setMediaCompanyID($companyID);
        $logoUrl = $companyResource->getMedia()->get('logo')->getVariant('document_thumbnail')->getUrl($companyID)->csm()->build();
        $logoImage = '<img src="' . $logoUrl . '" style="margin: 1rem 0;">';
    }

    if ($companyColor == '') {
        $companyColor = "#005ad0";
    }

    if ($companyColorHover == '') {
        $companyColorHover = "#006EFF";
    }

    list($r1, $g1, $b1) = sscanf($companyColor, "#%02x%02x%02x");
    $company_color_button_color = '#ffffff';
    if (($r1 * 0.299 + $g1 * 0.587 + $b1 * 0.114) > 186) {
        $company_color_button_color = '#000000';
    }

    list($r2, $g2, $b2) = sscanf($companyColorHover, "#%02x%02x%02x");
    $company_color_hover_button_color = '#ffffff';
    if (($r2 * 0.299 + $g2 * 0.587 + $b2 * 0.114) > 186) {
        $company_color_hover_button_color = '#000000';
    }

    if ($companyAddress2 == '') {
        $companyAddressBlock = '
                            ' . $companyAddress1 . '<br/>
                            ' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';
    }
    else {
        $companyAddressBlock = '
                            ' . $companyAddress1 . '<br/>
                            ' . $companyAddress2 . '<br/>
                            ' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';
    }

    //Phone
    include_once(__DIR__ . '/includes/classes/class_CompanyPhone.php');

    $object = new CompanyPhone();
    $object->setCompany($companyID);
    $object->getPhone();
    $phoneArray = $object->getResults();
    $companyPhoneDisplay = '';
    if (count($phoneArray) > 0) {
        foreach ($phoneArray as &$row) {
            $phoneNumber = $row['phoneNumber'];
            $phoneDescription = $row['phoneDescription'];

            $companyPhoneDisplay .= '
                                ' . $phoneDescription . ' ' . $phoneNumber . ' | ';
        }
        $companyPhoneDisplay = rtrim($companyPhoneDisplay, ' | ');
    }

    include_once(__DIR__ . '/includes/classes/class_EvaluationProject.php');

    $object = new EvaluationProject();
    $object->setEvaluation($evaluationID, $companyID, ($bidItemID !== null ? $bidItemID : $customEvaluation));
    $object->getEvaluation();
    $projectArray = $object->getResults();

    //Project
    $projectID = $projectArray['projectID'];
    $projectReferenceID = $projectArray['projectReferenceID'];
    $propertyID = $projectArray['propertyID'];
    $customerID = $projectArray['customerID'];
    $firstName = $projectArray['firstName'];
    $lastName = $projectArray['lastName'];
    $businessName = $projectArray['businessName'];
    $address = $projectArray['address'];
    $address2 = $projectArray['address2'];
    $city = $projectArray['city'];
    $state = $projectArray['state'];
    $zip = $projectArray['zip'];
    $ownerAddress = $projectArray['ownerAddress'];
    $ownerAddress2 = $projectArray['ownerAddress2'];
    $ownerCity = $projectArray['ownerCity'];
    $ownerState = $projectArray['ownerState'];
    $ownerZip = $projectArray['ownerZip'];
    $email = $projectArray['email'];
    $projectDescription = $projectArray['projectDescription'];
    $projectCancelled = $projectArray['projectCancelled'];
    $projectCompleted = $projectArray['projectCompleted'];
    $evaluationDescription = $projectArray['evaluationDescription'];
    $evaluationCreated = $projectArray['evaluationCreated'];
    $createdFirstName = $projectArray['createdFirstName'];
    $createdLastName = $projectArray['createdLastName'];
    $createdEmail = $projectArray['createdEmail'];
    $createdPhone = $projectArray['createdPhone'];
    $bidAccepted = $projectArray['bidAccepted'];
    $bidRejected = $projectArray['bidRejected'];
    $evaluationCancelled = $projectArray['evaluationCancelled'];
    $contractID = $projectArray['contractID'];

    $salesID = $projectArray['projectSalesperson'];
    $salesFirstName = $projectArray['salesFirstName'];
    $salesLastName = $projectArray['salesLastName'];
    $salesPhone = $projectArray['salesPhone'];
    $salesEmail = $projectArray['salesEmail'];

    $evaluationCreated = $time_service->getFromUtc($evaluationCreated)->format('n/j/Y');

    $project_evaluated_by_name = $createdFirstName . ' ' . $createdLastName;
    $project_evaluated_by_phone = $createdPhone;
    $project_evaluated_by_email = $createdEmail;
    if ($salesID !== null) {
        $project_evaluated_by_name = $salesFirstName . ' ' . $salesLastName;
        $project_evaluated_by_phone = $salesPhone;
        $project_evaluated_by_email = $salesEmail;
    }

    $inlineAddress = $address . ' ' . $address2 . ', ' . $city . ', ' . $state . ' ' . $zip;

    //Address Display
    $addressDisplay = '
		        	' . $address . ' ' . $address2 . '<br/>
					' . $city . ', ' . $state . ' ' . $zip . '';


    if (!$bidItemID) {
        throw new EntityNotFoundException('No bid item ID passed', 404);
    }

    $bid = BidItem::findByUuid($bidItemID);

    if (!$bid) {
        throw new EntityNotFoundException('Bid not found', 404);
    }

    $bid_content = BidItemContent::where('bidItemID', $bid->bidItemID)->where('type', BidItemContent::TYPE_ACKNOWLEDGEMENT)->orderBy('order', 'asc')->get();
    $bid_content_display = '';
    foreach ($bid_content as $content) {
        $content_key = str_replace(" ", "_", strtolower($content['name'])) . '_' . $content['order'];
        $required = $content['isAnswerRequired'] ? 'required' : '';
        $bid_content_display .= "<label>".
                        "<input {$required} class=\"acknowledgement-content\" type=\"checkbox\" name=\"{$content_key}\"/>".$content['content'].
                       "<small class=\"form-error\">Acknowledgement is required</small>".
                    "</label>";
    }
    if ($bid_content_display !== '') {
        $bid_content_display = "<div class=\"medium-12 columns bid-content-acknowledgements\">{$bid_content_display}</div>";
    }

    include_once(__DIR__ . '/includes/classes/class_Drawing.php');

    $object = new Drawing();
    $object->setDrawing($evaluationID);
    $object->getDrawing();
    $drawing = $object->getResults();

    if (!empty($drawing)) {
        //$evaluationDrawing = $drawing['evaluationDrawing'];

        //Drawing Display
        $drawingDisplay = '<a class="button" target="_blank" href="open-image.php?bid=' . $bidID . '" download>Download Evaluation Drawing</a><br/><br/>';

    }

    if ($bidRejected == NULL && $bidAccepted == NULL) {
        if ($contractID) {
            $viewBidButtons = '
                <button class="button" id="view-contract">View &amp; Accept Contract</button>
                <button class="button" id="reject">Reject</button>
                <br/>';
        } else {
            $viewBidButtons = '
                <button class="button" id="accept">Accept Bid</button>
                <button class="button" id="reject">Reject</button>
                <br/>';
        }
    } else if ($bidAccepted != NULL) {
        $bidAccepted = $time_service->getFromUtc($bidAccepted)->format('n/j/Y g:i a');
        $viewBidButtons = '<span>Bid Accepted ' . $bidAccepted . '</span>';

    } else if ($bidRejected != NULL) {
        $bidRejected = $time_service->getFromUtc($bidRejected)->format('n/j/Y g:i a');
        if ($contractID) {
            $viewBidButtons = '
            <button class="button" id="view-contract">View &amp; Accept Contract</button>
            <br/>
            <span>Bid Rejected ' . $bidRejected . '</span>';
        } else {
            $viewBidButtons = '
            <button class="button" id="accept">Accept Bid</button>
            <br/>
            <span>Bid Rejected ' . $bidRejected . '</span>';
        }

    }

    $signContractInline = '';
    $signContractModal = '';
    if ($contractID) {
        $signContractModal =
            '<div id="contractModalAccept" class="reveal small" data-reveal data-close-on-esc="false" data-close-on-click="false">
                <div class="row">
                    By signing any forms or agreements provided to you by ' . $companyName . ', you understand, agree and acknowledge that your electronic signature is the legally binding equivalent to your 
                    handwritten signature.  You agree, by providing your electronic signature, that you will not repudiate, deny or challenge the validity of your electronic signature or of any 
                    electronic agreement that you electronically sign or their legally binding effect.  
                </div>
                <div class="row" style="margin-top:1rem;">
                    <div class="medium-12 columns text-center">
                        <button class="button" id="signContractAccept">Agree</button> 
                        <button data-close class="button secondary">Cancel</button>
                    </div>
                </div>
            </div>';
    } else {
        $signContractInline =
            '<div class="row sign-contract">
                <div class="medium-12 columns">
                    <p>By signing any forms or agreements provided to you by ' . $companyName . ', you understand, agree and acknowledge that your electronic signature is the legally binding equivalent to your 
                    handwritten signature.  You agree, by providing your electronic signature, that you will not repudiate, deny or challenge the validity of your electronic signature or of any 
                    electronic agreement that you electronically sign or their legally binding effect.</p>
                </div>
                '.$bid_content_display.'
                <div class="medium-8 columns">
                    <label>Signature <small>required</small>
                        <input type="text" name="signature"/>
                        <small class="form-error">Signature is required.</small>
                    </label>
                </div>
                <div class="medium-6 columns">
                    <button class="button" id="signContractAccept">Sign</button> 
                </div>
            </div>';
    }

    //Phone
    include_once(__DIR__ . '/includes/classes/class_CustomerPhone.php');

    $object = new CustomerPhone();
    $object->setCustomer($customerID);
    $object->getPhone();
    $phoneArray = $object->getResults();


    foreach ($phoneArray as &$row) {
        $phoneNumber = $row['phoneNumber'];
        $phoneDescription = $row['phoneDescription'];
        $isPrimary = $row['isPrimary'];

        if ($isPrimary == '1') {
            $phoneDisplay = '' . $phoneNumber . '';
        }
    }

    $contract = null;
    if (!empty($contractID)) {
        include_once(__DIR__ . '/includes/classes/class_GetContract.php');

        $object = new Contract();
        $object->setCompany($companyID, $contractID);
        $object->getContract();
        $contractArray = $object->getResults();

        if ($contractArray != NULL) {

            $companyContract = $contractArray['contractText'];

            $tags = array("{date}", "{firstName}", "{lastName}", "{businessName}", "{address}", "{address2}", "{city}", "{state}", "{zip}", "{phone}", "{email}", "{bidNumber}");
            $variables = array($currentDate, $firstName, $lastName, $businessName, $address, $address2, $city, $state, $zip, $phoneDisplay, $email);

            if ($bidNumber != NULL) {
                $variables[] = '#' . $bidNumber;
            } else {
                $variables[] = '';
            }

            $companyContractText = str_replace($tags, $variables, $companyContract);

            $companyContractText = html_entity_decode($companyContractText);


            $contract = '
                <div class="medium-12 columns">
                    <p class="no-margin" style="text-align:center;font-size:1.5rem;font-weight: bold;line-height:1;">Contract</p>
                    <p class="no-margin" style="text-align:center;font-size:2.5rem;font-weight: bold;">
                        ' . $companyName . '
                    </p>
                    <p style="text-align:center;font-size:12px;line-height: 1.2;">
                        ' . $companyAddress1 . ', ' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>
                        ' . $companyPhoneDisplay . '<br/>
                        ' . $companyWebsite . '<br/>
                    </p>
                </div>
                <div>
                    <div style="display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;">Customer Name:</p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 30rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left:10px;">
                            ' . ($businessName != '' ? $businessName : $firstName . ' ' . $lastName) . '
                        </p>
                    </div>

                    <div style="display:inline-block;">
                        <p style="display:inline-block;text-align:right;margin-bottom:0;">
                            Date:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 7rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left: 10px;text-align:center;">
                            ' . $currentDate . '
                        </p>
                    </div>
                </div>
                <div>
                    <div style="display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;">
                            Located:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 44rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left:10px;">
                            ' . $address . ' ' . $address2 . ', ' . $city . ', ' . $state . ' ' . $zip . '
                        </p>
                    </div>
                </div>
                 <div style="margin-bottom: 1rem;">
                    <div style="display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;">
                            Phone:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 20rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left:10px;">
                            ' . $phoneDisplay . '
                        </p>
                    </div>

                    <div style="display:inline-block;">
                        <p style="display:inline-block;text-align:right;margin-bottom:0;">
                            Email:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 22rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left:10px;">
                            ' . $email . '
                        </p>
                    </div>
                </div>
                <p>
                ' . $companyContractText . '
                <div>
                    <div style="display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;">
                            Signature:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 30rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;padding-left:10px">
                            ' . $firstName . ' ' . $lastName . '
                        </p>
                    </div>

                    <div style="display:inline-block;">
                        <p style="display:inline-block;text-align:right;margin-bottom:0;">
                            Date:
                        </p>
                    </div>
                    <div style="border-bottom:1px solid #000000;width: 7rem;display:inline-block;">
                        <p style="display:inline-block;margin-bottom:0;text-align:center;padding-left:10px">
                            ' . $currentDate . '
                        </p>
                    </div>
                </div>
            ';
        }
    }

    // Wisetack section
    $logger = Log::create('wisetack', [
        'email' => [
            'enabled' => false
        ],
        'slack' => [
            'enabled' => false
        ],
        'file' => 'wisetack.log',
    ]);

    try {

        function shouldShowWisetackSection($company_id, $merchant, $bid) {
            $company_feature = new CompanyFeatureService($company_id);
            $is_wisetack_enabled = $company_feature->has(Feature::WISETACK_API, true);

            $setting_service = new CompanySettingService($company_id);
            $is_financing_required_for_all_projects = $setting_service->get('wisetack_financing_required_for_all_projects', false);

            $project = $bid->project()->first();

            if ($project->status === ProjectResource::STATUS_CANCELLED || $project->status === ProjectResource::STATUS_CLOSED) {
                return false;
            }

            if ($bid->status === BidItem::STATUS_CANCELLED) {
                return false;
            }

            $is_financing_enabled_for_project = $project->isFinancingEnabled;

            if (!$is_wisetack_enabled || !$merchant || !$merchant->isApproved()) {
                return false;
            }

            if ($is_financing_required_for_all_projects) {
                return true;
            }

            return !!$is_financing_enabled_for_project;
        };

        $merchant = WisetackMerchant::where('companyUUID', $company['companyUUID'])->whereNull('deletedAt')->first();

        if (shouldShowWisetackSection($companyID, $merchant, $bid)) {
            $merchant_id = strval(UUID::fromBytes($merchant->wisetackMerchantID)) ?? null;

            $amount = $bid->total;
            $service = new WisetackService(null);

            // Prequal section
            try {
                $prequals = $service->getPrequalificationsByProjectId($merchant_id, $bid->projectID);
                $prequalified_link = $prequals->firstWhere('status', "PREQUALIFIED");
                if ($prequalified_link) {
                    $formattedAmount = number_format($prequalified_link->maxQualifiedAmount, 2, '.', ',');
                    $wisetack_prequal_section = <<<HTML
                    <div class="row wt-flex-wrapper-prequal">
                        Pre-qualified on Wisetack for $$formattedAmount
                    </div>
                    HTML;
                }
            } catch (Exception $e) {
                $logger->error('Failed to fetch prequalification.', [
                    'message' => $e->getMessage(),
                    'bidId' => $bidItemID,
                    'merchantId' => $merchant_id,
                    'projectId' => $projectID
                ]);

                \Sentry\withScope(function (\Sentry\State\Scope $scope) use ($e, $bidItemID, $merchant_id, $projectID) {
                    $scope->setTag('bid_id', $bidItemID ?? 'none');
                    $scope->setTag('merchant_id', $merchant_id ?? 'none');
                    $scope->setTag('project_id', $projectID ?? 'none');
                    $scope->setExtra('exception_message', $e->getMessage());
                    \Sentry\captureException($e);
                });
            }

            //Promo messaging section
            $promo = $service->generatePromotionalMessaging($amount, $merchant_id);
            $as_low_as_text = "Or as low as <strong>$$promo[monthlyPayment]/mo.</strong>";
            $url = $service->buildPromotionalLearnMoreLink($promo, $amount);
            $learn_more_link = !empty($url) ? "<a href='$url' target='_blank'>Learn more</a>" : "";

            $app_source = "appSource=customer-portal";
            $payment_link = URI::route('wisetack.transactions.bid.redirect', ['id' => $bidItemID])->build() . "?" . $app_source;

            $wisetack_promo_section = <<<HTML
                <div class="row wt-flex-wrapper-block">
                    <div class="wt-flex-wrapper">
                        <div>
                            <div class="wt-promo-title">{$as_low_as_text} {$learn_more_link}</div>
                            <div class="wt-promo-description">Pay over time with <strong>Wisetack</strong>*</div>
                        </div>
                        <div><a href="{$payment_link}" target="_blank"><button class="button wt-primary-button">See Financing Options</button></a></div>
                    </div>
                    <div class="wt-footnote">
                        *All financing is subject to credit approval. Your terms may vary. Payment options through
                        Wisetack are provided by our <a target="_blank" href="https://wisetack.com/lenders">lending partners</a>. 
                        For example, a $1,200 purchase could cost $104.89 a month for 12 months, based on an 8.9% APR, or $400 a 
                        month for 3 months, based on a 0% APR. Offers range  from 0-35.9% APR based on creditworthiness. No other 
                        financing charges or participation fees. See additional terms at 
                        <a target="_blank" href="https://wisetack.com/faqs">https://wisetack.com/faqs</a>
                    </div>
                </div>
                HTML;
        }
    } catch (Exception $e) {

        $logger->error('Failed to build wisetack bid information.', [
            'message' => $e->getMessage(),
            'bidId' => $bidItemID,
            'companyID' => $companyID,
        ]);

        \Sentry\withScope(function (\Sentry\State\Scope $scope) use ($e, $bidItemID, $companyID) {
            $scope->setTag('bid_id', $bidItemID ?? 'none');
            $scope->setTag('company_id', $companyID ?? 'none');
            $scope->setExtra('exception_message', $e->getMessage());
            \Sentry\captureException($e);
        });

    }

    $name_display = $businessName != '' ? $firstName . ' ' . $lastName . ' with ' .$businessName : $firstName . ' ' . $lastName;

    $unavailable_bid_content =  '<a target="_blank" href="mailto:'.$project_evaluated_by_email.'?subject=Request%20New%20Quote&body=I%20would%20like%20to%20request%20a%20new%20quote%20for%20the%20project%20bid%20on%20'.$evaluationCreated.'.%20Please%20see%20project%20information%20below.%0A%0ACustomer%3A%20'.$name_display.'%0AAddress%3A%20' . $inlineAddress . '%0AProject%20Reference%20%23%3A%20'.$projectReferenceID.'%0ABid%20Name%3A%20' . $evaluationDescription . '%0A">Click here</a> to request a new quote or contact the salesperson below.
            <br/><br/>
		    <strong>'.$project_evaluated_by_name.'<br/></strong>
		    '.$companyName.'<br/>
		    '.$project_evaluated_by_phone.'<br/>
	        <a href="mailto:'.$project_evaluated_by_email.'">'.$project_evaluated_by_email.'</a>';

    if ($projectCompleted !== null) {
        $viewBidDisplay =
            'This bid is unavailable because the project is closed. ' . $unavailable_bid_content;
    } else if ($evaluationCancelled == NULL) {
        $url = $bidArray['bidFileID'] !== null ? ItemResource::make(Acl::make()->setCompanyID((int) $companyID))->getMedia()->get('file')->getOriginal()->getUrl($bidArray['bidFileID'])->csm()->build() : 'bid-accept-email.php?id=' . $bidID;
        $viewBidDisplay = '
					<p>
		            	<strong>Customer</strong><br/>
		            	' . $name_display . '
		            	<br/><br/>
		           		<strong>Project</strong><br/>
		            	' . $evaluationDescription . '
		            	<br/><br/>
		            	<strong>Address</strong><br/>
		            	' . $addressDisplay . '
		            	<br/><br/>
		            	<strong>Evaluation Performed on ' . $evaluationCreated . ' by ' . $project_evaluated_by_name . '</strong>
		            	<br/><br/>
		            	Thank you for giving us the opportunity to serve you.  If you have any questions, including questions about our evaluation of your property, the work to be performed, or the bid, please feel free to call me.
		            	<br/><br/>
		             	' . $project_evaluated_by_name . '<br/>
		            	' . $companyName . '<br/>
		           	    ' . $project_evaluated_by_phone . '<br/>
		            	' . $project_evaluated_by_email . '
		           	<br/><br/>
                    <a class="button" target="_blank" href="' . $url . '">Download Bid</a><br/>
                    <br/>
		            ' . $drawingDisplay . '
		           </p>
		           <p id="viewBidButtons">
		           	' . $viewBidButtons . '
					</p>
					';

    } else {
        $viewBidDisplay = 'This bid has been cancelled. ' . $unavailable_bid_content;
    }

    $viewBidContentDisplay = '
				<div class="row">
			     	<div class="medium-6 columns">
			     	    '.$logoImage.'
			  		</div>
			      	<div class="medium-6 columns">
			        	<p class="text-right" style="margin-top:2rem;">
			            	' . $companyAddressBlock . '
			          	</p>
			      	</div>
                </div>
                <div class="row">
			     	<div class="medium-12 columns">
			        	' . $viewBidDisplay . '
			  		</div>
			   	</div>
			   	'.$signContractInline.'
			    <div id="contractModal" class="reveal large" data-reveal data-close-on-esc="false" data-close-on-click="false">
			       	<div class="row">
			       		' . $contract . '
			      	</div>
			    	<div class="row" style="margin-top:1rem;">
			        	<div class="medium-12 columns text-center">
			      			<button class="button" id="signContract">Sign Contract</button> 
			        		<button data-close class="button secondary">Cancel</button>
			          	</div>
			   		</div>
				</div>
				'.$signContractModal.'
			    <div id="rejectBidModal" class="reveal tiny" data-reveal data-close-on-esc="false" data-close-on-click="false">
			 		<div class="row align-center">
			       		<p style="margin-top: 1rem;text-align: center;">Are you sure you want to reject this bid?</p>
			      	</div>
			     	<div class="row">
			       		<div class="medium-12 columns text-center">
			          		<button id="rejectBid" class="button">Ok</button>
			         		<button data-close class="button secondary">Cancel</button> 
			       		</div>
					</div>
				</div>
				
                ' . $wisetack_prequal_section .'
                ' . $wisetack_promo_section .'
			    <div id="evaluationID" class="is-hidden">' . $evaluationID . '</div>
			    <div id="customEvaluation" class="is-hidden">' . $customEvaluation . '</div>
			    <div id="bidItemID" class="is-hidden">' . $bidItemID . '</div>
				';
}


?>
