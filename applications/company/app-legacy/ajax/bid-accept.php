<?php

	if(isset($_POST['evaluationID'])) {
		 $evaluationID = filter_input(INPUT_POST, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
	}
	
	if(isset($_POST['customEvaluation'])) {
		 $customEvaluation = filter_input(INPUT_POST, 'customEvaluation', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	$bidItemID = isset($_POST['bidItemID']) ? $_POST['bidItemID'] : '';
	
	if(isset($_POST['bidID'])) {
		 $bidID = filter_input(INPUT_POST, 'bidID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if(isset($_POST['bidAcceptedName'])) {
		 $bidAcceptedName = filter_input(INPUT_POST, 'bidAcceptedName', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

    if(isset($_POST['acknowledgementAnswers'])) {
        $acknowledgementAnswers = filter_input(INPUT_POST, 'acknowledgementAnswers', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
    }

	include_once(__DIR__ . '/../includes/classes/class_AcceptBid.php');
			
		$object = new Bid();
		$object->setBid($evaluationID, ($bidItemID !== '' ? $bidItemID : $customEvaluation), $bidAcceptedName, $acknowledgementAnswers);
		$object->sendBid();
		$results = $object->getResults();

		echo json_encode('true');
?>