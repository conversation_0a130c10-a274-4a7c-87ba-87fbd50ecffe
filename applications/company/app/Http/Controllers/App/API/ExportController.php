<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Services\{ExportService, TimeService};
use Carbon\Carbon;
use Common\Models\Export;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Core\StaticAccessors\App;

/**
 * Class ExportController
 *
 * @package App\Http\Controllers\App\API
 */
class ExportController
{
    /**
     * Export CSV file based on type, resource scope, and file name
     *
     * @param int $type
     * @param Scope $scope
     * @param string $file_name
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \App\Exceptions\TimeException
     * @throws \Core\Exceptions\AppException
     */
    protected function exportFile(int $type, Scope $scope, string $file_name): \Core\Components\Http\Responses\FileResponse
    {
        /** @var TimeService $time_service */
        $time_service = App::get(TimeService::class);

        $export_service = new ExportService($type, Auth::acl(), $scope);
        $file = $export_service->run();
        $file_name .= '_' . $time_service->get(Carbon::now('UTC'))->format('Ymd_his') . '.csv';

        return Response::file($file->getRealPath())->contentType('text/csv')->download($file_name)
            ->onStreamEnd(function () use ($export_service) {
                $export_service->deleteFile();
            });
    }

    /**
     * Export customer data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function customers(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_CUSTOMER, $request->getScope(), 'customers');
    }

    /**
     * Export product data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function products(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_PRODUCT, $request->getScope(), 'products');
    }

    /**
     * Export property data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function properties(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_PROPERTY, $request->getScope(), 'properties');
    }

    /**
     * Export project data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function projects(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_PROJECT, $request->getScope(), 'projects');
    }

    /**
     * Export bid line item data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function bidLineItems(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_BID_LINE_ITEMS, $request->getScope(), 'bid_line_items');
    }

    /**
     * Export task data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function tasks(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_TASK, $request->getScope(), 'tasks');
    }

    /**
     * Export lead data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function leads(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_LEAD, $request->getScope(), 'leads');
    }

    /**
     * Export material data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function materials(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_MATERIAL, $request->getScope(), 'materials');
    }

    /**
     * Export additional cost data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function additionalCosts(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_ADDITIONAL_COST, $request->getScope(), 'additional_costs');
    }


    /**
     * Export financing prequalification data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function financingPrequalifications(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_FINANCING_PREQUAL, $request->getScope(), 'financing_prequalifications');
    }

    /**
     * Export financing transaction data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function financingTransactions(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_FINANCING_TRANSACTIONS, $request->getScope(), 'financing_transactions');
    }


    /**
     * Export financing opportunities data to CSV
     *
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\FileResponse
     * @throws \Core\Exceptions\AppException
     */
    public function financingOpportunities(ResourceRequest $request): \Core\Components\Http\Responses\FileResponse
    {
        return $this->exportFile(Export::TYPE_FINANCING_OPPORTUNITIES, $request->getScope(), 'financing_opportunities');
    }
}
