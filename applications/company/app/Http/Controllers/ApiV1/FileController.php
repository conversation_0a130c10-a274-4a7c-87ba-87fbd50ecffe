<?php

namespace app\Http\Controllers\ApiV1;

use App\Resources\FileResource;
use App\Traits\Resource\Controller\PolyActionTrait;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Exception;

class FileController
{
    use PolyActionTrait;

    protected $resource = FileResource::class;

    public function modifyImage($id, ResourceRequest $request)
    {
        try {
            $entity = $request->entity();
            $path = $entity->get('image.tmp_name');
            // update raw file instead of passing it through the form upload item
            $file = $this->getResource()->partialUpdate(Entity::make([
                'id' => $id,
                'path' => $path
            ]))->run();
            return Response::create($file, 204);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
}
