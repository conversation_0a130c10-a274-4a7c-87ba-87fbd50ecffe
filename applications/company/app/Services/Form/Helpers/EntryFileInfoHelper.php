<?php

declare(strict_types=1);

namespace App\Services\Form\Helpers;

use App\Resources\FileResource;
use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Services\Form\Exceptions\FormException;
use Closure;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Interfaces\AclInterface;

/**
 * Class EntryFileInfoHelper
 *
 * @package App\Services\Form\Helpers
 */
class EntryFileInfoHelper
{
    /**
     * @var array List of files to fetch
     */
    protected array $files = [];

    /**
     * @var FieldFileResource|null Cache for resource
     */
    protected ?FieldFileResource $resource = null;

    /**
     * Set resource
     *
     * @param FieldFileResource $resource
     */
    public function setResource(FieldFileResource $resource): void
    {
        $this->resource = $resource;
    }

    /**
     * Get resource
     *
     * @return FieldFileResource
     * @throws FormException
     */
    public function getResource(): FieldFileResource
    {
        if ($this->resource === null) {
            throw new FormException('Resource not defined, load must be called first');
        }
        return $this->resource;
    }

    /**
     * Fetch file by id
     *
     * Closure will be called with file data as first param after it is fetched.
     *
     * @param string $id
     * @param Closure $closure
     */
    public function fetchFile(string $id, Closure $closure): void
    {
        $this->files[$id] = $closure;
    }

    /**
     * Build media urls for field
     *
     * @param string $id Form item entry group field file id
     * @return array
     * @throws FormException
     */
    public function buildMediaUrls(string $id)
    {
        $file_media = $this->getResource()->getMedia()->type('file');
        $urls = [];
        foreach ($file_media->getVersions() as $version) {
            $urls[$version->getName()] = $version->getUrl($id)->csm()->build();
        }
        return $urls;
    }

    /**
     * Fetch all requested files and call all closures to load data
     *
     * @param AclInterface $acl
     */
    public function load(AclInterface $acl): void
    {
        if (count($this->files) === 0) {
            return;
        }
        $this->setResource(FieldFileResource::make($acl));
        $scope = Scope::make()->fields(['id', 'name', 'description', 'extension', 'size'])
            ->filter('id', 'in', array_keys($this->files))
            ->sort('created_at', 'asc')
            ->disablePagination();
        FileResource::make($acl)->collection()->scope($scope)->run()->each(function ($file) {
            $this->files[$file->id]($file->toArray());
        });
    }
}
