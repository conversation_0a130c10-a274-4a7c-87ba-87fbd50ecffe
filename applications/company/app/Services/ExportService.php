<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\{Acl, Func};
use App\Resources\{AdditionalCostResource,
    Bid\Item\LineItemResource,
    CustomerResource,
    Financing\WisetackPrequalResource,
    Financing\WisetackTransactionResource,
    LeadResource,
    MaterialResource,
    Product\ItemResource,
    ProjectResource,
    PropertyResource,
    TaskResource};
use \App\Resources\Bid\ItemResource as BidItemResource;
use Carbon\Carbon;
use Common\Models\Export;
use Core\Components\Resource\Classes\{Field, Resource, Scope};
use Core\Exceptions\AppException;
use League\Csv\Writer;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class ExportService
 *
 * @package App\Services
 */
class ExportService
{
    /**
     * @var array Map of model constants to resource
     */
    protected $resources = [
        Export::TYPE_CUSTOMER => CustomerResource::class,
        Export::TYPE_PROPERTY => PropertyResource::class,
        Export::TYPE_PROJECT => ProjectResource::class,
        Export::TYPE_BID_LINE_ITEMS => LineItemResource::class,
        Export::TYPE_PRODUCT => ItemResource::class,
        Export::TYPE_TASK => TaskResource::class,
        Export::TYPE_LEAD => LeadResource::class,
        Export::TYPE_MATERIAL => MaterialResource::class,
        Export::TYPE_ADDITIONAL_COST => AdditionalCostResource::class,
        Export::TYPE_FINANCING_PREQUAL => WisetackPrequalResource::class,
        Export::TYPE_FINANCING_TRANSACTIONS => WisetackTransactionResource::class,
        Export::TYPE_FINANCING_OPPORTUNITIES => BidItemResource::class
    ];

    /**
     * @var int Type of export (see Export model)
     */
    protected $type;

    /**
     * @var Acl
     */
    protected $acl;

    /**
     * @var Scope
     */
    protected $scope;

    /**
     * @var null|Resource
     */
    protected $resource = null;

    /**
     * @var null|\SplFileObject
     */
    protected $file = null;

    /**
     * ExportService constructor
     *
     * @param int $type
     * @param Acl $acl
     * @param Scope $scope
     * @throws AppException
     */
    public function __construct(int $type, Acl $acl, Scope $scope)
    {
        if ($acl->user() === null) {
            throw new AppException('Anonymous exports are currently not allowed');
        }

        $this->type = $type;
        $this->acl = $acl;

        $scope->format('export-v1');
        // disable pagination on scope since that is undesirable for an export...
        $scope->disablePagination();
        $this->scope = $scope;
    }

    /**
     * Get resource instance for type using Acl
     *
     * @return Resource
     * @throws AppException
     */
    public function getResource(): Resource
    {
        if ($this->resource === null) {
            if (!isset($this->resources[$this->type])) {
                throw new AppException('Unable to find resource for type: %d', $this->type);
            }
            $this->resource = new $this->resources[$this->type]($this->acl);
        }
        return $this->resource;
    }

    /**
     * Run export and generate file
     *
     * @return \SplFileObject
     * @throws AppException
     */
    public function run(): \SplFileObject
    {
        $time = microtime(true);

        $export = Export::create([
            'exportID' => Uuid::uuid4()->getBytes(),
            'type' => $this->type,
            'scope' => $this->scope->toArray(),
            'createdAt' => Carbon::now('UTC'),
            'createdByUserID' => $this->acl->user()->getKey()
        ]);

        $request = $this->getResource()
            ->collection()
            ->scope($this->scope);

        $rows = $request->stream();

        $this->file = Func::createTempFile();

        $writer = Writer::createFromFileObject($this->file);

        $scope_builder = $request->getScopeBuilder();

        $headers = [];
        $dates = [];
        /** @var Field $field */
        foreach ($scope_builder->getEntityFields($scope_builder->getFormatFields()) as $field) {
            // Skip fields that are excluded from output
            if ($field->isInternalFilter()) {
                continue;
            }

            $headers[] = $field->getLabel();
            $type = $field->getType();
            if (in_array($type, [Field::TYPE_DATE, Field::TYPE_DATETIME, Field::TYPE_TIME])) {
                $dates[$field->getName()] = $type;
            }
        }
        $date_formats = [
            Field::TYPE_DATETIME => 'Y-m-d H:i:s',
            Field::TYPE_DATE => 'Y-m-d',
            Field::TYPE_TIME => 'H:i:s'
        ];

        try {
            $writer->insertOne($headers);
            $t = 0;
            foreach ($rows as $chunk) {
                foreach ($chunk as $row) {
                    $t++;
                    $row = $row->toArray();
                    foreach ($row as $key => &$value) {
                        if ($value === null) {
                            $value = '';
                            continue;
                        }
                        if (is_bool($value)) {
                            $value = $value ? 'Yes' : 'No';
                            continue;
                        }
                        if (isset($dates[$key])) {
                            $value = Carbon::parse($value)->format($date_formats[$dates[$key]]);
                        }
                    }
                    $writer->insertOne($row);
                }
            }
        } catch (Throwable $e) {
            throw (new AppException('Unable to write row %d to file', $t))->setLastException($e);
        }

        $export->fill([
            'totalRows' => $t,
            'time' => (int) round(((microtime(true) - $time) * 1000000) / 1000)
        ])->save();

        return $this->file;
    }

    /**
     * Delete associated file
     *
     * @throws AppException
     */
    public function deleteFile(): void
    {
        if ($this->file === null) {
            throw new AppException('No file defined to delete, must call run() first');
        }
        if (!unlink($this->file->getRealPath())) {
            throw new AppException('Unable to delete temp export file');
        }
    }
}
