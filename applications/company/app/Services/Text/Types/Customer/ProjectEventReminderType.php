<?php

namespace App\Services\Text\Types\Customer;

use App\Services\Text\Classes\Type;
use App\Services\Text\Exceptions\TypeException;
use App\Services\Text\Interfaces\MessageInterface;
use App\Services\Text\Numbers\CustomerNumber;
use App\Services\Text\Traits\Type\{CompanyTrait, NotificationTrait};
use Carbon\Carbon;
use Common\Models\{CustomerPhone, ProjectSchedule};
use Ramsey\Uuid\Uuid;

/**
 * Class ProjectEventReminderType
 *
 * @package App\Services\Text\Types\Customer
 */
class ProjectEventReminderType extends Type
{
    use CompanyTrait;
    use NotificationTrait;

    /**
     * Build message
     *
     * @param array $payload
     * @return MessageInterface
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload): MessageInterface
    {
        $this->findNotification($payload);

        $event = ProjectSchedule::query()
            ->select([
                'projectScheduleUUID', 'scheduleType', 'isAllDay', 'scheduledStart', 'scheduledEnd', 'user.userFirstName',
                'user.userLastName', 'customer.customerID', 'companies.companyID', 'companies.name as companyName',
                'companyPhones.phoneNumber as companyPhone'
            ])
            ->join('project', 'project.projectID', '=', 'projectSchedule.projectID')
            ->join('property', 'property.propertyID', '=', 'project.propertyID')
            ->join('customer', 'customer.customerID', '=', 'property.customerID')
            ->join('companies', 'companies.companyID', '=', 'customer.companyID')
            ->join('companyPhones', function ($join) {
                $join->on('companyPhones.companyID', '=', 'companies.companyID')
                    ->where('companyPhones.isPrimary', true);
            })
            ->join('user', 'user.userID', '=', 'projectSchedule.scheduledUserID')
            ->where('projectScheduleUUID', $this->getNotificationItemID())
            ->first();
        if ($event === null) {
            throw new TypeException('Unable to find event');
        }

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($event->projectScheduleUUID));
        $message->from($this->getFromMessageServiceNumberByCompanyID($event->companyID));

        $recipients = CustomerPhone::query()
            ->where('customerID', $event->customerID)
            ->where('type', CustomerPhone::TYPE_CELL)
            ->get();
        foreach ($recipients as $recipient) {
            $message->to((new CustomerNumber())->fromModel($recipient));
        }

        $scheduled_start = Carbon::parse($event->scheduledStart);
        $scheduled_end = Carbon::parse($event->scheduledEnd);
        $one_day = $scheduled_start->day === $scheduled_end->day && $scheduled_end->diff($scheduled_end)->days === 0;
        $time = $scheduled_start->format('n/j/y');
        if ($one_day && !$event->isAllDay) {
            $time .= " at {$scheduled_start->format('g:ia')}";
        } elseif (!$one_day) {
            $time .= " to {$scheduled_end->format('n/j/y')}";
        }

        switch ($event->scheduleType) {
            case ProjectSchedule::TYPE_EVALUATION:
                $body = "REMINDER: Your {$event->companyName} sales appointment is scheduled for {$time} with {$event->userFirstName} {$event->userLastName}. Contact {$event->companyPhone} with questions. DO NOT REPLY";
                break;
            case ProjectSchedule::TYPE_INSTALLATION:
                $body = "REMINDER: Your {$event->companyName} installation appointment is scheduled for {$time} with {$event->userFirstName} {$event->userLastName}. Contact {$event->companyPhone} with questions. DO NOT REPLY";
                break;
        }

        $message->body($body);

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
