<?php

declare(strict_types=1);

namespace App\Resources\Project;

use App\ResourceDelegates\Project\EventDelegate;
use App\ResourceJobs\Project\Event\CalendarPushJob;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Carbon\Carbon;
use Closure;
use Common\Models\ProjectSchedule;
use Core\Components\Resource\Classes\{BatchRequest, Entity, Resource, Scope};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, RequestFailedException};
use Exception;

/**
 * Class EventResource
 *
 * @package App\Resources\Project
 */
class EventResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    const STATUS_ACTIVE = 1;
    const STATUS_CANCELLED = 2;
    const STATUS_REPLACED = 3;
    const STATUS_COMPLETED = 4;

    const SOURCE_CUSTOMER_ADD = 1;
    const SOURCE_PROPERTY_ADD = 2;
    const SOURCE_PROJECT_ADD = 3;
    const SOURCE_PROJECT_RESCHEDULE = 4;
    const SOURCE_CALENDAR_RESCHEDULE = 5;

    const TYPE_EVALUATION = 1;
    const TYPE_INSTALLATION = 2;

    const PENDING_DELAY = 15; // minutes

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;

    /**
     * @var string Table name
     */
    protected $table = 'projectSchedule';

    /**
     * @var string Model class name
     */
    protected $model = ProjectSchedule::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Get available statuses
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [static::STATUS_ACTIVE, static::STATUS_CANCELLED, static::STATUS_REPLACED, static::STATUS_COMPLETED];
    }

    /**
     * Get available sources
     *
     * @return array
     */
    public static function getSources(): array
    {
        return [
            static::SOURCE_CUSTOMER_ADD, static::SOURCE_PROPERTY_ADD, static::SOURCE_PROJECT_ADD,
            static::SOURCE_PROJECT_RESCHEDULE, static::SOURCE_CALENDAR_RESCHEDULE
        ];
    }

    /**
     * Get available types
     *
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            static::TYPE_EVALUATION, static::TYPE_INSTALLATION
        ];
    }

    /**
     * Map of model types to resource types
     *
     * @return array
     */
    public static function getTypeMap(): array
    {
        return [
            ProjectSchedule::TYPE_EVALUATION => static::TYPE_EVALUATION,
            ProjectSchedule::TYPE_INSTALLATION => static::TYPE_INSTALLATION
        ];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(EventDelegate::class);
    }

    /**
     * Reschedule event
     *
     * Takes old event id and determines if it's still pending. If so, the old event is updated. Otherwise, we create
     * a new event and mark the old one as replaced.
     *
     * @param int $old_event_id
     * @param Entity $new_event
     * @return int|null New event id
     */
    public function reschedule(int $old_event_id, Entity $new_event): ?int
    {
        $old_event = $this->entity($old_event_id)
            ->scope(Scope::make()->fields(['project_id', 'is_pending']))
            ->run();
        // if old event is in pending state, then we just update the same records since it isn't locked yet
        if ($old_event['is_pending']) {
            $new_event['id'] = $old_event_id;
            $new_event['is_pending'] = true;
            $this->partialUpdate($new_event)->run();
            return null;
        }

        // if old event is locked due to is_pending being false, then we create a new event and mark the old one replaced
        // we also set the new event's id as the replaced event id on the previous one so we can track the succession
        $batch_request = BatchRequest::make()->sequential();

        $new_event['project_id'] = $old_event['project_id'];
        $create_request = $this->create($new_event);
        $batch_request->add($create_request);

        $update_entity = Entity::make([
            'id' => $old_event_id,
            'status' => EventResource::STATUS_REPLACED
        ]);
        $update_request = $this->partialUpdate($update_entity)
            ->attach('batch.last_request', function () use ($update_entity, $create_request) {
                $update_entity->set('replaced_by_event_id', $create_request->response());
            });
        $batch_request->add($update_request);

        $batch_request->run();

        return $create_request->response();
    }

    /**
     * Check if project has any active events
     *
     * @param int $project_id
     * @return bool
     */
    public function hasActiveEventsForProjectID(int $project_id): bool
    {
        $count = $this->newScopedQuery()->where('projectSchedule.projectID', $project_id)
            ->where('projectSchedule.status', ProjectSchedule::STATUS_ACTIVE)
            ->count();
        return $count > 0;
    }

    /**
     * Enqueue calendar push jobs for any event which is found in defined query scope
     *
     * @param Closure $scope
     */
    protected function pushToCalendar(Closure $scope): void
    {
        $query = $this->newScopedQuery()->select('projectSchedule.*');
        $query = $scope($query);
        $query->whereIn('projectSchedule.status', [ProjectSchedule::STATUS_ACTIVE, ProjectSchedule::STATUS_COMPLETED])
            ->each(function (ProjectSchedule $event) {
                // set push status to in progress before we enqueue job, this ensures the sync related ui buttons are
                // disabled while the job is processing
                $event->isCalendarPushInProgress = true;
                $event->save();
                CalendarPushJob::enqueue($event->getKey());
            });
    }

    /**
     * Enqueue calendar push job any events related to customer
     *
     * @param int $customer_id
     */
    public function pushToCalendarByCustomerID(int $customer_id): void
    {
        $this->pushToCalendar(function ($query) use ($customer_id) {
            // project and property already joined via scoped query
            return $query->where('property.customerID', $customer_id);
        });
    }

    /**
     * Enqueue calendar push job for any events related to property
     *
     * @param int $property_id
     */
    public function pushToCalendarByPropertyID(int $property_id): void
    {
        $this->pushToCalendar(function ($query) use ($property_id) {
            // project already joined via scoped query
            return $query->where('project.propertyID', $property_id);
        });
    }

    /**
     * Enqueue calendar push job any events related to project
     *
     * @param int $project_id
     */
    public function pushToCalendarByProjectID(int $project_id): void
    {
        $this->pushToCalendar(function ($query) use ($project_id) {
            return $query->where('projectSchedule.projectID', $project_id);
        });
    }

    /**
     * Cancel any future active events by project id
     *
     * @param int $project_id
     */
    public function cancelFutureEventsByProjectID(int $project_id): void
    {
        $now = Carbon::now('UTC');

        $future_events = $this->newScopedQuery()->where('projectSchedule.projectID', $project_id)
            ->where('projectSchedule.scheduledStart', '>=', $now)
            ->where('projectSchedule.status', ProjectSchedule::STATUS_ACTIVE)
            ->get();
        foreach ($future_events as $event) {
            $this->partialUpdate(Entity::make([
                'id' => $event->getKey(),
                'status' => static::STATUS_CANCELLED
            ]))->run();
        }
    }

    /**
     * Delete all events by project ID
     *
     * @param int $project_id
     * @param bool $force
     */
    public function deleteByProjectID(int $project_id, bool $force = false): void
    {
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.projectID', $project_id)
            ->get()
            ->each(function ($event) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($event);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Event is immutable'), $e, [
                        'project_event_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete event'), $e, [
                        'project_event_id' => $id
                    ]);
                }
            });
    }
}
