<?php

declare(strict_types=1);

namespace App\FileBuilders\Bid\File;

use App\Classes\Acl;
use App\Classes\Log;
use App\ResourceMediaHandlers\Company\Logo\DocumentThumbnailHandler;
use App\ResourceMediaHandlers\Drawing\RepairPlanHandler;
use App\Services\CompanyFeatureService;
use App\Services\CompanySettingService;
use App\Services\Wisetack\Api\PromoApiService;
use App\Services\WisetackService;
use Common\Models\Company;
use Common\Models\Feature;
use Common\Models\WisetackMerchant;
use App\Resources\{
    CompanyResource,
    DrawingResource,
    MediaResource as MediaLibraryResource,
    ProjectResource,
    UserResource
};
use App\Resources\Bid\Item\{
    ContentResource,
    CustomDrawingResource,
    DrawingResource as ItemDrawingResource,
    InstallmentPaymentTerm\InstallmentResource,
    LineItemResource,
    MediaResource,
    OneTimePaymentTermResource,
    PaymentTermResource,
    PriceAdjustmentLineItemResource,
    SectionResource
};
use Core\Components\Http\StaticAccessors\URI;
use Exception;
use App\Resources\Form\Item\{
    Group\LayoutResource
};
use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Services\Form\Classes\Entry;
use App\Services\Form\Types\Company\BidType;
use App\Services\TimeService;
use Brick\Math\{BigDecimal, RoundingMode};
use Carbon\Carbon;
use Common\Models\BidItem;
use Core\Components\Resource\Classes\{Collection, Entity, Resource, Scope};
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\Traits\ArrayableTrait;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

/**
 * Class Context
 *
 * Pulls info from bid related data sources and builds a context array for use with templates. Also pulls in
 * attachments and sends them to the components instance for later use.
 *
 * @package App\FileBuilders\Bid\File
 */
class Context
{
    use ArrayableTrait;

    /**
     * @var BidItem
     */
    protected $model;

    /**
     * @var Acl
     */
    protected $acl;

    /**
     * @var Components
     */
    protected $components;

    /**
     * @var TimeService
     */
    protected $time_service;

    /**
     * @var array
     */
    protected $media = [];

    /**
     * Context constructor
     *
     * Builds context based on passed data.
     *
     * @param BidItem $model
     * @param Acl $acl
     * @param Components $components
     * @param TimeService $time_service
     * @throws \App\Exceptions\TimeException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function __construct(BidItem $model, Acl $acl, Components $components, TimeService $time_service)
    {
        $this->model = $model;
        $this->acl = $acl;
        $this->components = $components;
        $this->time_service = $time_service;

        $this->build();
    }

    /**
     * @return Acl
     */
    public function getAcl(): Acl
    {
        return $this->acl;
    }

    /**
     * Add media to global list
     *
     * @param string $name
     * @param string $link
     */
    protected function addMedia(string $name, string $link): void
    {
        $this->media[] = compact('name', 'link');
    }

    /**
     * Get created by user for bid
     */
    protected function getCreatedByUser(): void
    {
        try {
            $user_scope = Scope::make()
                ->fields(['first_name', 'last_name', 'email'])
                ->with([
                    'phones' => [
                        'fields' => ['number', 'description', 'is_primary']
                    ]
                ]);
            $created_by_user = UserResource::make($this->acl)
                ->entity($this->model->createdByUserID)
                ->scope($user_scope)
                ->run()
                ->toArray();
            $created_by_user['primary_phone'] = null;
            if (count($created_by_user['phones']) > 0) {
                $phones = array_filter($created_by_user['phones'], function ($phone) {
                    return $phone['is_primary'];
                });
                if (count($phones) > 0) {
                    $created_by_user['primary_phone'] = array_shift($phones);
                }
            }
        } catch (EntityNotFoundException $e) {
            $created_by_user = null;
        }

        $this->set('created_by_user', $created_by_user);
    }

    /**
     * Get all project related data
     *
     * This pulls in project and it's relations in one query and divides that data into multiple context vars. Currently
     * project, project contact, project salesperson, property, customer, customer phones, company, and company phone
     * data is grabbed and inserted into context.
     *
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getProjectRelatedData(): void
    {
        $project_resource = ProjectResource::make($this->acl);
        $project_scope = Scope::make()
            ->fields(['description'])
            ->with([
                'salesperson_user' => [
                    'fields' => ['id', 'first_name', 'last_name', 'email', 'image_file_id'],
                    'with' => [
                        'phones' => [
                            'fields' => ['number', 'description', 'is_primary']
                        ]
                    ]
                ],
                'property' => [
                    'fields' => [
                        'id', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township', 'latitude',
                        'longitude', 'image_file_id'
                    ],
                    'with' => [
                        'customer' => [
                            'fields' => [
                                'id', 'business_name', 'first_name', 'last_name', 'email', 'address', 'address_2', 'city',
                                'state', 'zip'
                            ],
                            'with' => [
                                'company' => [
                                    'fields' => [
                                        'id', 'name', 'address', 'address_2', 'city', 'state', 'zip', 'website',
                                        'logo_file_id', 'latitude', 'longitude'
                                    ],
                                    'with' => [
                                        'phones' => [
                                            'fields' => ['number', 'description', 'is_primary']
                                        ]
                                    ]
                                ],
                                'phones' => [
                                    'fields' => ['number', 'description', 'is_primary']
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $data = $project_resource
            ->entity($this->model->projectID)
            ->scope($project_scope)
            ->run()
            ->toArray();

        // handle customer info
        $customer = $data['property']['customer'];
        $customer['primary_phone'] = null;
        if (count($customer['phones']) > 0) {
            $phones = array_filter($customer['phones'], function ($phone) {
                return $phone['is_primary'];
            });
            if (count($phones) > 0) {
                $customer['primary_phone'] = array_shift($phones);
            }
        }
        $company = $customer['company'];
        unset($customer['company']);

        $this->set('customer', $customer);

        // handle property info
        $property = $data['property'];
        $property['full_address'] = $property['address'] . (isset($property['address_2']) ? ' ' . $property['address_2'] : '') . " {$property['city']}, {$property['state']} {$property['zip']}";
        /** @var \App\Resources\PropertyResource $property_resource */
        $property_resource = $project_resource->relationResource('property');
        if ($property['image_file_id'] !== null) {
            $property_image = $property_resource->getMedia()->get('image');
            $property['image'] = [
                'original' => $property_image->getOriginal()->getUrl($property['id'])->port(null)->csm()->build(),
                'medium' => $property_image->getVariant('size_medium')->getUrl($property['id'])->port(null)->csm()->build()
            ];
        }
        $street_view_image = $property_resource->getMedia()->get('street_view_image');
        $property['street_view_image'] = [
            'original' => $street_view_image->getOriginal()->getUrl($property['id'])->port(null)->csm()->build()
        ];
        $this->set('property', $property);

        // handle project salesperson
        $salesperson_user = $data['salesperson_user'];
        if ($salesperson_user !== null) {
            $salesperson_user['primary_phone'] = null;
            if (count($salesperson_user['phones']) > 0) {
                $phones = array_filter($salesperson_user['phones'], function ($phone) {
                    return $phone['is_primary'];
                });
                if (count($phones) > 0) {
                    $salesperson_user['primary_phone'] = array_shift($phones);
                }
            }
            if ($salesperson_user['image_file_id'] !== null) {
                /** @var UserResource $salesperson_user_resource */
                $salesperson_user_resource = $project_resource->relationResource('salesperson_user');
                $salesperson_user['image'] = [
                    'original' => $salesperson_user_resource->getMediaHandler('image')
                        ->getUrlFromID($salesperson_user['id'])->port(null)->csm()->build(),
                    'thumbnail' => $salesperson_user_resource->getMediaHandler('image', 'bid_thumbnail')
                        ->getUrlFromID($salesperson_user['id'])->port(null)->csm()->build()
                ];
            }
        }
        $this->set('salesperson_user', $salesperson_user);

        unset($data['customer'], $data['property'], $data['salesperson_user']);

        // handle project
        // all the other data comes from the project data, so we have to pull all that out before saving the project info
        $this->set('project', $data);

        // handle company
        if ($company['logo_file_id'] !== null) {
            $company_resource = CompanyResource::make($this->acl);
            /** @var DocumentThumbnailHandler $company_logo_handler */
            $company_logo_handler = $company_resource->getMediaHandler('logo', 'document_thumbnail');
            $company['logo'] = [
                'small' => $company_logo_handler->getPath($company['id'], $company['logo_file_id']),
                'original' => $company_resource->getMedia()->get('logo')->getOriginal()->getPath($company['logo_file_id'])
            ];
        }
        $this->set('company', $company);
    }

    /**
     * Get bid sections
     *
     * Grab section and form data. If sections component is enabled, associated forms are rendered and inserted
     * into context for display.
     *
     * @param UuidInterface $id
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidSections(UuidInterface $id): void
    {
        $section_scope = Scope::make()
            ->fields(['id', 'name'])
            ->query(function ($query) {
                return $query->ordered();
            })
            ->filter('item_id', 'eq', $id->toString())
            ->with([
                'forms' => [
                    'fields' => ['company_form_item_id', 'form_item_entry_id'],
                    'query' => fn($query) => $query->ordered(),
                    'with' => [
                        'company_form_item' => [
                            'fields' => ['name']
                        ]
                    ]
                ]
            ]);
        $sections = SectionResource::make($this->acl)->collection()
            ->scope($section_scope)
            ->run()
            ->toArray();

        $this->set('bid.sections', $sections);

        // handle sections data
        if ($this->components->isEnabled(Components::SECTIONS)) {
            $_sections = [];
            foreach ($sections as $section) {
                $_section = [
                    'name' => $section['name'],
                    'forms' => []
                ];
                foreach ($section['forms'] as $form) {
                    $bid_type = BidType::getByID($this->acl, $form['company_form_item_id']);
                    if ($bid_type->getIsHiddenFromBidDocument()) {
                        continue;
                    }
                    $entry = Entry::getByID($this->acl, $form['form_item_entry_id']);
                    $template = $bid_type->render($this->acl, LayoutResource::TYPE_OUTPUT_BID_DOCUMENT, $entry);
                    if ($template['content'] === '') {
                        continue;
                    }
                    // @todo handle styles?
                    $_section['forms'][] = [
                        'content' => $template['content']
                    ];
                }
                $_sections[] = $_section;
            }
            // filter out any sections with no forms
            $_sections = array_filter($_sections, function ($section) {
                return count($section['forms']) > 0;
            });
            $this->set('sections', $_sections);
        }
    }

    /**
     * Get bid line items and compile totals
     *
     * Pulls all line items, compiles totals for each section, handles price adjustments in proper order, and injects
     * data into context for use in template
     *
     * @param UuidInterface $id
     */
    protected function getBidLineItemData(UuidInterface $id): void
    {
        // get all line items for bid
        $line_item_scope = Scope::make()
            ->fields(['type', 'name', 'quantity', 'amount', 'subtotal', 'total', 'bid_item_section_id'])
            ->filter('bid_item_id', 'eq', $id->toString())
            ->disablePagination()
            ->query(function ($query) {
                return $query->ordered();
            })
            ->with(['item']);
        $line_items = LineItemResource::make($this->acl)->collection()
            ->scope($line_item_scope)
            ->run()
            ->toArray();

        // group line items based on type
        $grouped_line_items = [
            'sections' => [],
            'discounts' => [],
            'fees' => []
        ];
        $line_item_types = [
            LineItemResource::TYPE_GENERAL => 'is_type_general',
            LineItemResource::TYPE_PRODUCT => 'is_type_product',
            LineItemResource::TYPE_DISCOUNT => 'is_type_discount',
            LineItemResource::TYPE_FEE => 'is_type_fee'
        ];
        foreach ($line_items as $line_item) {
            // inject type booleans into line item for template use since you can't do complex if statements
            foreach ($line_item_types as $type => $var) {
                $line_item[$var] = $line_item['type'] === $type;
            }
            if ($line_item['type'] === LineItemResource::TYPE_PRODUCT) {
                $line_item['description'] = $line_item['item']['description'];
            }
            if (isset($line_item['bid_item_section_id'])) {
                if (!isset($grouped_line_items['sections'][$line_item['bid_item_section_id']])) {
                    $grouped_line_items['sections'][$line_item['bid_item_section_id']] = [];
                }
                $grouped_line_items['sections'][$line_item['bid_item_section_id']][] = $line_item;
                continue;
            }
            switch ($line_item['type']) {
                case LineItemResource::TYPE_DISCOUNT:
                case LineItemResource::TYPE_FEE:
                    $type_map = [
                        LineItemResource::TYPE_DISCOUNT => 'discounts',
                        LineItemResource::TYPE_FEE => 'fees'
                    ];
                    $line_item['amount'] = $line_item['item']['amount'];
                    // convert amount type into something more template/user friendly than an int
                    $line_item['is_percentage'] = $line_item['item']['amount_type'] === PriceAdjustmentLineItemResource::AMOUNT_TYPE_PERCENTAGE;
                    unset($line_item['item']);
                    $grouped_line_items[$type_map[$line_item['type']]][] = $line_item;
                    break;
            }
        }

        $has_section = false;
        $section_subtotal = BigDecimal::of('0.00');
        $sections = $this->get('bid.sections');
        foreach ($sections as &$section) {
            if (!isset($grouped_line_items['sections'][$section['id']])) {
                continue;
            }
            if (!$has_section) {
                $has_section = true;
            }
            $section['line_items'] = $grouped_line_items['sections'][$section['id']];
            // gather pricing disclaimers for the line items of a section and assign proper footnote ids
            $pricing_disclaimers = [];
            $i = 0;
            foreach ($section['line_items'] as &$line_item) {
                if (
                    $line_item['type'] !== LineItemResource::TYPE_PRODUCT ||
                    $line_item['item']['pricing_disclaimer'] === null
                ) {
                    continue;
                }
                $disclaimer = $line_item['item']['pricing_disclaimer'];
                if (!isset($pricing_disclaimers[$disclaimer])) {
                    $pricing_disclaimers[$disclaimer] = ++$i;
                }
                $line_item['pricing_disclaimer_id'] = $pricing_disclaimers[$disclaimer];
                unset($line_item);
            }
            $section['pricing_disclaimers'] = array_flip($pricing_disclaimers);
            $total_cost = array_reduce($section['line_items'], function (BigDecimal $carry, $item) {
                // round total before adding so each line item sums up properly. when displaying it will be rounded and
                // this can cause a mismatch between the end total and individual item sum.
                return $carry->plus(BigDecimal::of($item['total'])->toScale(2, RoundingMode::HALF_DOWN));
            }, BigDecimal::of('0.00'));
            $section['total_cost'] = (string) $total_cost;
            $section_subtotal = $section_subtotal->plus($total_cost);
            unset($section);
        }

        // order of the price adjustments matters, in the future people may want to apply these (or new ones) in a different order
        $price_adjustments = [
            'discounts' => [
                'var' => 'discount_total',
                'negative' => true
            ],
            'fees' => ['var' => 'fee_total']
        ];

        // update price adjustments with proper totals (needed since percentages are relative and we need the previous total before
        // we can calculate things properly
        $has_price_adjustment = false;
        $prev_total = $section_subtotal;
        foreach ($price_adjustments as $name => $config) {
            $pa_total = BigDecimal::of('0.00');
            foreach ($grouped_line_items[$name] as &$line_item) {
                if (!$has_price_adjustment) {
                    $has_price_adjustment = true;
                }
                $total = BigDecimal::of($line_item['quantity'])->multipliedBy($line_item['amount']);
                if ($line_item['is_percentage']) {
                    $total = $total->dividedBy('100')->multipliedBy($prev_total);
                }
                if (isset($config['negative']) && $config['negative']) {
                    $total = $total->negated();
                }
                $total = $total->toScale(2, RoundingMode::HALF_DOWN);
                $line_item['total'] = (string) $total;
                $pa_total = $pa_total->plus($total);
                unset($line_item);
            }
            $this->set("bid.{$config['var']}", (string) $pa_total);
            $prev_total = $prev_total->plus($pa_total);
        }

        // if the bid has sections with line items then add the section subtotal to the bid context
        if ($has_section && $has_price_adjustment) {
            $this->set('bid.section_subtotal', (string) $section_subtotal);
        }
        $this->set('bid.total', (string) $prev_total);
        $this->set('bid.line_items', $grouped_line_items);
        $this->set('bid.sections', $sections);
    }

    /**
     * Get bid payment terms and determine amounts based on bid total calculated in line item data method
     *
     * Maps payment term data into easy to use format for displaying in template.
     *
     * @param UuidInterface $id
     */
    protected function getBidPaymentTerms(UuidInterface $id): void
    {
        $percentage_payment_terms = [];
        $payment_terms_resource = PaymentTermResource::make($this->acl);
        $payment_terms_scope = Scope::make()
            ->fields(['type', 'order'])
            ->filter('bid_item_id', 'eq', $id->toString())
            ->with([
                'item' => [
                    'poly_scopes' => [
                        PaymentTermResource::TYPE_ONE_TIME => [
                            'fields' => ['due_time_frame']
                        ],
                        PaymentTermResource::TYPE_INSTALLMENT => [
                            'no_fields' => true,
                            'with' => [
                                'installments' => [
                                    'fields' => ['id', 'name', 'due_time_frame', 'amount_type', 'amount', 'order'],
                                    'query' => function ($query) {
                                        return $query->ordered();
                                    }
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $payment_terms = $payment_terms_resource->collection()->scope($payment_terms_scope)->run();
        if ($payment_terms->count() > 0) {
            $payment_term_type_map = [
                PaymentTermResource::TYPE_ONE_TIME => 'ONE_TIME',
                PaymentTermResource::TYPE_INSTALLMENT => 'INSTALLMENT'
            ];
            $one_time_due_time_frame_map = [
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_BID_ACCEPTANCE => 'AT_BID_ACCEPTANCE',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_BID_ACCEPTANCE => 'AFTER_BID_ACCEPTANCE',
                OneTimePaymentTermResource::DUE_TIME_FRAME_BEFORE_PROJECT_START => 'BEFORE_PROJECT_START',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_PROJECT_START => 'AFTER_PROJECT_START',
                OneTimePaymentTermResource::DUE_TIME_FRAME_BEFORE_PROJECT_COMPLETION => 'BEFORE_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_PROJECT_COMPLETION => 'AT_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_PROJECT_COMPLETION => 'AFTER_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_CLOSING => 'AT_CLOSING'
            ];
            $installment_due_time_frame_map = [
                InstallmentResource::DUE_TIME_FRAME_AT_BID_ACCEPTANCE => 'AT_BID_ACCEPTANCE',
                InstallmentResource::DUE_TIME_FRAME_AFTER_BID_ACCEPTANCE => 'AFTER_BID_ACCEPTANCE',
                InstallmentResource::DUE_TIME_FRAME_BEFORE_PROJECT_START => 'BEFORE_PROJECT_START',
                InstallmentResource::DUE_TIME_FRAME_AFTER_PROJECT_START => 'AFTER_PROJECT_START',
                InstallmentResource::DUE_TIME_FRAME_BEFORE_PROJECT_COMPLETION => 'BEFORE_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AT_PROJECT_COMPLETION => 'AT_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AFTER_PROJECT_COMPLETION => 'AFTER_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AT_CLOSING => 'AT_CLOSING'
            ];
            $installment_amount_type_map = [
                InstallmentResource::AMOUNT_TYPE_TOTAL => 'TOTAL',
                InstallmentResource::AMOUNT_TYPE_PERCENTAGE => 'PERCENTAGE'
            ];
            $bid_total = $this->get('bid.total');
            foreach ($payment_terms as $payment_term) {
                switch ($payment_term->type) {
                    case PaymentTermResource::TYPE_ONE_TIME:
                        $payment_term->item->due_time_frame = $one_time_due_time_frame_map[$payment_term->get('item.due_time_frame')];
                        $payment_term->item->amount = $bid_total;
                        $percentage_payment_terms[] = [
                            'name' => 'One-Time Payment',
                            'due_time_frame' => $payment_term->get('item.due_time_frame'),
                            'amount' => $payment_term->item->amount,
                            'order' => 1
                        ];
                        break;
                    case PaymentTermResource::TYPE_INSTALLMENT:
                        $installments = $payment_term->get('item.installments', []);
                        // loop through all installments and get total of all non-percentage based installments
                        $total_sum = BigDecimal::of('0');
                        $last_percentage_installment = null;
                        foreach ($installments as $installment) {
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_TOTAL) {
                                $total_sum = $total_sum->plus($installment->amount);
                            } elseif ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $last_percentage_installment = $installment->id;
                            }
                        }
                        // get difference of non-percentage installments and bid total to get sum for percentage
                        // installment calculations
                        $percentage_base_total = BigDecimal::of($bid_total)
                            ->minus($total_sum);
                        if ($percentage_base_total->isNegativeOrZero()) {
                            $percentage_base_total = BigDecimal::of('0');
                        }
                        $percentage_total = BigDecimal::of('0');
                        foreach ($installments as $installment) {
                            $amount = BigDecimal::of($installment->amount);
                            // if amount type is percentage, then we need to multiply the amount by the base
                            // percentage total. if this is the last percentage, then we make any adjustments to the
                            // amount due to rounding errors
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $amount = $percentage_base_total->multipliedBy($amount)->toScale(2, RoundingMode::HALF_DOWN);
                                $percentage_total = $percentage_total->plus($amount);
                                if ($installment->id === $last_percentage_installment) {
                                    $diff = $percentage_base_total->minus($percentage_total);
                                    if (!$diff->isZero()) {
                                        $amount = $amount->plus($diff);
                                    }
                                }
                            }
                            $installment->amount_type = $installment_amount_type_map[$installment->amount_type];
                            $installment->due_time_frame = $installment_due_time_frame_map[$installment->due_time_frame];
                            $installment->amount = (string) $amount->toScale(2, RoundingMode::HALF_DOWN);
                            $percentage_payment_terms[] = $installment->toArray();
                        }
                        break;
                }
                $payment_term->type = $payment_term_type_map[$payment_term->type];
            }
        }
        $this->set('percentage_payment_terms', $percentage_payment_terms);
        $this->set('bid.payment_terms', $payment_terms->toArray());
    }

    /**
     * Get bid content, divided into various type groups, and replace FX template tags
     *
     * @param UuidInterface $id
     * @throws \App\Exceptions\TimeException
     */
    protected function getBidContent(UuidInterface $id): void
    {
        $terms_conditions = [];
        $content_resource = ContentResource::make($this->acl);
        $content_types = [
            ContentResource::TYPE_DISCLAIMER => 'disclaimers',
            ContentResource::TYPE_WAIVER => 'waivers',
            ContentResource::TYPE_WARRANTY => 'warranties',
            ContentResource::TYPE_CONTRACT => 'contracts',
            ContentResource::TYPE_ACKNOWLEDGEMENT => 'acknowledgements'
        ];
        $content_scope = Scope::make()
            ->fields(['type', 'name', 'content', 'order'])
            ->filter('type', 'in', array_keys($content_types))
            ->filter('item_id', 'eq', $id->toString())
            ->query(function ($query) {
                return $query->ordered();
            });
        /** @var Collection $content */
        $content = $content_resource->collection()->scope($content_scope)->run();
        if (count($content) > 0) {
            $content = $content->groupBy('type');
            $customer = $this->get('customer');
            $property = $this->get('property');
            $fx_tag_info = [
                'date' => $this->time_service->get(Carbon::now('UTC'))->format('n/j/Y'),
                'firstName' => $customer['first_name'],
                'lastName' => $customer['last_name'],
                'businessName' => $customer['business_name'],
                'address' => $property['address'] . ($property['address_2'] !== null ? ' ' . $property['address_2'] : ''),
                'address1' => $property['address'],
                'address2' => $property['address_2'],
                'city' => $property['city'],
                'state' => $property['state'],
                'zip' => $property['zip'],
                'phone' => $customer['primary_phone']['number'],
                'email' => $customer['email'],
                'bidNumber' => $this->get('bid.reference_id'),
                'description' => $this->get('project.description')
            ];
            $fx_tags = array_map(function ($tag) {
                return "{{$tag}}";
            }, array_keys($fx_tag_info));
            $fx_tag_values = array_values($fx_tag_info);
            foreach ($content_types as $type => $key) {
                if (!isset($content[$type])) {
                    continue;
                }
                foreach ($content[$type] as $content_type) {
                    $content_type->content = str_replace($fx_tags, $fx_tag_values, $content_type->content);
                    $content_type->key_name = str_replace(" ", "_", strtolower($content_type->name)) . '_' . $content_type->order;
                }
                $terms_conditions[$key] = $content[$type]->toArray();
            }
        }
        $this->set('terms_conditions', $terms_conditions);
    }

    /**
     * Pull all bid images from form entries defined in bid section data
     *
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidImages(): void
    {
        if (!$this->components->isEnabled(Components::IMAGES)) {
            return;
        }
        $file_resource = FieldFileResource::make($this->acl);
        $file_handler = $file_resource->getMedia()->get('file')->getOriginal();
        $images = [];
        foreach ($this->get('bid.sections') as $section) {
            $image_group = [
                'name' => $section['name'],
                'forms' => []
            ];
            foreach ($section['forms'] as $form) {
                $_form = [
                    'name' => $form['company_form_item']['name'],
                    'images' => []
                ];
                $scope = Scope::make()
                    ->fields(['id', 'file_id'])
                    ->with([
                        'file' => [
                            'fields' => ['name', 'description']
                        ]
                    ])
                    ->sort('created_at');
                $form_images = $file_resource
                    ->getAllImagesByEntryID($form['form_item_entry_id'], $scope)
                    ->toArray();
                foreach ($form_images as $image) {
                    $_form['images'][] = [
                        'full' => $file_handler->getPath($image['file_id']),
                        'name' => $image['file']['name'],
                        'description' => $image['file']['description']
                    ];
                }
                if (count($_form['images']) > 0) {
                    $image_group['forms'][] = $_form;
                }
            }
            if (count($image_group['forms']) > 0) {
                $images[] = $image_group;
            }
        }
        if (count($images) > 0) {
            $this->set('images', $images);
        }
    }

    /**
     * Grab all bid media, add PDF files to media component to attachment, add rest to context for use in media links
     *
     * @param UuidInterface $id
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidMedia(UuidInterface $id): void
    {
        $media_library_resource = MediaLibraryResource::make($this->acl);
        $library_file_media = $media_library_resource->getMedia()->get('file')->getOriginal();
        $media_scope = Scope::make()
            ->fields(['type'])
            ->filter('bid_item_id', 'eq', $id->toString())
            ->query(function ($query) {
                return $query->ordered();
            })
            ->with([
                'item' => [
                    'poly_scopes' => [
                        MediaResource::TYPE_LIBRARY => [
                            'fields' => ['id', 'name'],
                            'with' => [
                                'file' => [
                                    'fields' => ['id', 'content_type', 'data']
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $items = MediaResource::make($this->acl)->collection()->scope($media_scope)->run();

        if ($items->count() > 0) {
            /** @var Entity $item */
            foreach ($items as $item) {
                switch ($item->type) {
                    case MediaResource::TYPE_LIBRARY:
                        // if item is a pdf and it isn't write protected, then we add it to be attached
                        if (
                            $item->get('item.file.content_type') === 'application/pdf' &&
                            !$item->get('item.file.data.protected', false)
                        ) {
                            $path = $library_file_media->getPath($item->get('item.file.id'));
                            $this->components->addFile(Components::MEDIA, $path, $item->get('item.file.data.pages'));
                            break;
                        }
                        $this->addMedia($item->get('item.name'), $library_file_media->getUrl($item->get('item.id'))->csm()->build());
                        break;
                }
            }
        }
    }

    /**
     * Get all bid drawings and add to drawing component for attachment
     *
     * @param UuidInterface $id
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidDrawingFiles(UuidInterface $id): void
    {
        $drawing_scope = Scope::make()
            ->field('drawing_id')
            ->filter('item_id', 'eq', $id->toString());
        $drawing_resource = ItemDrawingResource::make($this->acl);
        $drawings = $drawing_resource->collection()
            ->scope($drawing_scope)
            ->run();
        if ($drawings->count() > 0) {
            /** @var RepairPlanHandler $drawing_handler */
            $drawing_handler = DrawingResource::make($this->acl)->getMediaHandler('repair_plan');
            foreach ($drawings as $drawing) {
                $path = $drawing_handler->getPath($drawing['drawing_id']);
                $this->components->addFile(Components::DRAWINGS, $path, 1);
            }
        }
    }

    /**
     * Get all custom drawings for bid and add to custom drawings component for attachment
     *
     * @param UuidInterface $id
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidCustomDrawingFiles(UuidInterface $id): void
    {
        // get custom drawings
        $custom_drawing_scope = Scope::make()
            ->fields(['id', 'file_id'])
            ->filter('item_id', 'eq', $id->toString())
            ->query(function ($query) {
                return $query->ordered();
            })
            ->with([
                'file' => [
                    'fields' => ['name', 'data']
                ]
            ]);
        $custom_drawing_resource = CustomDrawingResource::make($this->acl);
        $drawings = $custom_drawing_resource->collection()
            ->scope($custom_drawing_scope)
            ->run();
        if ($drawings->count() > 0) {
            $custom_drawing_media = $custom_drawing_resource->getMedia()->get('file')->getOriginal();
            foreach ($drawings as $drawing) {
                if ($drawing->get('file.data.protected', false)) {
                    $this->addMedia($drawing->get('file.name'), $custom_drawing_media->getUrl($drawing['id'])->csm()->build());
                    continue;
                }
                $path = $custom_drawing_media->getPath($drawing['file_id']);
                $this->components->addFile(Components::CUSTOM_DRAWINGS, $path, $drawing->get('file.data.pages'));
            }
        }
    }


    /**
     * Determines if the Wisetack section should be shown based on various flags and data.
     *
     * - is_wisetack_enabled: Flag indicating if Wisetack is enabled.
     * - merchant: Data returned from the Wisetack API indicating wisetack merchant.
     * - is_financing_required_for_all_projects: Company setting that requires financing for all projects.
     * - is_financing_enabled_for_project: Flag indicating if financing is enabled for the specific project.
     *
     * @returns {boolean} True if the Wisetack section should be shown, otherwise false.
     */
    protected function shouldShowWisetackSection($company_id, $merchant, $bid) {
        $company_feature = new CompanyFeatureService($company_id);
        $is_wisetack_enabled = $company_feature->has(Feature::WISETACK_API, true);

        $setting_service = new CompanySettingService($company_id);
        $is_financing_required_for_all_projects = $setting_service->get('wisetack_financing_required_for_all_projects', false);

        $project = $bid->project()->first();
        $is_financing_enabled_for_project = $project->isFinancingEnabled;


        if (!$is_wisetack_enabled || !$merchant || !$merchant->isApproved()) {
            return false;
        }

        if ($is_financing_required_for_all_projects) {
            return true;
        }

        return !!$is_financing_enabled_for_project;
    }


    /**
     * Get Wisetack financing information
     *
     * @param $bid_id
     * @return void
     */
    protected function getWisetackInfo($bid_id): void {
        $this->set('wisetack_financing', false);
        $this->set('wisetack_financing_preview_mode', false);

        try {
            $company_id = $this->get('company')['id'];
            $company = Company::find($company_id);

            if (!$company) {
                throw new Exception('Company not found');
            }

            $bid = BidItem::findByUuid($bid_id);

            if (!$bid) {
                throw new Exception('Bid not found');
            }

            $company_uuid = $company->companyUUID;
            $merchant = WisetackMerchant::where('companyUUID', $company_uuid)->whereNull('deletedAt')->first();

            if ($this->shouldShowWisetackSection($company_id, $merchant, $bid)) {
                // Preview mode - should mock a few values
                if ($bid['status'] == BidItem::STATUS_INCOMPLETE || $bid['status'] == BidItem::STATUS_SUBMITTED) {
                    $promo = [ 'monthlyPayment' => "_____", 'termLength' => "_____"];
                    $this->set('wisetack_promo_monthly_payment', $promo['monthlyPayment']);
                    $this->set('wisetack_promo_term_length', $promo['termLength']);
                    $this->set('wisetack_financing', true);
                    $this->set('wisetack_financing_preview_mode', true);
                    $this->set('wisetack_promo_learn_more_link', "https://wisetack.com");
                    return;
                }
                if ($merchant) {
                    $merchant_id = strval(UUID::fromBytes($merchant->wisetackMerchantID));
                    $service = new WisetackService(null);
                    $promo = $service->generatePromotionalMessaging($bid['total'], $merchant_id);
                    $learn_more_link = $service->buildPromotionalLearnMoreLink($promo, $bid['total']);

                    $this->set('wisetack_promo_monthly_payment', $promo['monthlyPayment']);
                    $this->set('wisetack_promo_term_length', $promo['termLength']);
                    $app_source = "appSource=bid-pdf";
                    $this->set('wisetack_payment_link', URI::route('wisetack.transactions.bid.redirect', ['id' => $bid_id])->build()  . "?" . $app_source);
                    $this->set('wisetack_financing', true);
                    $this->set('wisetack_promo_learn_more_link', $learn_more_link);

                }
            }
    } catch (Exception $e) {
            Log::create('wisetack_bid_generation')->error('Unable to generate wisetack info for bid', [
                'exception' => $e->getMessage(),
                'bid_id' => $bid_id,
            ]);
        }
    }

    /**
     * Fetch all bid related context data
     *
     * Sets basic info like reference id and timestamps. Grabs section, line item, payment term, content, image,
     * media, and drawing file data.
     *
     * @throws \App\Exceptions\TimeException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    protected function getBidInfo(): void
    {
        $bid_id = $this->model->getUuidKey();

        $this->set('bid.reference_id', $this->model->referenceID);

        $this->set('bid.created_at', $this->time_service->getFromUtc($this->model->createdAt, true)->format('c'));
        if ($this->model->status === BidItem::STATUS_ACCEPTED) {
            $accepted_at = $this->time_service->getFromUtc($this->model->acceptedAt, true);
            $this->set('bid.accepted_signature', $this->model->acceptedSignature);
            $this->set('bid.accepted_date', $accepted_at->format('n/d/Y'));
            $this->set('bid.accepted_time', $accepted_at->format('g:ia'));
        }

        $this->getWisetackInfo($bid_id);

        $this->getBidSections($bid_id);
        $this->getBidLineItemData($bid_id);
        $this->getBidPaymentTerms($bid_id);
        $this->getBidContent($bid_id);
        $this->getBidImages();
        $this->getBidMedia($bid_id);
        $this->getBidDrawingFiles($bid_id);
        $this->getBidCustomDrawingFiles($bid_id);

        // assign all media derived from above calls to media key in context
        $this->set('media', $this->media);
    }

    /**
     * Build context using model data
     *
     * Set current time, created by user, project data, and all bid info items
     *
     * @throws \App\Exceptions\TimeException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function build(): void
    {
        $current_time = $this->time_service->get(Carbon::now('UTC'));
        $this->set('current_time', $current_time->format('c'));

        $this->getCreatedByUser();
        $this->getProjectRelatedData();
        $this->getBidInfo();
    }
}
