<?php

namespace App\ResourceDelegates;

use App\NotificationJobs\User\ProjectAssignmentNotificationJob;
use App\ResourceMediaHandlers\Project\OverviewHandler;
use App\Resources\Bid\ItemResource;
use App\Resources\CustomerResource;
use App\Resources\DrawingResource;
use App\Resources\Project\ContactResource;
use App\Resources\Project\EventResource;
use App\Resources\Project\FileResource;
use App\Resources\Project\NoteResource;
use App\Resources\ProjectResource;
use App\Resources\ProjectTypeResource;
use App\Resources\PropertyResource;
use App\Resources\ResultTypeResource;
use App\Resources\TaskResource;
use App\Resources\UserResource;
use App\Services\CompanySettingService;
use App\Services\ReferenceIdentifierService;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\CustomBid;
use Common\Models\Customer;
use Common\Models\CustomerPhone;
use Common\Models\Evaluation;
use Common\Models\EvaluationBid;
use Common\Models\MarketingType;
use Common\Models\Project;
use Common\Models\ProjectCost;
use Common\Models\ProjectEmail;
use Common\Models\Property;
use Common\Models\PunchTime;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\ImmutableRelationException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CollectionRequest;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

class ProjectDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('bid_items')->modelRelation('bidItems')->resource(ItemResource::class);
        $list->oneOrMany('cancelled_by_user')->modelRelation('cancelledByUser')->resource(UserResource::class);
        $list->oneOrMany('contacts')->resource(ContactResource::class);
        $list->oneOrMany('completed_by_user')->modelRelation('completedByUser')->resource(UserResource::class);
        $list->oneOrMany('customer')->resource(CustomerResource::class);
        $list->oneOrMany('drawings')->resource(DrawingResource::class);
        $list->oneOrMany('events')->resource(EventResource::class);
        $list->oneOrMany('files')->resource(FileResource::class);
        $list->oneOrMany('notes')->resource(NoteResource::class);
        $list->oneOrMany('property')->resource(PropertyResource::class);
        $list->oneOrMany('tasks')->resource(TaskResource::class);
        $list->oneOrMany('salesperson_user')->modelRelation('salesperson')->resource(UserResource::class);
        $list->oneOrMany('project_type')->modelRelation('projectType')->resource(ProjectTypeResource::class);
        $list->oneOrMany('result_type')->modelRelation('resultType')->resource(ResultTypeResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('projectID')
            ->noSave();

        $list->field('project_uuid')
            ->typeUuid()
            ->column('projectUUID')
            ->immutable()
            ->enableAction(ProjectResource::ACTION_FILTER);

        $list->field('reference_id')
            ->label('Reference ID')
            ->column('referenceID')
            ->immutable()
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('property_id')
            ->column('propertyID', true)
            ->validation('Property Id', 'required|type[int]|check_property_id')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('customer_id')
            ->column('customerID', true)
            ->validation('Customer Id', 'required|type[int]|check_customer_id');

        // @todo look into creating an additional on demand field in the future to pull business name separately
        $list->field('customer_name')
            ->onDemand()
            ->label('Customer Name')
            ->rawColumn('IF(customer.businessName IS NOT NULL, CONCAT(customer.firstName, \' \', customer.lastName, \' (\', customer.businessName, \')\'), CONCAT(customer.firstName, \' \', customer.lastName))', 'customer_name')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('business_name')
            ->onDemand()
            ->label('Business Name')
            ->rawColumn('customer.businessName', 'business_name')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('property_address')
            ->onDemand()
            ->label('Property Address')
            ->rawColumn('CONCAT(property.address, \' \', IF(property.address2 IS NOT NULL, CONCAT(property.address2, \' \'), \'\'), property.city, \', \', property.state, \' \', property.zip)', 'property_address')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('description')
            ->column('projectDescription')
            ->label('Description')
            ->validationRules('trim|required|max_length[250]');

        $list->field('salesperson_user_id')
            ->column('projectSalesperson', true)
            ->validation('Salesperson User Id', 'nullable|optional|type[int]|check_salesperson_user_id')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('salesperson_name')
            ->onDemand()
            ->label('Salesperson Name')
            ->query(function ($query) {
                return $query->leftJoin('user as salesperson', 'salesperson.userID', '=', 'project.projectSalesperson');
            })
            ->rawColumn('IF(project.projectSalesperson, CONCAT(salesperson.userFirstName, \' \', salesperson.userLastName), null)', 'salesperson_name')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('is_financing_enabled')
            ->column('isFinancingEnabled')
            ->label('Financing Enabled')
            ->validationRules('optional|type[bool]')
            ->enableAction(ProjectResource::ACTION_SORT)
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $list->field('referral_marketing_type_id')
            ->column('referralMarketingTypeID')
            ->validation('Referral Marketing Type Id', 'nullable|optional|type[int]|check_marketing_type_id')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('secondary_marketing_type_id')
            ->column('secondaryMarketingTypeID')
            ->validation('Secondary Marketing Type Id', 'nullable|optional|type[int]|check_marketing_type_id')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('marketing_source')
            ->onDemand()
            ->label('Primary Marketing Source')
            ->query(function ($query) {
                return $query->leftJoin('marketingType', 'marketingType.marketingTypeID', '=', 'project.referralMarketingTypeID');
            })
            ->rawColumn('marketingType.marketingTypeName', 'marketing_source')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('secondary_marketing_source')
            ->onDemand()
            ->label('Secondary Marketing Source')
            ->query(function ($query) {
                return $query->leftJoin('marketingType as secondary', 'secondary.marketingTypeID', '=', 'project.secondaryMarketingTypeID');
            })
            ->rawColumn('secondary.marketingTypeName', 'secondary_marketing_source')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('status')
            ->label('Status')
            ->requireColumn()
            ->validationRules('required|type[int]|in_array[statuses]|check_status')
            ->onAction([ProjectResource::ACTION_CREATE, ProjectResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->disable();
            })
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $status_names = ProjectResource::getStatusNames();
        $list->field('status_name')
            ->onDemand()
            ->label('Status')
            ->value(function (Project $project) use ($status_names) {
                return $status_names[$project->status];
            });

        $list->field('priority')
            ->label('Priority')
            ->requireColumn()
            ->validation('Priority', 'nullable|optional|type[int]|in_array[priorities]')
            ->enableAction(ProjectResource::ACTION_SORT)
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $priority_names = ProjectResource::getPriorityNames();
        $list->field('priority_name')
            ->onDemand()
            ->label('Priority')
            ->value(function (Project $project) use ($priority_names) {
                return $priority_names[$project->priority];
            });

        $list->field('type')
            ->typeUuid()
            ->label('Type')
            ->validationRules('optional|uuid|check_project_type_id')
            ->enableAction(ProjectResource::ACTION_SORT)
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('type_name')
            ->onDemand()
            ->label('Type')
            ->query(function ($query) {
                return $query->leftJoin('projectTypes', 'projectTypes.projectTypeID', '=', 'project.type');
            })
            ->rawColumn('projectTypes.name', 'type_name')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('summary')
            ->validation('Summary', 'trim|nullable|optional|max_length[10000]');

        $list->field('result_type_id')
            ->typeUuid()
            ->column('resultTypeID')
            ->label('Result Type')
            ->validationRules('optional|uuid|check_result_type_id')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('result_type_name')
            ->onDemand()
            ->label('Result')
            ->query(function ($query) {
                return $query->leftJoin('resultTypes', 'resultTypes.resultTypeID', '=', 'project.resultTypeID');
            })
            ->rawColumn('resultTypes.name', 'result_type_name')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('first_sales_appointment')
            ->onDemand()
            ->label('First Sales Appointment')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT firstApt.projectID, scheduledStart AS first_appt
                    FROM `projectSchedule` AS firstApt
                    WHERE 
                    firstApt.projectScheduleID = (
                        SELECT MIN(projectScheduleID) FROM projectSchedule
                        WHERE projectSchedule.projectID = firstApt.projectID AND
                        projectSchedule.scheduleType = "Evaluation" AND
                         projectSchedule.status IN (1,4)
                    )  
                    GROUP BY firstApt.projectID)
                    firstSalesApt'), function($join) {
                        $join->on('project.projectID', '=', 'firstSalesApt.projectID');
                });
                return $query;
            })
            ->rawColumn('firstSalesApt.first_appt', 'first_sales_appointment')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            })
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('has_sales_appointment')
            ->onDemand()
            ->label('Has Sales Appointment')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT hasSalesApt.projectID, count(hasSalesApt.projectID) AS first_appt
                    FROM `projectSchedule` AS hasSalesApt
                    WHERE 
                    hasSalesApt.projectScheduleID = (
                        SELECT MIN(projectScheduleID) FROM projectSchedule
                        WHERE projectSchedule.projectID = hasSalesApt.projectID AND
                        projectSchedule.scheduleType = "Evaluation" AND
                        projectSchedule.status IN (1,4)
                    )
                    GROUP BY hasSalesApt.projectID)
                    hasFirstSalesApt'), function($join) {
                    $join->on('project.projectID', '=', 'hasFirstSalesApt.projectID');
                });
                return $query;
            })
            ->rawColumn('IF(hasFirstSalesApt.first_appt > 0, 1, 0)', 'has_sales_appointment')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('total_bids_sent')
            ->onDemand()
            ->label('Total Bids Sent')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT sentBids.projectID, COUNT(sentBids.evaluationID) AS total
                    FROM `evaluation` AS sentBids
                    WHERE 
                    sentBids.deletedAt IS NULL AND 
                    sentBids.evaluationFinalized IS NOT NULL AND 
                    sentBids.evaluationCancelled IS NULL
                    GROUP BY sentBids.projectID)
                    totalBidsSent'), function($join) {
                        $join->on('project.projectID', '=', 'totalBidsSent.projectID');
                });
                return $query;
            })
            ->rawColumn('totalBidsSent.total', 'total_bids_sent')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('has_bids_sent')
            ->onDemand()
            ->label('Has Bids Sent')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT hasSentBids.projectID, COUNT(hasSentBids.evaluationID) AS total
                    FROM `evaluation` AS hasSentBids
                    WHERE 
                    hasSentBids.deletedAt IS NULL AND 
                    hasSentBids.evaluationFinalized IS NOT NULL AND 
                    hasSentBids.evaluationCancelled IS NULL
                    GROUP BY hasSentBids.projectID)
                    hasBidsSent'), function($join) {
                    $join->on('project.projectID', '=', 'hasBidsSent.projectID');
                });
                return $query;
            })
            ->rawColumn('IF(hasBidsSent.total > 0, 1, 0)', 'has_bids_sent')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('total_bids_accepted')
            ->onDemand()
            ->label('Total Bids Accepted')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT acceptedBids.projectID, COUNT(acceptedBids.evaluationID) AS total
                    FROM `evaluation` AS acceptedBids
                    JOIN `customBid` AS cb ON cb.evaluationID = acceptedBids.evaluationID
                    WHERE 
                    acceptedBids.deletedAt IS NULL AND 
                    acceptedBids.evaluationFinalized IS NOT NULL AND 
                    acceptedBids.evaluationCancelled IS NULL AND 
                    cb.bidAccepted IS NOT NULL
                    GROUP BY acceptedBids.projectID)
                    totalBidsAccepted'), function($join) {
                        $join->on('project.projectID', '=', 'totalBidsAccepted.projectID');
                });
            })
            ->rawColumn('totalBidsAccepted.total', 'total_bids_accepted')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('has_bids_accepted')
            ->onDemand()
            ->label('Has Bids Accepted')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT hasAcceptedBids.projectID, COUNT(hasAcceptedBids.evaluationID) AS total
                    FROM `evaluation` AS hasAcceptedBids
                    JOIN `customBid` AS acb ON acb.evaluationID = hasAcceptedBids.evaluationID
                    WHERE 
                    hasAcceptedBids.deletedAt IS NULL AND 
                    hasAcceptedBids.evaluationFinalized IS NOT NULL AND 
                    hasAcceptedBids.evaluationCancelled IS NULL AND 
                    acb.bidAccepted IS NOT NULL
                    GROUP BY hasAcceptedBids.projectID)
                    hasBidsAccepted'), function($join) {
                    $join->on('project.projectID', '=', 'hasBidsAccepted.projectID');
                });
                return $query;
            })
            ->rawColumn('IF(hasBidsAccepted.total > 0, 1, 0)', 'has_bids_accepted')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('total_bids_accepted_value')
            ->onDemand()
            ->label('Total Bids Accepted Value')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT acceptedBidsValue.projectID, SUM(IF(cb.bidTotal IS NULL, 0, cb.bidTotal + IFNULL(cb.bidScopeChangeTotal, 0))) AS total
                    FROM `evaluation` AS acceptedBidsValue
                    JOIN `customBid` AS cb ON cb.evaluationID = acceptedBidsValue.evaluationID
                    WHERE 
                    acceptedBidsValue.deletedAt IS NULL AND 
                    acceptedBidsValue.evaluationFinalized IS NOT NULL AND 
                    acceptedBidsValue.evaluationCancelled IS NULL AND 
                    cb.bidAccepted IS NOT NULL
                    GROUP BY acceptedBidsValue.projectID)
                    totalBidsAcceptedValue'), function($join) {
                        $join->on('project.projectID', '=', 'totalBidsAcceptedValue.projectID');
                });
            })
            ->rawColumn('totalBidsAcceptedValue.total', 'total_bids_accepted_value')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('first_bid_accepted')
            ->onDemand()
            ->label('First Bid Accepted')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT acceptedBid.projectID, bidAccepted
                    FROM `evaluation` AS acceptedBid
                    JOIN `customBid` AS cb ON cb.evaluationID = acceptedBid.evaluationID
                    WHERE (
                        SELECT MIN(evaluation.evaluationID)
                        FROM evaluation
                        JOIN customBid AS cb ON cb.evaluationID = evaluation.evaluationID
                        WHERE 
                        evaluation.projectID = acceptedBid.projectID AND
                        acceptedBid.deletedAt IS NULL AND 
                        acceptedBid.evaluationFinalized IS NOT NULL AND 
                        acceptedBid.evaluationCancelled IS NULL AND 
                        cb.bidAccepted IS NOT NULL
                        GROUP BY evaluation.projectID
                    )
                    GROUP BY acceptedBid.projectID)
                    firstBidAccepted'), function($join) {
                    $join->on('project.projectID', '=', 'firstBidAccepted.projectID');
                });
            })
            ->rawColumn('firstBidAccepted.bidAccepted', 'first_bid_accepted')
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('customer_name')
            ->onDemand()
            ->label('Customer Name')
            ->query(function ($query) {
                $query->leftJoin('property as p', 'p.propertyID', '=', 'project.propertyID');
                $query->leftJoin('customer as c', 'c.customerID', '=', 'p.customerID');
                return $query;
            })
            ->rawColumn('CONCAT(c.firstName, \' \', c.lastName)', 'customer_name');

        $list->field('completed_on')
            ->typeDateTime()
            ->column('projectCompleted')
            ->label('Closed At')
            ->validationRules('ignored_if[is_not_completed]|required|iso8601_date:datetime|to_carbon|to_utc')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            })
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('completed_at')
            ->typeDateTime()
            ->column('completedAt')
            ->label('Completed At')
            ->immutable()
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('completed_by_user_id')
            ->column('projectCompletedByID', true)
            ->immutable()
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('cancelled_on')
            ->typeDateTime()
            ->column('projectCancelled')
            ->label('Cancelled On')
            ->validationRules('ignored_if[is_not_cancelled]|required|iso8601_date:datetime|to_carbon|to_utc')
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            })
            ->onAction(ProjectResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('cancelled_at')
            ->typeDateTime()
            ->column('cancelledAt')
            ->label('Cancelled At')
            ->immutable()
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('cancelled_by_user_id')
            ->column('projectCancelledByID', true)
            ->immutable()
            ->onAction(ProjectResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'description', 'status'
        ], function (Field $field) {
            return $field->enableAction(ProjectResource::ACTION_SORT);
        });

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('project_overview')
            ->urlSlug('project-overview')
            ->versions(function (MediaType $type) {
                $type->original()->handler(OverviewHandler::class);
            });
        return $list;
    }

    public function validationRules(Rules $rules, ProjectResource $resource)
    {
        $rules->register('check_property_id', function ($id) use ($resource) {
            if ($resource->relationResource('property')->entityExists($id)) {
                return true;
            }
            return 'check_property_id';
        }, [
            'check_property_id' => 'Unable to find property'
        ]);

        $rules->register('check_customer_id', function ($id) use ($resource) {
            if ($resource->relationResource('customer')->entityExists($id)) {
                return true;
            }
            return 'check_customer_id';
        }, [
            'check_customer_id' => 'Unable to find customer'
        ]);

        $rules->register('check_project_type_id', function ($id) use ($resource) {
            if ($resource->relationResource('project_type')->entityExists($id)) {
                return true;
            }
            return 'check_project_type_id';
        }, [
            'check_project_type_id' => 'Unable to find project type'
        ]);

        $rules->register('check_result_type_id', function ($id) use ($resource) {
            if ($resource->relationResource('result_type')->entityExists($id)) {
                return true;
            }
            return 'check_result_type_id';
        }, [
            'check_result_type_id' => 'Unable to find result types'
        ]);

        $rules->register('check_salesperson_user_id', function ($id) use ($resource) {
            if ($resource->relationResource('salesperson_user')->entityExists($id)) {
                return true;
            }
            return 'check_salesperson_user_id';
        }, [
            'check_salesperson_user_id' => 'Unable to find salesperson user'
        ]);

        $rules->register('check_marketing_type_id', function ($id) use ($resource) {
            $query = MarketingType::query();
            $user = $resource->acl()->user();
            if ($user !== null) {
                $query->where('companyID', $user->companyID);
            }
            if ($query->where('marketingTypeID', $id)->count() !== 0) {
                return true;
            }
            return 'check_marketing_type_id';
        }, [
            'check_marketing_type_id' => 'Unable to find marketing type'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [ProjectResource::STATUS_ACTIVE],
                ProjectResource::STATUS_ACTIVE => [ProjectResource::STATUS_CANCELLED, ProjectResource::STATUS_CLOSED],
                ProjectResource::STATUS_CANCELLED => [ProjectResource::STATUS_ACTIVE],
                ProjectResource::STATUS_CLOSED => [ProjectResource::STATUS_ACTIVE]
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', ProjectResource::getStatuses());
        $config->store('priorities', ProjectResource::getPriorities());

        $config->store('is_not_completed', function ($field, Validator $validator) {
            return $validator->data('status') !== ProjectResource::STATUS_CLOSED;
        });
        $config->store('is_not_cancelled', function ($field, Validator $validator) {
            return $validator->data('status') !== ProjectResource::STATUS_CANCELLED;
        });

        $config->store('is_not_active', function ($field, Validator $validator) {
            return $validator->data('status') !== ProjectResource::STATUS_ACTIVE;
        });

        return $config;
    }

    public function requestValidationFields(FieldList $list, Request $request)
    {
        $is_creating = $request->isAction(ProjectResource::ACTION_CREATE | ProjectResource::ACTION_NESTED_CREATE);

        $list->field('contacts')
            ->validation('Contacts', 'nullable|optional|type[array]' . ($is_creating ? '|min_count[1]' : ''));

        $user = $request->resource()->acl()->user();
        if ($user) {
            $setting_service = new CompanySettingService($user->companyID);

            if ($list->has('referral_marketing_type_id')) {
                if ($setting_service->get('marketing_source_required', false) && $is_creating) {
                    $list->modify('referral_marketing_type_id', function (Field $field) {
                        return $field->validationRules('required|type[int]|check_marketing_type_id');
                    });
                }
            }
        }

        return $list;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $id_service = new ReferenceIdentifierService($request->resource()->acl()->companyID(), ReferenceIdentifierService::TYPE_PROJECT);
        $user = $request->resource()->acl()->user();
        $model_data['referenceID'] = $id_service->getNext();
        $model_data['projectUUID'] = Uuid::uuid4()->getBytes();
        $model_data['status'] = Project::STATUS_ACTIVE;

        $model_data['isFinancingEnabled'] = false;

        // Create company command doesn't have a user set.
        if ($user) {
            $setting_service = new CompanySettingService($user->companyID);
            if ($setting_service->get('wisetack_project_financing_enabled_by_default', false)) {
                $model_data['isFinancingEnabled'] = true;
            }

            if ($model_data['projectSalesperson'] !== null && $user && $model_data['projectSalesperson'] !== $user->userID) {
                $request->store('send_assigned_notification', true);
            }

            // check if project type is required when creating a project
            // if it is required and it's not set then we need to throw a validation error
            if ($setting_service->get('project_type_required', false) && (!isset($model_data['type']) || $model_data['type'] === null)) {
                throw new ValidationException('Project type is required');
            }
        }

        return $model_data;
    }

    /**
     * @throws AppException
     * @throws ValidationException
     */
    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $model = $request->getModel();
        $user = $request->resource()->acl()->user();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            $status_columns = [
                Project::STATUS_CANCELLED => [
                    'time_on' => 'projectCancelled',
                    'time_at' => 'cancelledAt',
                    'user' => 'projectCancelledByID'
                ],
                Project::STATUS_CLOSED => [
                    'time_on' => 'projectCompleted',
                    'time_at' => 'completedAt',
                    'user' => 'projectCompletedByID'
                ]
            ];
            switch ($model_data['status']) {
                case Project::STATUS_ACTIVE:
                    // clear out columns
                    foreach ($status_columns as $columns) {
                        $model_data[$columns['time_on']] = null;
                        $model_data[$columns['time_at']] = null;
                        $model_data[$columns['user']] = null;
                    }
                    $model_data['resultTypeID'] = null;
                    break;
                case Project::STATUS_CANCELLED:
                case Project::STATUS_CLOSED:
                    $columns = $status_columns[$model_data['status']];
                    $model_data[$columns['time_at']] = Carbon::now('UTC');
                    if ($user !== null) {
                        $model_data[$columns['user']] = $user->getKey();
                    }
                    break;
            }
            $request->store('cancelling', $model_data['status'] === Project::STATUS_CANCELLED);
            $request->store('closing', $model_data['status'] === Project::STATUS_CLOSED);

            // check if result type is required when cancelling and closing a project
            // if it is required and it's not set then we need to throw a validation error
            if ($model_data['status'] === Project::STATUS_CANCELLED || $model_data['status'] === Project::STATUS_CLOSED) {
                $setting_service = new CompanySettingService($user->companyID);
                if ($setting_service->get('result_required', false) && (!isset($model_data['resultTypeID']) || $model_data['resultTypeID'] === null)) {
                    throw new ValidationException('Result type is required');
                }
            }
        } else {
            // check if project type is required when saving a project and the status isn't changing
            // if it is required and it's not set then we need to throw a validation error
            $setting_service = new CompanySettingService($user->companyID);
            if ($setting_service->get('project_type_required', false) && (!isset($model_data['type']) || $model_data['type'] === null)) {
                throw new ValidationException('Project type is required');
            }
        }
        if (
            $user !== null &&
            isset($model_data['projectSalesperson']) &&
            $model_data['projectSalesperson'] !== $model->projectSalesperson &&
            $model_data['projectSalesperson'] !== $user->userID
        ) {
            $request->store('send_assigned_notification', true);
        }
        return $model_data;
    }

    /**
     * Before delete is saved, we delete any related entities using their respective resources
     *
     * @param DeleteRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Exceptions\AppException
     */
    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();

        /** @var Project $model */
        $model = $request->getModel();

        $project_id = $resource->getPrimaryField()->outputValueFromModel($model);

        try {
            /** @var ItemResource $bid_item_resource */
            $bid_item_resource = $resource->relationResource('bid_items');
            $bid_item_resource->deleteByProjectID($project_id);

            /** @var ContactResource $contact_resource */
            $contact_resource = $resource->relationResource('contacts');
            $contact_resource->deleteByProjectID($project_id);

            $model->costs()->with(['cost'])->get()->each(function (ProjectCost $cost) {
                $cost->delete();
            });

            /** @var DrawingResource $drawing_resource */
            $drawing_resource = $resource->relationResource('drawings', 2);
            $drawing_resource->deleteByProjectID($project_id, true);

            // delete any legacy evaluations (evaluations associated with new bidding system are deleted when new bid items are deleted)
            $model->evaluations()->get()->each(function (Evaluation $evaluation) {
                $evaluation->delete();
            });

            /** @var EventResource $event_resource */
            $event_resource = $resource->relationResource('events');
            $event_resource->deleteByProjectID($project_id);

            /** @var FileResource $file_resource */
            $file_resource = $resource->relationResource('files');
            $file_resource->deleteByProjectID($project_id);

            /** @var NoteResource $note_resource */
            $note_resource = $resource->relationResource('notes');
            $note_resource->deleteByProjectID($project_id);

            PunchTime::query()->where('projectID', $project_id)->update([
                'projectID' => 0
            ]);
        } catch (ImmutableEntityException | ImmutableRelationException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    protected function createNestedContacts(CreateRequest $request)
    {
        $contacts = $request->getValidatedEntity()->get('contacts', []);
        if ($contacts === null || count($contacts) === 0) {
            return;
        }
        try {
            $project_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            $request->resource()->relationResource('contacts')
                ->batchCreate(Collection::fromArray($contacts))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($project_id) {
                    $entity->set('project_id', $project_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create contacts - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'contacts' => $errors
                ]);
            }
            throw $exception;
        }
    }

    /**
     * @throws AppException
     * @throws ValidationException
     */
    public function anyCreateSaveAfter(CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        $model_data = $request->getModel();

        if (!empty($user->companyID)) {
            $setting_service = new CompanySettingService($user->companyID);
            if ($setting_service->get('project_type_required', false) && (!isset($model_data['type']) || $model_data['type'] === null)) {
                throw new ValidationException('Project type is required');
            }
        }

        $this->createNestedContacts($request);
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            ProjectAssignmentNotificationJob::enqueue($model->getKey());
        }
    }

    protected function updateNestedContacts($project_id, UpdateRequest $request)
    {
        $contacts = $request->getValidatedEntity()->get('contacts');
        if ($contacts === null) {
            return;
        }
        try {
            /** @var \App\Resources\Project\ContactResource $contact_resource */
            $contact_resource = $request->resource()->relationResource('contacts');
            $ids = [];
            if (count($contacts) > 0) {
                $ids = $contact_resource->batchUpdateOrCreate(Collection::fromArray($contacts))
                    ->nested()
                    ->attach('entity', function (Entity $entity) use ($project_id) {
                        $entity->set('project_id', $project_id);
                        return $entity;
                    })
                    ->run();
            }
            $contact_resource->deleteMissingContactsByProjectID($project_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update contacts - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'contacts' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function cancelEvaluations($project_id, UpdateRequest $request)
    {
        $user = $request->resource()->acl()->user();

        /** @var ItemResource $bid_item_resource */
        $bid_item_resource = $request->resource()->relationResource('bid_items');

        $evaluations = Evaluation::where('projectID', $project_id)
            ->leftJoin('evaluationBid', 'evaluationBid.evaluationID', '=', 'evaluation.evaluationID')
            ->leftJoin('customBid', 'customBid.evaluationID', '=', 'evaluation.evaluationID')
            ->whereNull('evaluationCancelled')
            ->whereNull('evaluationBid.bidAccepted')
            ->whereNull('customBid.bidAccepted')
            ->get('evaluation.*');
        foreach ($evaluations as $evaluation) {
            $evaluation->evaluationCancelled = Carbon::now('UTC');
            $evaluation->evaluationCancelledByID = $user !== null ? $user->getKey() : null;
            $evaluation->save();
            if ($evaluation->bidItemID !== null) {
                $bid_item_resource->partialUpdate(Entity::make([
                    'id' => Uuid::fromBytes($evaluation->bidItemID)->toString(),
                    'status' => ItemResource::STATUS_CANCELLED
                ]))->force(true)->findConfig(['check_mutability' => false])->run();
            }
        }
    }

    protected function disableBidFollowUps($project_id, UpdateRequest $request)
    {
        /** @var ItemResource $bid_item_resource */
        $bid_item_resource = $request->resource()->relationResource('bid_items');
        $bid_item_resource->disableFollowUpsByProjectID($project_id);
    }

    protected function cancelFutureEvents($project_id, UpdateRequest $request)
    {
        /** @var EventResource $event_resource */
        $event_resource = $request->resource()->relationResource('events');
        $event_resource->cancelFutureEventsByProjectID($project_id);
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        $project_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
        $this->updateNestedContacts($project_id, $request);

        // if user is cancelling or closing the project, then we run extra code to cleanup related data
        if ($request->storage('cancelling', false)) {
            $this->cancelEvaluations($project_id, $request);
            $this->disableBidFollowUps($project_id, $request);
            $this->cancelFutureEvents($project_id, $request);
        } else if ($request->storage('closing', false)) {
            $this->disableBidFollowUps($project_id, $request);
        } else {
            $request->store('calendar_push', true);
        }
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            ProjectAssignmentNotificationJob::enqueue($model->getKey());
        }
    }

    public function deleteSaveAfter(DeleteRequest $request)
    {
        $resource = $request->resource();
        $project_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        $resource->relationResource('tasks')->detachAssociation($project_id);
    }

    /**
     * Close request
     *
     * If a calendar push is required, we execute it. It's handled via close so it handles atomic batch requests properly.
     * This method is only called if the batch finishes successfully.
     *
     * @param Request $request
     */
    public function close(Request $request): void
    {
        if ($request->storage('calendar_push', false)) {
            EventResource::make($request->resource()->acl())->pushToCalendarByProjectID($request->getModel()->getKey());
        }
    }

    public function queryScopeGlobal($query, ProjectResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        $setting_service = new CompanySettingService($user->companyID);
        if (!$setting_service->get('project_sales_collaboration', false) && !$user->primary && !$user->projectManagement && !$user->installation && $user->sales) {
            return $query->ofUser($user);
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param ProjectResource $resource
     * @return bool
     */
    public function actionAllowed($action, ProjectResource $resource)
    {
        if (in_array($action, [ProjectResource::ACTION_SORT, ProjectResource::ACTION_GROUP_READ_ONLY_FULL, ProjectResource::ACTION_GET_COLLECTION, ProjectResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        // if user is project management or sales, then we only allow them to do non-delete actions
        if (($user->projectManagement || $user->sales) && ($action & ProjectResource::ACTION_GROUP_DELETE) === 0) {
            return true;
        }
        return false;
    }

    /**
     * Get count of all sent and/or accepted bids which are not cancelled for a specific project
     *
     * @param int $project_id
     * @return int
     */
    protected function getSentOrAcceptedNonCancelledEvaluationCount($project_id)
    {
        $evaluation_bid = EvaluationBid::query()
            ->select([DB::raw('COUNT(*) as count')])
            ->join('evaluation', 'evaluation.evaluationID', '=', 'evaluationBid.evaluationID')
            ->where('evaluation.projectID', $project_id)
            ->where(function ($query) {
                $query->whereNotNull('bidFirstSent')->orWhereNotNull('bidAccepted');
            })
            ->whereNull('evaluation.evaluationCancelled')
            ->whereNull('evaluation.deletedAt');

        $custom_bid = CustomBid::query()
            ->select([DB::raw('COUNT(*) as count')])
            ->join('evaluation', 'evaluation.evaluationID', '=', 'customBid.evaluationID')
            ->where('evaluation.projectID', $project_id)
            ->where(function ($query) {
                $query->whereNotNull('bidFirstSent')->orWhereNotNull('bidAccepted');
            })
            ->whereNull('evaluation.evaluationCancelled')
            ->whereNull('evaluation.deletedAt')
            ->union($evaluation_bid);

        return DB::table(DB::raw("({$custom_bid->toSql()}) as evaluations"))->mergeBindings($custom_bid->getQuery())
            ->sum('count');
    }

    /**
     * Determine if specific model is available for deletion
     *
     * @param Project $model
     * @param ProjectResource $resource
     * @return bool
     * @throws ImmutableEntityException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function modelIsDeletable(Project $model, ProjectResource $resource)
    {
        $project_id = $model->getKey();

        /** @var EventResource $event_resource */
        $event_resource = $resource->relationResource('events');
        if ($event_resource->hasActiveEventsForProjectID($project_id)) {
            throw new ImmutableEntityException('Cannot delete project with active events');
        }

        if ($this->getSentOrAcceptedNonCancelledEvaluationCount($project_id) > 0) {
            throw new ImmutableEntityException('Cannot delete project with sent or accepted bids');
        }
        return true;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && in_array($scope->getFormat(), ['list-v1', 'drawing-v2', 'task-v1', 'collection-v1', 'collection-limited-v1', 'export-v1'])) {
            $resource = $request->resource();
            $term = $scope->getSearch();

            $contacts_query = $resource->newScopedQuery()
                // customer joined in global scope
                ->join('projectEmail', 'projectEmail.projectID', '=', 'project.projectID');
            (new ProjectEmail())->scopeSearch($contacts_query, $term);
            $request->unionQuery($contacts_query);

            $property_query = $resource->newScopedQuery();
            // property joined in global scope
            (new Property())->scopeSearch($property_query, $term);
            $request->unionQuery($property_query);

            $customer_query = $resource->newScopedQuery();
            // customer joined in global scope
            (new Customer())->scopeSearch($customer_query, $term);
            $request->unionQuery($customer_query);

            $customer_phone_query = $resource->newScopedQuery()
                // customer joined in global scope
                ->join('customerPhone', 'customerPhone.customerID', '=', 'customer.customerID');
            (new CustomerPhone())->scopeSearch($customer_phone_query, $term);
            $request->unionQuery($customer_phone_query);

            $request->enableChunking()->chunkLimit(100);
        }
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
                $scope->fields(['id', 'description', 'is_financing_enabled'], true);
                $scope->with(['drawings']);
                break;
            case 'collection-v1':
            case 'collection-limited-v1':
                $scope_fields = [
                    'id', 'reference_id', 'property_id', 'description', 'property_address', 'status', 'priority',
                    'salesperson_name', 'marketing_source', 'secondary_marketing_source', 'result_type_id', 'type',
                    'result_type_name', 'type_name', 'created_at', 'created_by_user_name', 'updated_at',
                    'updated_by_user_name', 'is_financing_enabled', 'cancelled_on', 'completed_on'
                ];
                if ($scope->getFormat() === 'collection-v1') {
                    array_push($scope_fields, 'total_bids_sent', 'total_bids_accepted', 'total_bids_accepted_value',
                        'first_sales_appointment', 'first_bid_accepted', 'has_bids_sent', 'has_bids_accepted', 'has_sales_appointment');
                }
                $scope->fields($scope_fields);
                $scope->with([
                    'property' => [
                       'fields' => ['customer_id'],
                       'with' => [
                           'customer' => [
                               'fields' => ['id', 'first_name', 'last_name', 'business_name']
                           ]
                       ]
                   ]
                ]);
                break;
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                $scope->fields(['id', 'description'], true);
                $scope->with(['drawings', 'property']);
                $scope->query(function ($query) {
                    return $query->withTrashed();
                });
                break;
            case 'export-v1':
                $scope->fields([
                    'reference_id', 'customer_name', 'property_address', 'salesperson_name', 'description',
                    'type_name', 'priority_name', 'status_name', 'result_type_name',
                    'marketing_source', 'secondary_marketing_source', 'summary',
                    'total_bids_sent', 'total_bids_accepted', 'total_bids_accepted_value',
                    'first_sales_appointment', 'first_bid_accepted', 'has_bids_sent', 'has_bids_accepted',
                    'has_sales_appointment', 'completed_on', 'cancelled_on', 'created_at', 'created_by_user_name'
                ], true);
                break;
            case 'list-v1':
                $scope->fields(['id', 'project_uuid', 'customer_name', 'description'], true);
                break;
            case 'info-v1':
                $scope->fields([
                    'reference_id', 'description', 'salesperson_user_id', 'referral_marketing_type_id', 'secondary_marketing_type_id',
                    'is_financing_enabled', 'marketing_source', 'secondary_marketing_source', 'status', 'completed_on', 'cancelled_on',
                    'summary', 'priority', 'type', 'result_type_id',
                ])
                ->with([
                    'result_type' => [
                        'fields' => ['name']
                    ],
                    'project_type' => [
                        'fields' => ['name']
                    ],
                    'cancelled_by_user' => [
                        'fields' => ['first_name', 'last_name']
                    ],
                    'completed_by_user' => [
                        'fields' => ['first_name', 'last_name']
                    ],
                    'contacts' => [
                        'fields' => ['name', 'email', 'phone_number']
                    ],
                    'customer' => [
                        'fields' => ['id', 'first_name', 'last_name']
                    ],
                    'project_overview_media_urls' => [],
                    'property' => [],
                    'salesperson_user' => [
                        'fields' => ['id', 'first_name', 'last_name', 'is_active']
                    ]
                ]);
                break;
            case 'drawing-v2':
            case 'drawing-v2-search':
            case 'task-v1':
                $scope->fields([
                    'id', 'reference_id', 'property_id', 'description', 'customer_name', 'status',
                    'salesperson_name', 'marketing_source', 'secondary_marketing_source', 'created_at', 'created_by_user_name'
                ], true);
                break;
        }
    }
}
