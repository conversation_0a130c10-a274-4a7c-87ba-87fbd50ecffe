<?php

namespace App\ResourceDelegates\Project;

use App\NotificationJobs\Project\Event\CreatedNotificationJob;
use App\NotificationJobs\User\ProjectEventCreatedNotificationJob;
use App\ResourceJobs\Project\Event\CalendarDeleteJob;
use App\ResourceJobs\Project\Event\CalendarPushJob;
use App\Resources\Project\EventResource;
use App\Resources\ProjectResource;
use App\Resources\TaskResource;
use App\Resources\UserResource;
use App\Services\CompanySettingService;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\Customer;
use Common\Models\CustomerPhone;
use Common\Models\Project;
use Common\Models\ProjectSchedule;
use Common\Models\Property;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Classes\ScopeBuilder;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Requests\CollectionRequest;
use Core\Components\Resource\Exceptions\RelationNotFoundException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

class EventDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('project')->resource(ProjectResource::class);
        $list->oneOrMany('scheduled_user')->modelRelation('scheduledUser')->resource(UserResource::class);
        $list->oneOrMany('tasks')->resource(TaskResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('projectScheduleID')
            ->noSave()
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('uuid')
            ->typeUuid()
            ->onDemand()
            ->column('projectScheduleUUID')
            ->immutable();

        $list->field('project_id')
            ->column('projectID', true)
            ->validation('Project Id', 'required|type[int]|check_project_id')
            ->onAction(EventResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]|check_status')
            ->onAction([EventResource::ACTION_CREATE, EventResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->disable();
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('source')
            ->validation('Source', 'nullable|optional|type[int]|in_array[sources]')
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric|in_array[sources]')->mutable();
            })
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $type_map = EventResource::getTypeMap();
        $list->field('type')
            ->column('scheduleType')
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->immutable()
            ->onAction([EventResource::ACTION_CREATE, EventResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->mutable();
            })
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric|in_array[types]')->mutable();
            })
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('user_id')
            ->column('scheduledUserID', true)
            ->validation('User Id', 'required|type[int]|check_user_id')
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('scheduled_user_name')
            ->onDemand()
            ->label('Scheduled User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as scheduled_user', 'scheduled_user.userID', '=', 'projectSchedule.scheduledUserID');
            })
            ->rawColumn('IF(projectSchedule.scheduledUserID, CONCAT(scheduled_user.userFirstName, \' \', scheduled_user.userLastName), null)', 'scheduled_user_name')
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('start')
            ->typeDateTime()
            ->rawColumn('CONVERT_TZ(projectSchedule.scheduledStart, timezones.timezone, \'UTC\')', 'start')
            ->immutable()
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            })
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('start_at')
            ->typeDateTime()
            ->column('scheduledStart')
            ->validation('Start At', 'required|iso8601_date|to_carbon')
            ->enableAction(EventResource::ACTION_SORT)
            ->hide();

        $list->field('end')
            ->typeDateTime()
            ->rawColumn('CONVERT_TZ(projectSchedule.scheduledEnd, timezones.timezone, \'UTC\')', 'end')
            ->immutable()
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            })
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('end_at')
            ->typeDateTime()
            ->column('scheduledEnd')
            ->validation('End', 'required|iso8601_date|to_carbon|date_after:field[start_at]')
            ->hide();

        $list->field('is_all_day')
            ->column('isAllDay')
            ->validation('Is All Day', 'required|type[bool]')
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $list->field('description')
            ->validation('Description', 'trim|nullable|optional|max_length[20000]');

        $list->field('sequence')
            ->onDemand()
            ->immutable();

        $list->field('is_pending')
            ->column('isPending')
            ->validation('Is Pending', 'required|type[bool]|check_pending');

        $list->field('pending_at')
            ->typeDateTime()
            ->column('pendingAt')
            ->immutable();

        $list->field('send_notifications')
            ->column('sendNotifications')
            ->validation('Send Notifications', 'required|type[bool]')
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $list->field('is_calendar_push_in_progress')
            ->column('isCalendarPushInProgress')
            ->immutable();

        $list->field('is_sync_in_progress')
            ->column('isSyncInProgress')
            ->immutable();

        $list->field('completed_at')
            ->typeDateTime()
            ->column('completedAt')
            ->immutable()
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            });

        $list->field('completed_by_user_id')
            ->column('completedByUserID')
            ->immutable();

        $list->field('replaced_at')
            ->typeDateTime()
            ->column('replacedAt')
            ->immutable()
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            });

        $list->field('replaced_by_user_id')
            ->column('replacedByUserID')
            ->immutable();

        $list->field('replaced_by_event_id')
            ->column('replacedByProjectScheduleID')
            ->validation('Replaced By Event Id', 'required_if[is_replaced]|type[int]|check_event_id')
            ->onAction([EventResource::ACTION_CREATE], function (Field $field) {
                return $field->disable();
            });

        $list->field('cancelled_at')
            ->typeDateTime()
            ->column('cancelledAt')
            ->immutable()
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            })
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable();
            });

        $list->field('cancelled_by_user_id')
            ->column('cancelledByUserID')
            ->immutable();

        $list->field('has_bids_accepted')
            ->onDemand()
            ->label('Has Bids Accepted')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT hasAcceptedBids.projectID, COUNT(hasAcceptedBids.evaluationID) AS total
                    FROM `evaluation` AS hasAcceptedBids
                    JOIN `customBid` AS acb ON acb.evaluationID = hasAcceptedBids.evaluationID
                    WHERE 
                    hasAcceptedBids.deletedAt IS NULL AND 
                    hasAcceptedBids.evaluationFinalized IS NOT NULL AND 
                    hasAcceptedBids.evaluationCancelled IS NULL AND 
                    acb.bidAccepted IS NOT NULL
                    GROUP BY hasAcceptedBids.projectID)
                    hasBidsAccepted'), function($join) {
                    $join->on('projectSchedule.projectID', '=', 'hasBidsAccepted.projectID');
                });
            })
            ->rawColumn('IF(hasBidsAccepted.total > 0, 1, 0)', 'has_bids_accepted')
            ->onAction(EventResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('total_bids_accepted_value')
            ->onDemand()
            ->label('Total Bids Accepted Value')
            ->query(function($query) {
                $query->leftJoin(
                    DB::raw('(SELECT acceptedBidsValue.projectID, SUM(IF(cb.bidTotal IS NULL, 0, cb.bidTotal + IFNULL(cb.bidScopeChangeTotal, 0))) AS total
                    FROM `evaluation` AS acceptedBidsValue
                    JOIN `customBid` AS cb ON cb.evaluationID = acceptedBidsValue.evaluationID
                    WHERE 
                    acceptedBidsValue.deletedAt IS NULL AND 
                    acceptedBidsValue.evaluationFinalized IS NOT NULL AND 
                    acceptedBidsValue.evaluationCancelled IS NULL AND 
                    cb.bidAccepted IS NOT NULL
                    GROUP BY acceptedBidsValue.projectID)
                    totalBidsAcceptedValue'), function($join) {
                    $join->on('projectSchedule.projectID', '=', 'totalBidsAcceptedValue.projectID');
                });
            })
            ->rawColumn('totalBidsAcceptedValue.total', 'total_bids_accepted_value')
            ->onAction(EventResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'status', 'source', 'send_notifications', 'is_all_day'
        ], function (Field $field) {
            return $field->enableAction(EventResource::ACTION_SORT);
        });

        return $list;
    }

    public function anyUpdateRequestValidationFields(FieldList $list, UpdateRequest $request)
    {
        $model = $request->getModel();
        // if event is not pending, we don't allow updating some fields since the record is essentially locked
        if (!$model->isPending) {
            // note: user_id is restricted via a rule
            $list->get('send_notifications')->disable();
        }
        return $list;
    }

    public function validationRules(Rules $rules, EventResource $resource)
    {
        $rules->register('check_project_id', function ($id) use ($resource) {
            if ($resource->relationResource('project')->entityExists($id)) {
                return true;
            }
            return 'check_project_id';
        }, [
            'check_project_id' => 'Unable to find project'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [EventResource::STATUS_ACTIVE],
                EventResource::STATUS_ACTIVE => [
                    EventResource::STATUS_CANCELLED, EventResource::STATUS_COMPLETED, EventResource::STATUS_REPLACED
                ],
                EventResource::STATUS_COMPLETED => [ProjectResource::STATUS_ACTIVE],
                // once in replaced or cancelled status, it cannot be changed again
                EventResource::STATUS_CANCELLED => [],
                EventResource::STATUS_REPLACED => []
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        $rules->register('check_user_id', function ($id, $params, Validator $validator) use ($resource) {
            $model = $validator->getConfig()->storage('_model');
            if ($model !== null && !$model->isPending && $model->scheduledUserID !== $id) {
                return 'user_change_disallowed';
            }
            $user = $resource->relationResource('scheduled_user')->find($id);
            if ($user === null) {
                return 'user_not_found';
            }
            // if there is an error with the type field, we stop processing this one since we are dependent on
            // that value
            if ($validator->errors()->has('type')) {
                return Rules::STOP;
            }
            // if this is a create or the scheduled user changed, we verify the user has proper roles
            if ($model === null || $model->scheduledUserID !== $id) {
                $roles = [
                    'sales' => [EventResource::TYPE_EVALUATION],
                    'installation' => [EventResource::TYPE_INSTALLATION]
                ];
                // if model is not available, use input data. otherwise use model value since type field isn't available
                // during updates
                $type = $model === null ? $validator->data('type') : $resource->getFields()->get('type')->outputValueFromModel($model);
                $allowed = false;
                foreach ($roles as $role => $types) {
                    if (!$user[$role] || !in_array($type, $types)) {
                        continue;
                    }
                    $allowed = true;
                    break;
                }
                if (!$allowed) {
                    return 'user_disallowed_type';
                }
            }
            return true;
        }, [
            'user_change_disallowed' => 'User cannot be changed when pending state is disabled',
            'user_not_found' => 'Unable to find user',
            'user_disallowed_type' => 'User cannot be scheduled with this event type due to missing role assignments'
        ]);

        $rules->register('check_pending', function ($pending, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            // pass validation if no model is available (during create) or state didn't change or if user is disabling
            // pending state
            if ($model === null || $model->isPending === $pending || !$pending) {
                return true;
            }
            return 'check_pending';
        }, [
            'check_pending' => 'Pending state cannot be enabled on an existing entity which is already disabled'
        ]);

        $rules->register('check_event_id', function ($id) use ($resource) {
            if ($resource->entityExists($id)) {
                return true;
            }
            return 'check_event_id';
        }, [
            'check_event_id' => 'Unable to find event'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', EventResource::getStatuses());
        $config->store('sources', EventResource::getSources());
        $config->store('types', EventResource::getTypes());
        $config->store('is_replaced', function ($id, Validator $validator) {
            return $validator->data('status') === EventResource::STATUS_REPLACED;
        });
        return $config;
    }

    /**
     * Determine which request actions can be run based on available data and store for close hooks to handle
     *
     * @param array $actions
     * @param array $model_data
     * @param CreateRequest|UpdateRequest $request
     * @return array
     */
    protected function determineCloseActions(array $actions, array $model_data, $request)
    {
        $actions = array_merge([
            'send_notifications' => false,
            'push_calendar_events' => false,
            'remove_calendar_events' => false
        ], $actions);

        // if we should be sending notifications, but user has requested we don't then we forcefully disable notifications
        if ($actions['send_notifications'] && !$model_data['sendNotifications']) {
            $actions['send_notifications'] = false;
        }

        // if we are pushing or removing calendar events, then we set the isCalendarPushInProgress flag to true, this ensures
        // the sync related ui buttons are disabled while the job is processing
        if ($actions['push_calendar_events'] || $actions['remove_calendar_events']) {
            $model_data['isCalendarPushInProgress'] = true;
        }

        $request->store('close_actions', $actions);

        return $model_data;
    }

    /**
     * @param array $model_data
     * @param CreateRequest|UpdateRequest $request
     * @return array
     */
    protected function convertAllDay(array $model_data, $request)
    {
        $entity = $request->getValidatedEntity();
        $model_data['scheduledStart'] = $entity->start_at->copy()->startOfDay()->format('Y-m-d H:i:s');
        $model_data['scheduledEnd'] = $entity->end_at->copy()->endOfDay()->format('Y-m-d H:i:s');
        return $model_data;
    }

    /**
     * @throws RelationNotFoundException
     */
    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        // convert times to start of day and end of day if it's marked as an all day event
        if ($model_data['isAllDay']) {
            $this->convertAllDay($model_data, $request);
        }

        $model_data['projectScheduleUUID'] = Uuid::uuid4()->getBytes();
        $model_data['status'] = ProjectSchedule::STATUS_ACTIVE;
        $model_data['sequence'] = 0;
        $model_data['updateCount'] = 0;

        if ($model_data['isPending']) {
            $model_data['pendingAt'] = Carbon::now('UTC');
        } else {
            // if not in pending state, then we can do close related actions
            $model_data = $this->determineCloseActions([
                'send_notifications' => true,
                'push_calendar_events' => true
            ], $model_data, $request);
        }

        $project_resource = $request->resource()->relationResource('project');
        $project_id = $model_data['projectID'];
        $user = $request->resource()->acl()->user();
        $project = $project_resource->newScopedQuery()->whereKey($project_id)->firstOrFail();

        if ($user && $project->projectSalesperson !== $user->userID) {
            $request->store('send_assigned_notification', true);
        }

        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        $model = $request->getModel();

        $model_data['sequence'] = $model->sequence + 1;

        // convert times to start of day and end of day if it's marked as an all day event
        if ($model_data['isAllDay']) {
            $this->convertAllDay($model_data, $request);
        }

        $is_replacing = false;
        $status_changed = false;
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            $timestamps = [
                ProjectSchedule::STATUS_REPLACED => 'replaced',
                ProjectSchedule::STATUS_CANCELLED => 'cancelled',
                ProjectSchedule::STATUS_COMPLETED => 'completed'
            ];
            if (isset($timestamps[$model_data['status']])) {
                $prefix = $timestamps[$model_data['status']];
                $model_data["{$prefix}At"] = Carbon::now('UTC');
                if ($user !== null) {
                    $model_data["{$prefix}ByUserID"] = $user->getKey();
                }
            }
            switch ($model_data['status']) {
                case ProjectSchedule::STATUS_ACTIVE:
                    // clear out timestamps of other statuses
                    foreach ($timestamps as $status => $prefix) {
                        $model_data["{$prefix}At"] = null;
                        $model_data["{$prefix}ByUserID"] = null;
                    }
                    break;
                case ProjectSchedule::STATUS_REPLACED:
                    $is_replacing = true;
                    break;
            }
            $status_changed = true;
        }
        // if we aren't replacing this record, we force replaced by id to null since we can't prevent it from being
        // filled out currently
        if (!$is_replacing) {
            $model_data['replacedByProjectScheduleID'] = null;
        }

        $pending_changed = isset($model_data['isPending']) && $model_data['isPending'] !== $model->isPending;
        // the only change that can happen is when pending is disabled, so we clear the pending at date field
        if ($pending_changed) {
            $model_data['pendingAt'] = null;
        }
        // if pending hasn't changed and we are currently in pending status, then we increase the update count so we
        // can track internally how many times someone updates while in pending state
        if (!$pending_changed && $model->isPending) {
            $model_data['pendingAt'] = Carbon::now('UTC');
            $model_data['updateCount'] = $model->updateCount + 1;
        }

        $actions = [
            'send_notifications' => false,
            'push_calendar_events' => false,
            'remove_calendar_events' => false
        ];

        // if pending state is disabled, the event is now locked and we can send notifications and push calendar events
        // if it's still active
        if ($pending_changed) {
            if ($model_data['status'] === ProjectSchedule::STATUS_ACTIVE) {
                $actions['send_notifications'] = true;
                $actions['push_calendar_events'] = true;
            }
        } // if pending is already disabled, then we check for status related changes and run necessary updates
        else if (!$model->isPending) {
            // if status didn't change, then we push the event to google to update description or scheduled times if
            // event is currently in active status
            if (!$status_changed) {
                if ($model->status === ProjectSchedule::STATUS_ACTIVE) {
                    $actions['push_calendar_events'] = true;
                }
            } else {
                switch ($model_data['status']) {
                    case ProjectSchedule::STATUS_ACTIVE:
                        // if status is switched back to active, then we push calendar events again
                        $actions['push_calendar_events'] = true;
                        break;
                    case ProjectSchedule::STATUS_CANCELLED:
                    case ProjectSchedule::STATUS_REPLACED:
                        // if status is changed to cancelled, replaced or completed, then we need to remove the event from external
                        // calendars
                        $actions['remove_calendar_events'] = true;
                        break;
                }
            }
        }

        $model_data = $this->determineCloseActions($actions, $model_data, $request);

        return $model_data;
    }

    public function deleteModelDataAfter($model_data, DeleteRequest $request)
    {
        $model_data['sequence'] = $request->getModel()->sequence + 1;
        return $model_data;
    }

    /**
     * Handle actions for create request after successful database save
     *
     * @param CreateRequest $request
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function anyCreateSaveAfter(CreateRequest $request): void
    {
        $model = $request->getModel();

        /** @var ProjectResource $project_resource */
        $project_resource = $request->resource()->relationResource('project');
        $project_id = $model->projectID;
        $project_update = [];

        // if it's a sales appointment and the current project doesn't have a salesperson, we assign the associated
        // user automatically. This ensures they can access the project due to restrictions on projects which limit
        // visibility for certain user roles.
        if (
            $model->scheduleType === ProjectSchedule::TYPE_EVALUATION &&
            $project_resource->isMissingSalesperson($project_id)
        ) {
            $project_update['salesperson_user_id'] = $model->scheduledUserID;
        }

        // if project is not active, we reopen it
        if ($project_resource->isNotActive($project_id)) {
            $project_update['status'] = ProjectResource::STATUS_ACTIVE;
        }

        if (count($project_update) > 0) {
            $project_update['id'] = $project_id;
            $project_resource->partialUpdate(Entity::make($project_update))->run();
        }
    }

    /**
     * Cleanup after successful delete
     *
     * We set the close actions to remove any calendar events if the model status is active or completed
     *
     * @param DeleteRequest $request
     */
    public function deleteSaveAfter(DeleteRequest $request)
    {
        $model = $request->getModel();
        if (in_array($model->status, [ProjectSchedule::STATUS_ACTIVE, ProjectSchedule::STATUS_COMPLETED])) {
            $request->store('close_actions', [
                'send_notifications' => false,
                'push_calendar_events' => false,
                'remove_calendar_events' => true
            ]);
        }
    }

    public function close($request)
    {
        $actions = $request->storage('close_actions');
        if ($actions !== null) {
            $model = $request->getModel();
            // send notifications - we do not have to check if email is allowed to send since this will be determined before close is called
            if ($actions['send_notifications']) {
                CreatedNotificationJob::enqueue($model->getKey());
            }

            if ($request->storage('send_assigned_notification', false)) {
                ProjectEventCreatedNotificationJob::enqueue($model->getKey());
            }

            // push calendar events
            if ($actions['push_calendar_events']) {
                CalendarPushJob::enqueue($model->getKey());
            }

            // remove calendar events
            if ($actions['remove_calendar_events']) {
                CalendarDeleteJob::enqueue($model->getKey());
            }
        }
    }

    public function queryScopeGlobal($query, EventResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        $setting_service = new CompanySettingService($user->companyID);
        // if user is only an install user and install collaboration is off then only show them their events
        if (!$user->primary && !$user->projectManagement && !$setting_service->get('project_install_collaboration', false) && $user->installation) {
            return $query->ofUser($user);
        }
        // if user is only a sales user and sales collaboration is off then only show them their events
        if (!$user->primary && !$user->projectManagement && !$setting_service->get('project_sales_collaboration', false) && $user->sales) {
            return $query->ofUser($user);
        }
        // if user is not a primary or project manager and calendar sharing is off then only show them their own events
        if (!$user->primary && !$user->projectManagement && !$setting_service->get('allow_calendar_sharing', false)) {
            return $query->ofUser($user);
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param EventResource $resource
     * @return bool
     */
    public function actionAllowed($action, EventResource $resource)
    {
        if (in_array($action, [EventResource::ACTION_SORT, EventResource::ACTION_GROUP_READ_ONLY_FULL, EventResource::ACTION_GET_COLLECTION, EventResource::ACTION_GET_ENTITY, EventResource::ACTION_FILTER])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        // if user is project management or sales, then we only allow them to do non-delete actions
        if (($user->projectManagement || $user->sales) && ($action & EventResource::ACTION_GROUP_DELETE) === 0) {
            return true;
        }
        return false;
    }

    /**
     * Determine if specific model is available for deletion
     *
     * @param ProjectSchedule $model
     * @return bool
     * @throws ImmutableEntityException
     */
    public function modelIsDeletable(ProjectSchedule $model)
    {
        if ($model->status === ProjectSchedule::STATUS_ACTIVE) {
            throw new ImmutableEntityException('Cannot delete active events');
        }
        return true;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && in_array($scope->getFormat(), ['collection-v1', 'collection-limited-v1', 'export-v1'])) {
            $resource = $request->resource();
            $term = $scope->getSearch();

            $project_query = $resource->newScopedQuery();
            // project joined in global scope
            (new Project())->scopeSearch($project_query, $term);
            $request->unionQuery($project_query);

            $property_query = $resource->newScopedQuery();
            // property joined in global scope
            (new Property())->scopeSearch($property_query, $term);
            $request->unionQuery($property_query);

            $customer_query = $resource->newScopedQuery();
            // customer joined in global scope-1

            (new Customer())->scopeSearch($customer_query, $term);
            $request->unionQuery($customer_query);

            $customer_phone_query = $resource->newScopedQuery()
                // customer joined in global scope
                ->join('customerPhone', 'customerPhone.customerID', '=', 'customer.customerID');
            (new CustomerPhone())->scopeSearch($customer_phone_query, $term);
            $request->unionQuery($customer_phone_query);

            $request->enableChunking()->chunkLimit(100);
        }
    }

    public function scopeBuildBefore(Scope $scope)
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'collection-v1':
            case 'collection-limited-v1':
                $scope_fields = [
                    'id', 'project_id', 'status', 'source', 'type', 'scheduled_user_name', 'start', 'end', 'is_all_day',
                    'cancelled_at', 'completed_at', 'replaced_at', 'send_notifications', 'created_at',
                    'created_by_user_name', 'updated_at', 'updated_by_user_name'
                ];
                if ($scope->getFormat() === 'collection-v1') {
                    array_push($scope_fields, 'total_bids_accepted_value', 'has_bids_accepted');
                }
                $scope->fields($scope_fields);
                $scope->with(['project']);
                break;
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                if ($format === 'drawing-app-v1-sync') {
                    $now = Carbon::now('UTC');

                    $scope->clearFilters();
                    $scope->filter('start', 'lte', $now->copy()->addWeek()->toDateString(), Field::TYPE_DATE);
                    $scope->filter('end', 'gte', $now->copy()->subWeek()->toDateString(), Field::TYPE_DATE);
                }
                $scope->filter('type', 'eq', EventResource::TYPE_EVALUATION);
                $scope->fields(['id', 'start', 'end', 'user_id', 'cancelled_at'], true);
                $scope->with(['project']);
                $scope->query(function ($query) {
                    $query->addSelect([
                        'projectSchedule.status', 'projectSchedule.deletedAt', 'projectSchedule.replacedAt'
                    ]);
                    return $query->withTrashed();
                });
                $scope->disablePagination();
                break;
        }
    }

    /**
     * Modify individual entity
     *
     * If scope format is related to the drawing app, then we remap some data to match the format the drawing app
     * needs. Currently we have to move the customer data from the property entity to the project since it originally
     * was pulled from the project (which was incorrect). Replaced and deleted entities didn't exist when the app was
     * made, so we have to fill out the cancelled_at field to make the app remove them from event listing.
     *
     * @param Entity $entity
     * @param ScopeBuilder $scope_builder
     * @param ProjectSchedule $model
     * @return Entity
     */
    public function scopeEntity(Entity $entity, ScopeBuilder $scope_builder, ProjectSchedule $model)
    {
        switch ($scope_builder->getScope()->getFormat()) {
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                if ($entity['cancelled_at'] === null) {
                    if ($model->trashed()) {
                        $entity['cancelled_at'] = $model->deletedAt->format('c');
                    } elseif ($model->status === ProjectSchedule::STATUS_REPLACED) {
                        $entity['cancelled_at'] = $model->replacedAt->format('c');
                    }
                }
                $property = $entity->get('project.property');
                $entity['project']['customer'] = $property['customer'];
                unset($property['customer']);
                break;
        }
        return $entity;
    }
}
