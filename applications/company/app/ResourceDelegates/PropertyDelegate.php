<?php

namespace App\ResourceDelegates;

use App\ResourceMediaHandlers\Property\Image\SizeMediumHandler;
use App\ResourceMediaHandlers\Property\ImageHandler;
use App\ResourceMediaHandlers\Property\StreetViewImageHandler;
use App\Resources\CustomerResource;
use App\Resources\FileResource;
use App\Resources\Project\EventResource;
use App\Resources\ProjectResource;
use App\Resources\PropertyResource;
use App\Resources\TaskResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\Customer;
use Common\Models\CustomerPhone;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\ImmutableRelationException;
use Core\Components\Resource\Requests\CollectionRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\PolyUpdateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\Rules;

class PropertyDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('customer')->resource(CustomerResource::class);
        $list->oneOrMany('image')->resource(FileResource::class);
        $list->oneOrMany('projects')->resource(ProjectResource::class);
        $list->oneOrMany('tasks')->resource(TaskResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('propertyID')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->noSave();

        $list->field('customer_id')
            ->column('customerID', true)
            ->validation('Customer ID', 'required|type[int]|check_customer_id')
            ->onAction(PropertyResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('customer_name')
            ->onDemand()
            ->label('Customer Name')
            ->rawColumn('IF(customer.businessName IS NOT NULL, CONCAT(customer.firstName, \' \', customer.lastName, \' (\', customer.businessName, \')\'), CONCAT(customer.firstName, \' \', customer.lastName))', 'customer_name')
            ->onAction(PropertyResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('address')
            ->label('Address')
            ->validationRules('trim|required|max_length[100]');

        $list->field('address_2')
            ->column('address2')
            ->label('Address 2')
            ->validationRules('trim|nullable|optional|max_length[100]');

        $list->field('city')
            ->label('City')
            ->validationRules('trim|required|max_length[50]')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('state')
            ->label('State')
            ->validationRules('trim|required|max_length[15]')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('zip')
            ->label('Zip')
            ->validationRules('trim|required|max_length[12]')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('county')
            ->label('County')
            ->validationRules('trim|nullable|optional|max_length[50]')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('township')
            ->label('Township')
            ->validationRules('trim|nullable|optional|max_length[100]')
            ->onAction(PropertyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('latitude')
            ->validation('Latitude', 'trim|nullable|empty_default[0.0]|coordinate[latitude]');

        $list->field('longitude')
            ->validation('Longitude', 'trim|nullable|empty_default[0.0]|coordinate[longitude]');

        $list->field('image_file_id')
            ->typeUuid()
            ->column('imageFileID', true)
            ->validation('Image File Id', 'nullable|optional|uuid|check_image_file_id');

        $list->mediaField('image')
            ->validation('Image', [
                'mimes' => ['png', 'jpeg'],
                'max_size' => '10MB'
            ]);

        $list->field('moved_at')
            ->typeDateTime()
            ->column('movedAt')
            ->immutable();

        $list->field('moved_by_user_id')
            ->column('movedByUserID')
            ->immutable();

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'address', 'address_2', 'city', 'state', 'zip', 'county', 'township'
        ], function (Field $field) {
            return $field->enableAction(PropertyResource::ACTION_SORT);
        });

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('image')
            ->directoryName('property-images', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(ImageHandler::class);
                $type->variant('size_medium')
                    ->directoryName('size-medium', true)
                    ->handler(SizeMediumHandler::class);
            });
        $list->type('street_view_image')
            ->urlSlug('property-street-view-images')
            ->versions(function (MediaType $type) {
                $type->original()->handler(StreetViewImageHandler::class);
            });
        return $list;
    }

    public function validationRules(Rules $rules, PropertyResource $resource)
    {
        $rules->register('check_customer_id', function ($id) use ($resource) {
            if ($resource->relationResource('customer')->entityExists($id)) {
                return true;
            }
            return 'check_customer_id';
        }, [
            'check_customer_id' => 'Unable to find customer'
        ]);

        $rules->register('check_image_file_id', function ($id) use ($resource) {
            $image = $resource->relationResource('image');
            if (($file = $image->find($id)) === null) {
                return 'file_not_found';
            }
            if ($file->type !== FileResource::TYPE_PROPERTY_IMAGE) {
                return 'file_invalid_type';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} is not a property image'
        ]);

        return $rules;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        // if customer id changed, then we set the related moved columns
        if (isset($model_data['customerID']) && $request->getModel()->customerID !== $model_data['customerID']) {
            $model_data['movedAt'] = Carbon::now('UTC');
            $user = $request->resource()->acl()->user();
            $model_data['movedByUserID'] = $user !== null ? $user->getKey() : null;
        }

        return $model_data;
    }

    /**
     * Before update is saved, we check if image file has been cleared so we can delete the associated file entry
     *
     * @param UpdateRequest $request
     * @param array $model_data
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function anyUpdateSaveBefore(UpdateRequest $request, $model_data)
    {
        $model = $request->getModel();
        if (array_key_exists('imageFileID', $model_data) && $model_data['imageFileID'] === null && $model->imageFileID !== null) {
            /** @var PropertyResource $resource */
            $resource = $request->resource();

            /** @var ImageHandler $image_handler */
            $image_handler = $resource->getMediaHandler('image');
            $image_handler->deleteByFileField('image_file_id', $model);
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        $request->store('calendar_push', true);
    }

    /**
     * Before delete is saved, we delete any related entities using their respective resources
     *
     * @param DeleteRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Exceptions\AppException
     */
    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();

        $property_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        try {
            /** @var ProjectResource $project_resource */
            $project_resource = $resource->relationResource('projects');
            $project_resource->deleteByPropertyID($property_id);
        } catch (ImmutableEntityException | ImmutableRelationException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    public function deleteSaveAfter(DeleteRequest $request)
    {
        $resource = $request->resource();
        $property_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        $resource->relationResource('tasks')->detachAssociation($property_id);
    }

    /**
     * Close request
     *
     * If a calendar push is required, we execute it. It's handled via close so it handles atomic batch requests properly.
     * This method is only called if the batch finishes successfully.
     *
     * @param Request $request
     * @throws \Core\Exceptions\AppException
     */
    public function close(Request $request): void
    {
        if ($request instanceof PolyUpdateRequest) {
            $request = $request->getRequest();
            if ($request->storage('calendar_push', false)) {
                EventResource::make($request->resource()->acl())->pushToCalendarByPropertyID($request->getModel()->getKey());
            }
        }
    }

    public function queryScopeGlobal($query, PropertyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param PropertyResource $resource
     * @return bool
     */
    public function actionAllowed($action, PropertyResource $resource)
    {
        if (in_array($action, [PropertyResource::ACTION_SORT,  PropertyResource::ACTION_GET_COLLECTION, PropertyResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        if (($user->projectManagement || $user->sales) && (PropertyResource::ACTION_GROUP_DELETE & $action) === 0) {
            return true;
        }
        return false;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && in_array($scope->getFormat(), ['collection-v1', 'list-v1', 'task-v1', 'export-v1'])) {
            $resource = $request->resource();
            $term = $scope->getSearch();

            $customer_query = $resource->newScopedQuery();
            // customer joined in global scope
            (new Customer())->scopeSearch($customer_query, $term);
            $request->unionQuery($customer_query);

            $customer_phone_query = $resource->newScopedQuery()
                // customer joined in global scope
                ->join('customerPhone', 'customerPhone.customerID', '=', 'customer.customerID');
            (new CustomerPhone())->scopeSearch($customer_phone_query, $term);
            $request->unionQuery($customer_phone_query);

            $request->enableChunking()->chunkLimit(100);
        }
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'customer_id', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township',
                    'created_at', 'created_by_user_name', 'updated_at', 'updated_by_user_name'
                ], true)
                ->with([
                   'customer'
                ]);
                break;
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                $scope->fields(['id', 'address', 'address_2', 'city', 'state', 'zip'], true);
                $scope->with(['customer']);
                $scope->query(function ($query) {
                    return $query->withTrashed();
                });
                break;
            case 'export-v1':
                $scope->fields([
                    'customer_name', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township', 'created_at', 'created_by_user_name'
                ], true);
                break;
            case 'info-v1':
                $scope->fields([
                    'address', 'address_2', 'city', 'state', 'zip', 'county', 'township', 'image_file_id'
                ])
                ->with([
                    'customer' => [
                        'fields' => ['id', 'quickbooks_id', 'business_name', 'first_name', 'last_name', 'address', 'address_2', 'city', 'state', 'zip', 'email', 'is_unsubscribed'],
                        'with' => [
                            'phones' => [
                                'fields' => ['description', 'number', 'is_primary']
                            ]
                        ]
                    ],
                    'image_media_urls' => [],
                    'street_view_image_media_urls' => []
                ]);
                break;
            case 'list-v1':
                $scope->fields(['id', 'address', 'address_2', 'city', 'state', 'zip'], true);
                break;
            case 'task-v1':
                $scope->fields([
                    'id', 'customer_id', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township',
                    'created_at', 'created_by_user_name'
                ], true);
                break;
        }
    }
}
