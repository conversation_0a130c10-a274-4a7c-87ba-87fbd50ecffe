<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\User;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use App\Services\TrainingService;
use App\Traits\ResourceMedia\ImageTrait;
use Common\Models\TrainingAction;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Validation;

/**
 * Class ImageHandler
 *
 * @package App\ResourceMediaHandlers\User
 */
class ImageHandler extends BaseCompanyFileHandler
{
    use ImageTrait;

    /**
     * Save image to database and filesystem
     *
     * @param int $id
     * @param Entity $entity
     * @param array $config
     * @throws ValidationException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function save(int $id, Entity $entity, array $config = [])
    {
        $user = $this->resource->findOrFail($id);
        $this->resource->setMediaCompanyID($user->companyID);

        $upload = [
            'required' => true,
            'mimes' => ['jpeg', 'png', 'gif'],
            'max_size' => '5MB'
        ];

        if (!isset($config['check_image_dimensions']) || $config['check_image_dimensions']) {
            $upload['min_dimensions'] = [100, 100];
        }

        $validator = Validation::make()
            ->config([
                'file' => [
                    'label' => 'File',
                    'rules' => [
                        'upload' => $upload
                    ]
                ]
            ])
            ->run($entity->toArray());
        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        $path = $this->resizeImage($validator->data('file.tmp_name'));

        $image_id = $this->resource->getFields()->get('image_file_id')->outputValueFromModel($user);
        if ($image_id === null) {
            $image_id = $this->getFileCreateRequest(
                FileResource::TYPE_USER_IMAGE, $validator->data('file.name'), null, $path
            )
                ->run();
            $this->resource->update(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($user),
                'image_file_id' => $image_id
            ]))
                ->partial()
                ->run();

            // @todo move to event system
            TrainingService::queueCompleteUserAction($this->resource->acl()->user(), TrainingAction::UPLOAD_USER_PROFILE_PHOTO);
        } else {
            $this->getFileUpdateRequest($image_id, $validator->data('file.name'), $path)
                ->run();
        }
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $user = $this->resource->findOrFail($id);

        $user_acl = $this->resource->acl()->user();
        $this->resource->setMediaCompanyID($user_acl !== null ? $user_acl->companyID : $user->companyID);
        return Response::file($this->getPathFromFileField('image_file_id', $user));
    }
}
