<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Form\Item\Entry\Group;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use App\Traits\ResourceMedia\ImageTrait;
use Common\Models\FormItemEntryGroupFieldFile;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Exceptions\AppException;

/**
 * Class FileHandler
 *
 * @package App\ResourceMediaHandlers\Form\Item\Entry\Group
 */
class FileHandler extends BaseCompanyFileHandler
{
    use ImageTrait;

    /**
     * Save file
     *
     * @param Entity $entity
     * @param RequestInterface $request
     * @return string
     * @throws AppException
     * @throws ValidationException
     */
    public function save(Entity $entity, RequestInterface $request): string
    {
        $fields = new FieldList();
        $fields->field('file')
            ->validation('File', [
                'upload' => [
                    'required' => true,
                    'mimes' => ['png', 'jpeg', 'gif', 'pdf'],
                    'max_size' => '100MB'
                ]
            ]);

        $validation = $this->resource->getValidation($fields);

        $validator = $validation->run($entity->toArray());
        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        $company_id = $this->resource->acl()->user()->companyID;

        $this->resource->setMediaCompanyID($company_id);

        $path = $entity->get('file.tmp_name');
        $mime_type = Http::getMimeType($path);
        if (in_array($mime_type, ['image/png', 'image/jpeg', 'image/gif'])) {
            $path = $this->resizeImage($path);
        }

        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_FORM_UPLOAD,
            $entity->get('file.name'),
            $entity->get('description'),
            $path);
        return $file_request->run();
    }

    /**
     * Get company id from model
     *
     * @param FormItemEntryGroupFieldFile $field_file
     * @return int
     */
    protected function getCompanyID(FormItemEntryGroupFieldFile $field_file): int
    {
        $user = $this->resource->acl()->user();
        if ($user !== null) {
            return $user->companyID;
        }
        return FormItemEntryGroupFieldFile::query()->withFormItem()->whereKey($field_file->getKey())->value('companyID');
    }

    /**
     * Get response from media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $field_file = $this->resource->findOrFail($id);

        if (!$this->resource->hasMediaCompanyID()) {
            if (($company_id = $this->getCompanyID($field_file)) === null) {
                throw new AppException('No company id defined for field file: %s', $id);
            }
            $this->resource->setMediaCompanyID($company_id);
        }

        return Response::file($this->getPathFromFileField('file_id', $field_file));
    }
}
