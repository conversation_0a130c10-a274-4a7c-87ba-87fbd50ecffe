<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Project;

use App\Classes\Func;
use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use App\Traits\ResourceMedia\ImageTrait;
use Common\Models\ProjectFile;
use Core\Classes\File;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Exceptions\AppException;

/**
 * Class FileHandler
 *
 * @package App\ResourceMediaHandlers\Project
 */
class FileHandler extends BaseCompanyFileHandler
{
    use ImageTrait;

    /**
     * Get company id from authenticated user or project file
     *
     * @param ProjectFile $project_file
     * @return int|null
     */
    protected function getCompanyID(ProjectFile $project_file): ?int
    {
        $user = $this->resource->acl()->user();
        if ($user !== null) {
            return $user->companyID;
        }
        $company_id = ProjectFile::query()
            ->withCustomer()
            ->whereKey($project_file->getKey())
            ->value('companyID');
        return $company_id !== null ? (int) $company_id : null;
    }

    /**
     * Get path from entity
     *
     * If file is an image, then we resize to reduce file size/
     *
     * @param Entity $entity
     * @param bool $allow_resize
     * @return string
     * @throws AppException
     */
    protected function getPath(Entity $entity, bool $allow_resize = true): string
    {
        $path = $entity->get('tmp_name');
        if ($allow_resize) {
            $mime_type = Http::getMimeType($path);
            if (in_array($mime_type, ['image/png', 'image/jpeg', 'image/gif'])) {
                $temp_file = Func::createTempFile(null, false);
                $image = $this->newImageManager()->make($path)
                    ->orientate()
                    ->resize(1024, 1024, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    })
                    ->save($temp_file);
                $path = $temp_file;
                $image->destroy();
            }
        }
        return $path;
    }

    /**
     * Create new file entity with uploaded data and store
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws AppException
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $this->resource->setMediaCompanyID($this->resource->acl()->companyID());

        $path = $this->getPath($entity, $request->storage('allow_resize', true));
        $file_request = $this->getFileCreateRequest(FileResource::TYPE_PROJECT_FILE, $entity->get('name'), null, $path);
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            // add file id to main entity of parent request
            $request->getMainEntity()->set('file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Update existing file with new data
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws AppException
     */
    public function update(Entity $entity, UpdateRequest $request): void
    {
        $this->resource->setMediaCompanyID($this->resource->acl()->companyID());

        $path = $this->getPath($entity);
        $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($request->getModel());
        $this->getFileUpdateRequest($file_id, $entity->get('name'), $path)->run();
    }

    /**
     * Get response for media controller
     *
     * @param string $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws AppException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $file = $this->resource->findOrFail($id);

        if (!$this->resource->hasMediaCompanyID()) {
            if (($company_id = $this->getCompanyID($file)) === null) {
                throw new AppException('No company id defined for project file: %s', $id);
            }
            $this->resource->setMediaCompanyID($company_id);
        }

        $response = Response::file($this->getPathFromFileField('file_id', $file))
            ->contentType($file->file->contentType)
            ->filename(File::sanitizeName($file->file->name, $file->file->extension));
        if (isset($config['download'])) {
            $response->download();
        }
        return $response;
    }
}
