<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Property;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Traits\ResourceMedia\ImageTrait;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Classes\BatchRequest;
use App\Resources\FileResource;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;

/**
 * Class ImageHandler
 *
 * @package App\ResourceMediaHandlers\Property
 */
class ImageHandler extends BaseCompanyFileHandler
{
    use ImageTrait;

    /**
     * Handle media field data during create request
     *
     * Create file entity by adding a request to the batch of the poly create and assign id to drawing before it's saved.
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $company_id = $this->resource->acl()->user()->companyID;
        $this->resource->setMediaCompanyID($company_id);

        $path = $this->resizeImage($entity->get('tmp_name'));

        $file_request = $this->getFileCreateRequest(FileResource::TYPE_PROPERTY_IMAGE, $entity->get('name'), null, $path);
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            $request->getRequest()->getEntity()->set('image_file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Handle media field data during update request
     *
     * If no image id is available, then we create file entity and assign the id to the drawing. Otherwise, we update
     * the existing file entity and flag the repair plan as invalid to make sure it gets regenerated.
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function update(Entity $entity, UpdateRequest $request): void
    {
        $model = $request->getModel();

        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $path = $this->resizeImage($entity->get('tmp_name'));

        $file_id = $this->resource->getFields()->get('image_file_id')->outputValueFromModel($model);
        if ($file_id === null) {
            $batch = BatchRequest::make()->sequential();

            $file_request = $this->getFileCreateRequest(FileResource::TYPE_PROPERTY_IMAGE, $entity->get('name'), null, $path);
            $batch->add($file_request);

            $property_request = $this->resource->partialUpdate(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($model)
            ]))
                ->attach('batch.last_request', function (CreateRequest $file_request, UpdateRequest $property_request) {
                    $entity = $property_request->getEntity();
                    $entity->set('image_file_id', $file_request->response());
                });
            $batch->add($property_request);

            $batch->run();
        } else {
            $this->getFileUpdateRequest($file_id, $entity->get('name'), $path)->run();
        }
    }

    /**
     * Get response for media controller
     *
     * @param int $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $property = $this->resource->findOrFail($id);

        $user = $this->resource->acl()->user();
        $this->resource->setMediaCompanyID($user !== null ? $user->companyID : $property->customer->companyID);

        return Response::file($this->getPathFromFileField('image_file_id', $property));
    }
}
