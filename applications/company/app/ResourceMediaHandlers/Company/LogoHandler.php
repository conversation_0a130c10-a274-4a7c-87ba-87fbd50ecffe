<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Company;

use App\{ResourceMediaHandlers\BaseCompanyFileHandler, Resources\FileResource, Services\TrainingService};
use Common\Models\{Company, TrainingAction};
use Core\Components\Http\{Responses\FileResponse, StaticAccessors\Response};
use Core\Components\Resource\{Classes\Entity, Exceptions\ValidationException};
use Core\Components\Validation\Classes\Validation;

/**
 * Class LogoHandler
 *
 * @package App\ResourceMediaHandlers\Company
 */
class LogoHandler extends BaseCompanyFileHandler
{
    /**
     * Save logo for company
     *
     * @param int|Company $company
     * @param Entity $entity
     * @param array $config
     * @throws ValidationException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function save(int|Company $company, Entity $entity, array $config = []): void
    {
        if (!is_object($company) || !($company instanceof Company)) {
            $company = $this->resource->findOrFail($company);
        }
        $this->resource->setMediaCompanyID($company->companyID);

        $upload = [
            'required' => true,
            'mimes' => ['jpeg', 'png', 'gif', 'webp'],
            'max_size' => '5MB'
        ];

        if (!isset($config['check_image_dimensions']) || $config['check_image_dimensions']) {
            $upload['max_dimensions'] = [2000, 2000];
        }

        $validator = Validation::make()
            ->config([
                'file' => [
                    'label' => 'File',
                    'rules' => [
                        'upload' => $upload
                    ]
                ]
            ])
            ->run($entity->toArray());
        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        $file_id = $this->resource->getFields()->get('logo_file_id')->outputValueFromModel($company);
        if ($file_id === null) {
            $image_request = $this->getFileCreateRequest(
                FileResource::TYPE_COMPANY_LOGO,
                $validator->data('file.name'),
                null,
                $validator->data('file.tmp_name')
            );
            if (isset($config['created_by_user_id'])) {
                $image_request->store('_created_by_user_id', $config['created_by_user_id']);
            }
            $image_id = $image_request->run();
            $this->resource->partialUpdate(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($company),
                'logo_file_id' => $image_id
            ]))
                ->store('marketing_list_push', false)
                ->run();
        } else {
            $this->getFileUpdateRequest(
                $file_id, $validator->data('file.name'), $validator->data('file.tmp_name')
            )->run();

            // @todo move to event system
            TrainingService::queueCompleteUserAction($this->resource->acl()->user(), TrainingAction::UPDATE_COMPANY_LOGO);
        }
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $company = $this->resource->findOrFail($id);

        $user = $this->resource->acl()->user();
        $this->resource->setMediaCompanyID($user !== null ? $user->companyID : $company->companyID);

        return Response::file($this->getPathFromFileField('logo_file_id', $company));
    }
}
