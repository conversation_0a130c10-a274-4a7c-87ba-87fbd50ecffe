<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Company\Invoice;

use App\Classes\Func;
use App\Classes\Pdf;
use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\Company\Invoice\LineItemResource;
use App\Resources\Company\InvoiceResource;
use App\Resources\Company\PaymentMethodResource;
use App\Resources\FileResource;
use App\Services\TimeService;
use Common\Models\CompanyInvoice;
use Core\Components\Asset\StaticAccessors\Asset;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Http\StaticAccessors\View;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;
use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Formatter\IntlMoneyFormatter;
use Money\Parser\DecimalMoneyParser;
use NumberFormatter;

/**
 * Class StatementHandler
 *
 * @package App\ResourceMediaHandlers\Company\Invoice
 */
class StatementHandler extends BaseCompanyFileHandler
{
    /**
     * Generate PDF
     *
     * @param int|CompanyInvoice $invoice
     * @param bool $force
     * @return array
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\BatchHandleException
     * @throws \Core\Components\Resource\Exceptions\BatchPrepareException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     *
     * @todo convert to use resources once reseller related resources are available
     */
    public function generate(mixed $invoice, bool $force = false): array
    {
        if (!($invoice instanceof CompanyInvoice)) {
            $invoice = $this->resource->findOrFail((int) $invoice);
        }

        $file_name = "invoice_{$invoice->getKey()}.pdf";

        if ($force || $invoice->statementFileID === null || !$invoice->isStatementFileValid) {
            if ($this->resource->acl()->user() === null) {
                throw new AppException('User is required to generate invoice PDF');
            }

            $this->setStartTime();

            $temp_file = Func::createTempFile(null, false);

            $line_items_scope = Scope::make()->filter('invoice_id', 'eq', $invoice->getKey());

            $type_map = [
                'general' => [
                    'types' => [
                        LineItemResource::TYPE_GENERAL,
                        LineItemResource::TYPE_SUBSCRIPTION,
                        LineItemResource::TYPE_ADD_ON,
                        LineItemResource::TYPE_SUBSCRIPTION_ADD_ON
                    ]
                ],
                'discounts' => [
                    'label' => 'Discounts',
                    'types' => [
                        LineItemResource::TYPE_DISCOUNT,
                        LineItemResource::TYPE_SUBSCRIPTION_DISCOUNT
                    ]
                ],
                'fees' => [
                    'label' => 'Fees',
                    'types' => [
                        LineItemResource::TYPE_FEE,
                        LineItemResource::TYPE_SUBSCRIPTION_FEE
                    ]
                ],
                'credits' => [
                    'label' => 'Credits',
                    'types' => [
                        LineItemResource::TYPE_CREDIT
                    ]
                ]
            ];

            $line_items = $this->resource->relationResource('line_items')->collection()->scope($line_items_scope)->run();

            $payment_method_scope = Scope::make()
                ->with([
                   'item' => [
                       'poly_scopes' => [
                           PaymentMethodResource::TYPE_ACH => [
                               'fields' => ['account_number']
                           ],
                           PaymentMethodResource::TYPE_CREDIT_CARD => [
                               'fields' => ['number', 'expiration_date', 'address', 'address_2', 'city', 'state', 'zip']
                           ]
                       ]
                   ]
               ]);

            $payment_method_number = '';
            if ($invoice->paidByCompanyPaymentMethodID !== null) {
                $payment_method = $this->resource->relationResource('paid_by_payment_method')->entity($invoice->paidByCompanyPaymentMethodID)->scope($payment_method_scope)->run();
                $payment_method_item = $payment_method->item;
                switch($payment_method->item_type) {
                    case PaymentMethodResource::TYPE_ACH:
                        $payment_method_number = 'Account Number ' . $payment_method_item->account_number;
                        break;
                    case PaymentMethodResource::TYPE_CREDIT_CARD:
                        $address = $payment_method_item->address;
                        $address = $address . ($payment_method_item->address_2 ? ', ' . $payment_method_item->address_2 : '');
                        $address = $address . '<br>' . $payment_method_item->city . ', ' . $payment_method_item->state . ' ' . $payment_method_item->zip;

                        $payment_method_number = 'Card Ending in ' . $payment_method_item->number . '<br>' . $address;
                        break;
                }
            }

            $currencies = new ISOCurrencies();
            $formatter = new IntlMoneyFormatter(new NumberFormatter('en_US', NumberFormatter::CURRENCY), $currencies);

            $currency = new Currency('USD');
            $total_amount = (new DecimalMoneyParser($currencies))->parse($invoice->total, $currency);
            $invoice->total = $formatter->format($total_amount);

            // loop through line items to format date and group by type
            $line_items_by_type = [];
            foreach ($line_items as $line_item) {
                $total = (new DecimalMoneyParser($currencies))->parse($line_item->total, $currency);
                // @todo if percentage, then show with name
                $line_item->total = $formatter->format($total);
                $line_items_by_type[$line_item->type] ??= [];
                $line_items_by_type[$line_item->type][] = $line_item;
            }

            // loop through types to set in the correct order
            $line_item_types = [];
            foreach ($type_map as $group => $config) {
                $group_line_items = [];
                foreach ($config['types'] as $type) {
                    if (!isset($line_items_by_type[$type])) {
                        continue;
                    }
                    $group_line_items = array_merge($group_line_items, $line_items_by_type[$type]);
                }
                if (count($group_line_items) === 0) {
                    continue;
                }
                $line_item_types[$group] = [
                    'label' => $config['label'] ?? null,
                    'line_items' => $group_line_items
                ];
            }

            $status_stamps = [
                InvoiceResource::STATUS_PAID => Asset::path('resourceImage', 'pdfs/invoice/stamp_paid.png'),
                InvoiceResource::STATUS_VOIDED => Asset::path('resourceImage', 'pdfs/invoice/stamp_voided.png'),
                InvoiceResource::STATUS_FAILED => Asset::path('resourceImage', 'pdfs/invoice/stamp_past_due.png'),
                InvoiceResource::STATUS_REFUNDED => Asset::path('resourceImage', 'pdfs/invoice/stamp_refunded.png')
            ];

            $invoice['status_stamp'] = null;
            if (isset($status_stamps[$invoice->status])) {
                $invoice['status_stamp'] = $status_stamps[$invoice->status];
            }
            $invoice['createdAt'] = $invoice['createdAt']->timezone(TimeService::getTimezoneForCompany($invoice->companyID));

            $company = $invoice->company;
            $company_phone = $company->phones()->where('isPrimary', 1)->first();
            if (empty($company->reseller->billingResellerID)) {
                $reseller = $company->reseller;
            } else {
                $reseller = $company->reseller->billingReseller;
            }
            $reseller_phone =  $reseller->phones()->where('isPrimary', 1)->first();
            $reseller_domain = $reseller->domains()->wherePivot('isResellerPrimaryDomain', 1)->first();
            $html = View::fetch('pdfs.invoice.statement', [
                'is_subscription' => $invoice->type === CompanyInvoice::TYPE_SUBSCRIPTION,
                'company' => $company,
                'companyPhone' => $company_phone->phoneNumber,
                'invoice' => $invoice,
                'lineItemTypes' => $line_item_types,
                'reseller' => $reseller,
                'resellerPhone' => $reseller_phone->phoneNumber,
                'brand' => $reseller_domain->brand,
                'paymentMethod' => $payment_method_number
            ]);

            $snappy = new Pdf();
            $snappy->setOptions([
                'page-size' => 'Letter',
                'margin-top' => '0',
                'margin-right' => '0',
                'margin-bottom' => '0',
                'margin-left' => '0',
                'disable-smart-shrinking' => true,
                'title' => $file_name
            ]);

            $snappy->generateFromHtml($html, $temp_file);

            $invoice_id = $this->resource->getPrimaryField()->outputValueFromModel($invoice);

            $batch = BatchRequest::make()->sequential();

            $statement_file_id = $this->resource->getFields()->get('statement_file_id')->outputValueFromModel($invoice);
            if ($statement_file_id === null) {
                $file_request = $this->getFileCreateRequest(
                    FileResource::TYPE_INVOICE_STATEMENT, 'invoice.pdf', null, $temp_file
                );
            } else {
                $file_request = $this->getFileUpdateRequest($statement_file_id, 'invoice.pdf', $temp_file);
            }
            $batch->add($file_request);

            $invoice_entity = Entity::make([
                'id' => $invoice_id,
                'is_statement_file_valid' => true
            ]);
            $invoice_request = $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->partialUpdate($invoice_entity)
                ->findConfig([
                    'check_mutability' => false
                ])
                ->attach('batch.last_request', function () use ($invoice, $invoice_entity, $file_request) {
                    if ($invoice->statementFileID !== null) {
                        return;
                    }
                    $invoice_entity->set('statement_file_id', $file_request->response());
                });

            $batch->add($invoice_request);

            $batch->run();

            $file_id = $file_request->response();
        } else {
            $file_id = $this->resource->getfields()->get('statement_file_id')->outputValueFromModel($invoice);
        }
        return [
            'file_name' => $file_name,
            'path' => $this->getPathFromFileID($file_id)
        ];
    }

    /**
     * Get path to invoice file
     *
     * @param int|CompanyInvoice $invoice
     * @return string
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\BatchHandleException
     * @throws \Core\Components\Resource\Exceptions\BatchPrepareException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function getPath(int|CompanyInvoice $invoice): string
    {
        if (!$this->resource->hasMediaCompanyID() && $this->resource->acl()->user() === null) {
            throw new AppException('User is required to get path');
        }
        return $this->generate($invoice)['path'];
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\BatchHandleException
     * @throws \Core\Components\Resource\Exceptions\BatchPrepareException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $info = $this->generate($id, isset($config['regenerate']));
        return Response::file($info['path'])->contentType('application/pdf')->filename($info['file_name']);
    }
}
