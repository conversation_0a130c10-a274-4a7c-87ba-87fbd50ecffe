<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Media;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use Core\Classes\File;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;

/**
 * Class FileHandler
 *
 * @package App\ResourceMediaHandlers\Media
 */
class FileHandler extends BaseCompanyFileHandler
{
    /**
     * Create new file entity from upload data
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_MEDIA, $entity->get('name'), null, $entity->get('tmp_name')
        );
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            // add file id to main entity of parent request
            $request->getMainEntity()->set('file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Update or create file entity from upload data
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     */
    public function update(Entity $entity, UpdateRequest $request): void
    {
        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($request->getModel());
        $this->getFileUpdateRequest($file_id, $entity->get('name'), $entity->get('tmp_name'))->run();
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $media = $this->resource->findOrFail($id);

        $user_acl = $this->resource->acl()->user();
        $this->resource->setMediaCompanyID($user_acl !== null ? $user_acl->companyID : $media->companyID);

        return Response::file($this->getPathFromFileField('file_id', $media))
            ->contentType($media->file->contentType)
            ->filename(File::sanitizeName($media->name, $media->file->extension));
    }
}
