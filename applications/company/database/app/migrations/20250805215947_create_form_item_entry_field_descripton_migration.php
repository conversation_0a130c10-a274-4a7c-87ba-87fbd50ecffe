<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

final class CreateFormItemEntryFieldDescriptonMigration extends Migration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->updateTable('files')
            ->columns(function (Blueprint $table) {
                $table->string('description', 250)->after('name')->nullable();
            })
            ->useHistory()
            ->alter();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $this->updateTable('files')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('description');
            })
            ->useHistory()
            ->alter();
    }
}
