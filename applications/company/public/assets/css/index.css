div.contentContainer{
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.home-header {
    display: flex;
    background-color:#f6f6f6;
}

.dashboard-title {
    flex: 1;
}

.home-map-switch {
    flex: 0 0 auto;
    padding: 0.5rem;
}

.contentContainer {
    display: flex;
}

.dashboard {
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.project-status {
    width: 75%;
    flex: 0 0 auto;
    background-color: #ffffff;
    z-index: 30;
    border-left:1px solid #005ad0;
    position: relative;
    height: 100%;
}

@media print, screen and (min-width: 40em) {
    .project-status {
        width: 275px;
    }
}

.project-status.hidden {
    width: 0;
}

#calendarMap {
    height: 320px;
    border: 1px solid #000;
}

h1.dashboard-title{
    margin-top: 0;
    margin-bottom: 0;
    padding: 0.5rem;
}

.mapSwitchContainer label.mapLabel {
    display: inline;
    font-weight: bold;
    color: #58595b;
    font-size: 1rem;
    text-align: right;
    vertical-align: top;
}
.mapSwitchContainer div.switch{
    display: inline;
}

.menu-bar {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-wrap: nowrap;
    padding: .625rem .625rem;
    border-bottom: 1px solid #b1b4bf;
    background-color: #f6f6f6;
    justify-content: space-between;
    position: fixed;
    width: 75%;
    top: 56px;
    z-index: 40;
}

.l-app.t-has-notification .menu-bar {
    top: 96px;
}

@media print, screen and (min-width: 40em) {
    .menu-bar {
        width: 275px;
    }
}

.project-status.hidden .menu-bar {
    width: 0%;
}

.menu-bar .sort {
    flex: 0 0 auto;
}

.menu-bar .sort select {
    padding: .3rem 1.5rem .3rem .5rem;
    height: 1.8rem;
    font-size: .9rem;
    margin-bottom: 0;
    width: 110px;
}

.menu-bar .group {
    flex: 0 0 auto;
    margin: .25rem 0 0 .5rem;
}

.menu-bar .group label {
    font-size: .7rem;
}

@media print, screen and (min-width: 74.9375em) {
    .menu-bar .group label {
        font-size: .8rem;
    }
}

.menu-bar .group label > [type='checkbox'] {
    margin-right: 0rem;
}

.menu-bar .refresh {
    flex: 1;
    margin-left: .5rem;
    padding: .5rem 1.5rem 0 0;
}

.menu-bar .refresh span {
    display: block;
    background-image: url('../images/icons/refresh.png');
    width:16px;
    height: 16px;
    background-position: 0 0;
}

.menu-bar .refresh span:hover {
    background-position: 0 16px;
}

.projectStatusTab {
    z-index: 20;
    background-color: #005ad0;
    height: 190px;
    width: 33px;
    border-radius: 3px 0 0 3px;
    cursor: pointer;
    position: fixed;
    right: 75%;
    top: 112px;
}

@media print, screen and (min-width: 40em) {
    .projectStatusTab {
        right: 275px;
    }
}

.projectStatusTab.t-trial {
    top: calc(112px + 40px)
}

.projectStatusTab.closed {
    right: 0;
}

.projectStatusTab span {
	-webkit-transform:rotate(-90deg);
    -moz-transform:rotate(-90deg);
    -o-transform: rotate(-90deg);
    -ms-transform:rotate(-90deg);
    transform: rotate(-90deg);
    display: block;
    color: #ffffff;
	width: 170px;
    font-weight: bold;
    right: -69px;
    position: absolute;
    top: 81px;
    font-size: 16px;
}

.projectStatusContainer {
	background-color: #ffffff;
    padding-top: 49px;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.projectStatusTitle {
    background-color: #ffffff;
    color: #333333;
	cursor: pointer;
	font-size: .85rem;
    padding: .4rem 0 .4rem .6rem;
    border-top: 1px solid lightgray;
    display: none;
    line-height: 1.3;
}

.projectStatusTitle.open {
    border-bottom: 1px solid lightgray;
    background-color: #f7f7f7;
}

.projectStatusTitle .subTitle {
    font-size: .7rem;
    font-style: italic;
    color: #5a5a5c;
}

.projectStatusTitle .arrow {
    width: 15px;
    height: 12px;
    display: inline-block;
    background-size: 15px 15px;
    float: right;
    margin: 10px 5px 0 0;
    opacity: .4;
    background-image: url("../images/icons/arrow-left.png");
}

.projectStatusTitle .arrow.open {
    transform: rotate(-90deg);
}

.sales-bucket .projectStatusList {
    padding: .25rem 0 .25rem 2.3rem;
}

.projectStatusList {
	cursor: pointer;
	font-size: .8rem;
	color: #777777;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    padding: .25rem 0 .25rem 1.2rem;
}

.projectStatusList a {
    color: #777777;
}

.projectStatusList:hover {
    color: #444444;
}

.projectStatusList span {
    font-size: .7rem;
    font-style: italic;
}

.projectStatusStep {
    display: none;
    -webkit-box-shadow: inset 1px -1px 5px 0px rgba(0,0,0,0.2);
    -moz-box-shadow: inset 1px -1px 5px 0px rgba(0,0,0,0.2);
    box-shadow: inset 1px -1px 5px 0px rgba(0,0,0,0.2);
}

.projectStatusStep a:first-of-type {
    margin-top: .25rem;
}

.projectStatusStep .no-items {
    font-size: .8rem;
    margin-left: 1rem;
    color: #5a5a5c;
    padding: .1rem 0;
}

.sales-bucket .salesperson {
    padding: .3rem 0rem .3rem 1rem;
    color: #005ad0;
    cursor: pointer;
    font-size: .85rem;
    border-top: 1px solid lightgray;
}

.sales-bucket a {
    display: none;
}

.sales-bucket a:last-child .projectStatusList {
    padding-bottom: .5rem;
}

.sales-bucket.open a {
    display: block;
}

.sales-bucket .arrow {
    width: 12px;
    height: 12px;
    display: inline-block;
    background-size: 12px 12px;
    float: right;
    margin: 5px 10px;
    opacity: .4;
    background-image: url(../images/icons/arrow-left.png);
}

.sales-bucket .arrow.open {
    transform: rotate(-90deg);
}

.sales-bucket .salesperson .avatar {
    width: 16px;
    height: 16px;
    margin: -.2rem .3rem 0 0;
}

.projectStatusStep .sales-bucket:first-of-type .salesperson {
    border-top: none;
}

div.loadingImage {
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    opacity: 0.5;
    text-align: center;
    display: none;
    background-image: url(../images/ajax-loader.gif);
    background-position: center center;
    background-repeat: no-repeat;
}

div.loadingImage.contained {
    position: absolute;
}

.today.list {
    margin: .5rem .5rem 0 .5rem
}

.fc-list-item .fc-list-item-title.fc-widget-content {
    text-align: left;
}

.fc-list-item .fc-list-item-marker.fc-widget-content .fc-event-dot {
    width: 15px;
    height: 15px;
    border-radius: 7px !important;
}

.button-group.list-types {
    margin: .43rem .5rem .25rem 0 !important;;
    float: right;
    display: block !important;
}

#calendar .fc-list-empty {
    vertical-align: top;
    padding-top: 1rem;
}

.fc-list-table td {
    padding: 8px 5px !important;
}

.fc-list-table td.fc-list-item-time.fc-widget-content, .fc-list-table td.fc-list-item-marker.fc-widget-content {
    vertical-align: middle;
}

#calendar .fc-list-table td.fc-list-item-title.fc-widget-content {
    line-height: 1.3;
}

.list-types .button.view {
    padding: 0.6em .5em;
    font-size: .65rem;
}

.internalNote {
    background-color: #dff1fc;
}

.reference-id {
    color: #A9A9A9;
    font-weight: normal;
}

.dashboard-filter-bar.show-for-small-only {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.dashboard-filter-bar.show-for-small-only .filter {
    flex: 0 0 auto;
    width: unset;
}

.dashboard-filter-bar.show-for-small-only .todayButton {
    flex: 1;
    width: unset;
}

.dashboard-filter-bar.show-for-small-only .button-group {
    flex: 1;
    width: unset;
}

.dashboard-filter-bar.show-for-small-only .daySelector {
    display: flex;
}

.dashboard-filter-bar.show-for-small-only .daySelector .calendar-previous {
    flex: 0 0 auto;
}

.dashboard-filter-bar .daySelector .calendar-title {
    flex: 1;
    padding: 0;
}

.dashboard-filter-bar .daySelector .calendar-title #calendarTitle {
    width: unset;
}

.dashboard-filter-bar .daySelector .calendar-next {
    flex: 0 0 auto;
}