body {
    background-color: #fff;
}

.row.inputs {
    padding-top: 1rem;
}

#marketingReport, #employeeSalesReport {
    max-width: 115rem;
}

#marketingReport table, #employeeSalesReport table {
    font-size: 0.8em;
    border: 1px solid black;
}

#overallSalesReport table, #productSalesReport table {
    font-size: 0.8em;
    border: 1px solid black;
    width: 74%;
    margin-bottom: 1rem;
    border-radius: 5px;
    margin-left: 17px;
}

#marketingReport table td, #overallSalesReport table td, #employeeSalesReport table td, #productSalesReport table td {
    border: 1px solid black;
    color: black;
}

#overallSalesReport table td {
    border: 1px solid black;
    color: black;
    width: 50%;
}

#overallSalesReport table tr:nth-child(even), #employeeSalesReport table tr:nth-child(even) {
    background: #dff1fc
}

#overallSalesReport table tr:nth-child(odd), #employeeSalesReport table tr:nth-child(odd) {
    background: #ffffff
}

#heading, .heading {
    font-size: 1.3em;
    font-weight: bold;
}

.results {
    font-size: 1em;
    font-weight: bold;
}

#allMarketing {
    background-color: #dff1fc;
    font-weight: bold;
    font-size: 1rem;
}

[name="leadSource"] {
    text-align: left;
}

[class="subsourceName"][name="leadSource"] {
    text-indent: 1rem;
}

[name="totalMarketingCosts"], [name="grossSales"], [name="costPerLead"], [name="costPerSale"], [name="revenuePerLead"] {
    text-align: right;
}

.number,
.money {
    text-align: right;
}

#productSalesReport .heading td:first-child {
    text-align: left;
}

#productSalesReport table td:nth-child(1) {
    width: 40%;
}

#productSalesReport table td:nth-child(2) {
    width: 35%;
}

#productSalesReport table td:nth-child(3) {
    width: 25%;
}

#productSalesReport table tr:first-child td:first-child {
    text-align: center !important;
}

#employeeSalesReport table {
    width: 100%;
}
#employeeSalesReport[data-type="8"] { /* special style for product table */
    width: 60%;
    margin: 0 auto;
}
#employeeSalesReport[data-type="8"] td:first-child {
    text-align: left;
}
#employeeSalesReport[data-type="8"] td:nth-child(2) {
    width: 15%;
    text-align: right;
}
#employeeSalesReport[data-type="8"] td:nth-child(3) {
    width: 15%;
    text-align: left;
}
#employeeSalesReport[data-type="8"] td:nth-child(4) {
    width: 15%;
    text-align: right;
}

.noSalesData {
    font-size: 1.5em;
    font-style: italic;
}

.subservice {
    text-indent: 1rem;
    font-size: 0.8em;
    font-weight: normal;
}

.heading.mainservice {
    background-color: #dff1fc;
}

#heading td {
    text-align: center;
}

.ui-tooltip-content {
    font-size: 0.7rem;
}

.source {
    background-color: #dff1fc;
    font-weight: bold;
}

[name="reportType"] {
    margin-bottom: 0;
}

div.loadingImage {
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    opacity: 0.5;
    text-align: center;
}

div.loadingImage {
    display: none;
}

div.loadingImage > img {
    position: absolute;
    top: 40%;
}