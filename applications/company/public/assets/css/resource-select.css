@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoon_calendar-filter.eot?gib9a4');
  src:  url('../fonts/icomoon_calendar-filter.eot?gib9a4#iefix') format('embedded-opentype'),
  url('../fonts/icomoon_calendar-filter.ttf?gib9a4') format('truetype'),
  url('../fonts/icomoon_calendar-filter.woff?gib9a4') format('woff'),
  url('../fonts/icomoon_calendar-filter.svg?gib9a4#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

.select2-results__option .wrap{
  margin-left:15px;
}

.select2-results__option[role=group] .wrap:before {
  content:"";
}

.select2-container--default .select2-results > .select2-results__options {
  max-height: 385px;
}


[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-cross:before {
  content: "\ea0f";
  font-size: 11px;
}
.icon-checkmark:before {
  content: "\ea10";
  font-size: 12px;
}
.icon-tab:before {
  content: "\ea45";
}
.icon-filter:before {
  content: "\ea5b";
}

.move {
  background-image: url('../images/move.svg');
  background-repeat: no-repeat;
  height:15px;
  width:20px;
  float:right;
  z-index: 10;
  position: relative;
}

.calendarWrapper {
  display: flex;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  background-color: #fff;
}

#filterGroup {
  width:0%;
  border-top: 2px solid #b1b4bf;
  border-bottom: 1px solid #b1b4bf;
  background-color: #f6f6f6;
  border-right: 1px solid #9ea5b5;
  flex:0 0 auto;
  -webkit-box-flex: 0 0 auto;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  overflow:hidden;
}

#calendarShow #filterGroup {
  border-right: none;
  border-left: 2px solid #b1b4bf;
  margin-bottom: 0;
}

#calendarGroup {
  width: 100%;
  flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
}

#filterGroup .header {
  border-bottom: 2px solid #b1b4bf;
}

#filterGroup .header .title {
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: bold;
  color: #58595b;
  font-size: 1rem;
  margin-bottom: 0;
  padding: .5rem;
  width: 50%;
  display:inline-block;
}

#filterGroup .header .group {
  display: inline-block;
  float: right;
  margin: .5rem .5rem 0 0;
}

#filterGroup .header .group .btn {
  width: 1.7rem;
  height: 1.7rem;
  border-radius: 2px 2px 2px 2px;
  background: #ffffff;
  position: relative;
  border: 1px solid #d1d3d4;
  transition: background-color 0.25s ease-out, color 0.25s ease-out;
  color:#8a8a8a;
  padding-bottom: .2rem;
}

#filterGroup .header .group .btn:hover {
  background: #f6f6f6;
}

#filterGroup .list {
  overflow-y: auto;
}

#filterGroup .filter-section {
  margin-bottom: 0;
}

#filterGroup .filter-section .section-title {
  font-weight: bold;
  background: #005ad0;
  color: white;
  display: block;
  padding: 0;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

#filterGroup .filter-section .section-title > span {
  font-size: 1rem;
  font-style: normal;
  color: #ffffff;
  padding: .3rem .5rem;
  display: block;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

#filterGroup .filter-section .section-title a.handle {
  width:30px;
  height:32px;
  position: absolute;
  top: 0px;
  right: 0px;
}

#filterGroup .filter-section .section-title span.move {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  top: 12px;
  right: 10px;
}

#filterGroup .ui-state-default {
  background: #f6f6f6;
  color:#58595b;
  padding:.3rem 2rem .3rem .5rem;
  z-index: 1;
  position: relative;
  cursor: pointer;
}

#filterGroup .ui-state-default.active {
  background: #dddddd;
}

#filterGroup .role-list {
  margin-bottom: 0;
}

#filterGroup .role-item {
  background: #f6f6f6;
  color:#58595b;
  padding:.3rem .5rem;
  font-weight: normal;
  z-index: 1;
  position: relative;
  cursor: pointer;
  border: 1px solid #d3d3d3;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

#filterGroup .role-item.active {
  background: #dddddd;
}