div.loadingImage {
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    opacity: 0.5;
    text-align: center;
}
div.loadingImage {
    display: none;
}
div.loadingImage > img {
    position: absolute;
    top: 40%;
}

p {width:100%;}

.sign-contract {
    display: none;
}

.wt-flex-wrapper-block {
    display: block;
    padding: 0 0.75em;
}

.wt-flex-wrapper {
    /*callout*/
    border-radius: 6px;
    font-weight: 400;
    border: 1.5px solid #FFB80026;
    min-height: 39px;

    background-color: #FFF8E8;
    color: #435162;
    margin: 1em 0;
    padding: 1em;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: space-between;
}

.wt-footnote {
    font-size: 0.8em;
    margin-bottom: 2em;
}

.wt-flex-wrapper-prequal {
    display: flex;
    font-style: italic;
    font-size: 0.9em;
    text-align: right;
    flex-direction: row-reverse;
}

.wt-primary-button {
    margin-bottom: 0!important;
}

.t-hidden {
    display: none;
}

.bid-content-acknowledgements {
    margin-bottom: 16px;
}

.bid-content-acknowledgements p {
    margin-bottom: 0;
    flex: 1;
}

.bid-content-acknowledgements label {
    display: flex;
    flex-wrap: wrap;
}

.bid-content-acknowledgements input {
    width: 15px;
}

.bid-content-acknowledgements .form-error {
    margin-top: 0;
    margin-bottom: .5rem;
}

.bid-content-acknowledgements .form-error.is-visible {
    width: 100%;
}