.fc-event .fc-bg {
   	background: none; 
}

#calendar table {
	margin-bottom:0;
	}
	
#calendar table thead tr th,
#calendar table tfoot tr th,
#calendar table tfoot tr td,
#calendar table tbody tr th, 
#calendar table tbody tr td,
#calendar table tr td {
    line-height: inherit;
	 }	
	
#calendar table thead {
	background:none;
	}

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
	border-color: #9ea5b5;
}


.fc-row .fc-content-skeleton td, .fc-row .fc-helper-skeleton td {
	background: none;
	border-color: transparent;
	border-bottom: 0;
}

.fc-unthemed td.fc-today {
	background: #F3FBFF;
	border-top: 1px solid #b1b4bf;
	border-left: 1px solid #b1b4bf;
}


.fc button {
	font-size: .9em; /* normalize */
}

.fc-state-default {
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-decoration: none;
  text-align: center;
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  display: inline-block;
  padding-top: 1rem;
  padding-right: 2rem;
  padding-bottom: 1.0625rem;
  padding-left: 2rem;
  font-size: 1rem;
  background-color: #003C79;
  border-color: #003C79;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}

.fc-state-hover,
.fc-state-down,
.fc-state-active,
.fc-state-disabled {
	/*color: #333333;
	background-color: #e6e6e6;*/
}

.fc-state-hover {
	 background-color: #353536;
	text-decoration:none;
	background-position:none;
}

.fc-state-down,
.fc-state-active {
	background-color: #999999;
	background-image: none;
}

.fc-state-disabled {
	 background-color: #003C79 !important;
	opacity: 1 !important;
	 transition: background-color 300ms ease-out;
}

.fc-state-disabled:hover {
	 background-color: #353536;
	text-decoration:none;
}

.fc th {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: bold;
	color: #58595b;
	font-size:1rem;
}


.fc th:first-of-type {
	border-left: 2px solid #ffffff; 
}

.fc td {
	border-bottom: 2px solid #b1b4bf; 
	
}

.fc td.fc-today {
	border-style: double; /* overcome neighboring borders */
}

.fc-content-skeleton table thead, 
.fc-content-skeleton table tbody, 
.fc-content-skeleton table tfoot 
 {
	border:none;
}

.fc-event {
	border-radius:0;
	font-weight: 400; /* undo jqui's ui-widget-header bold */
}

.fc-day-grid-event {
	padding: 5px 1px;
	border: 1px solid #b1b4bf !important;
	text-align: left;
}

.fc-day-grid-event .fc-time,
.fc-day-grid-event .fc-title {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #000000;
    font-size: 1.1rem;
	line-height: 1;
	margin: 0 .2rem 0 0;
}

.fc-day-grid-event .fc-title .fc-title-type {
	font-size:.9rem;
}

.fc-day-grid-event .fc-title .fc-title-address, .fc-day-grid-event .fc-title .fc-title-city {
  	font-weight: normal;
	font-size:.75rem;
}

.fc-toolbar {
	height: 0rem;
	margin-bottom: 0;
}

.fc-toolbar .fc-left select{
	margin: 0;
	height: 3.4rem;
	background-color: #f6f6f6;
    border-radius: 0;
    border-right: 2px solid #b1b4bf;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #58595b;
    font-size: 1rem;
	padding: 0.5rem 1rem;
}

.fc-toolbar .fc-left {
	width: 30.04%;
}

.fc-toolbar h2 {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: bold;
	color: #5a5a5c;
	font-size:1rem;
}

.fc-toolbar .fc-state-hover,
.fc-toolbar .ui-state-hover {
	z-index: none;
}

.fc-ltr .fc-basic-view .fc-day-number {
	text-align: right;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #58595b;
    font-size: 1rem;
}

.fc-time-grid .fc-time span {
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #58595b;
    font-size: .8rem;
}

.fc-time-grid .fc-event,
.fc-time-grid .fc-bgevent {
	border: 1px solid #b1b4bf !important;
	text-align: left;
}

.fc-time-grid-event {
	border: 1px solid #b1b4bf;
}

.fc-time-grid-event .fc-time,
.fc-time-grid-event .fc-title {
	padding: 0 0 0 6px;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #000000;
    font-size: 1.1rem;
	line-height: 1;
}

.fc-time-grid-event .fc-time {
	margin: 6px 0 3px 0;
}

.fc-time-grid-event .fc-title .fc-title-type {
	font-size:.9rem;
}

.fc-time-grid-event .fc-title .fc-title-address, .fc-time-grid-event .fc-title .fc-title-city {
  	font-weight: normal;
	font-size:.75rem;
}

.fc-timeline .fc-divider {
	width: 0;
}

.fc-timeline .fc-cell-text {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: bold;
	color: #58595b;
	font-size:1rem;
	padding-left: 0;
	padding-right: 0;
}

.fc-widget-header .fc-cell-content {
	text-align:center;
}

h1.fancy::nth-letter(n) {
  display: inline-block;
  padding: 20px 10px;
}

.fc-timeline .fc-cell-role {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: normal;
	color: #5a5a5c;
	font-size:.8rem;
}

.fc-timeline .fc-cell-role::before {
	content: '\A';
    white-space: pre;
}

.fc-timeline .fc-title {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: bold;
	color: #000000;
	font-size:1.1rem;
}

.fc-timeline .fc-title .fc-title-type {
	font-size:.9rem;
	padding-left: 5px;
}

.fc-timeline .fc-title .fc-title-address, .fc-timeline .fc-title .fc-title-city {
  	font-weight: normal;
	font-size:.75rem;
	padding-left: 5px;
}

.fc-timeline .fc-head .fc-cell-content {
	padding-top: .4rem;
	padding-bottom: .4rem;
}

.fc-resource-area {
	width: 24.4%;
}

.fc-resource-area .fc-cell-content {
	padding-left: 1rem;
    padding-right: 1rem;
}

.fc-body .fc-resource-area .fc-cell-content { /* might BE the cell */
	padding-top: 1.3rem;
	padding-bottom: 1.3rem;
}

.fc-no-overlap .fc-body .fc-resource-area .fc-cell-content { /* might BE the cell */
	padding-top: 1.35rem;
	padding-bottom: 1.35rem;
}

.fc-time-area .fc-slats .fc-minor {
	border-color: #cbcacf;
}

.fc-time-area .fc-slats .fc-major {
	border-left: 2px solid #b1b4bf;
}

.fc-timeline-event {
	border-left: 1px solid #b1b4bf !important;
	border-right: 1px solid #b1b4bf !important;
	margin-left: -1px;
}

.fc-no-overlap .fc-timeline-event {
	border-left: 1px solid #b1b4bf;
	border-right: 1px solid #b1b4bf;
}

.fc-ltr .fc-timeline-event { 
	text-align: left; 
}

.fc-timeline-event .fc-content {
	padding: 0;
	line-height:1;
}

.fc-ltr .fc-timeline-event .fc-title {
	padding-left: 13px;
}

.fc-timeline-event.fc-not-start .fc-title,
.fc-body .fc-time-area .fc-following {
	line-height: 1;
	padding: 2px 0 0 8px !important;
	text-align: left;
}

.fc-title.fc-following .fc-title-type, .fc-title.fc-following .fc-title-address, .fc-title.fc-following .fc-title-city  {
	padding-left: 0; 
}

.fc-title.fc-following div {
	margin-right:3px !important;
}

.fc-timeline-event span.fc-time {
	font-size: .75rem;
	color:#000000;
}

.fc-no-overlap .fc-timeline-event.resource-week {
	padding: 0 0 2px 0;
}

.fc-timeline .resource-week .fc-title .fc-title-type {
	font-size: .8rem;
	line-height: 1.1;
}

.fc-ltr .fc-timeline-event.fc-not-start.resource-week .fc-title:before, .fc-ltr .fc-body .fc-time-area .fc-following:before {
	display:none;
}

.fc-toolbar.fc-header-toolbar {
	margin-bottom: 0em;
}

.fc-day-grid-event .fc-content {
	margin-left: .2rem;
}