@font-face {
    font-family: 'icomoon';
    src:  url('../fonts/icomoon.eot?e7ly90');
    src:  url('../fonts/icomoon.eot?e7ly90#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?e7ly90') format('truetype'),
    url('../fonts/icomoon.woff?e7ly90') format('woff'),
    url('../fonts/icomoon.svg?e7ly90#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
}

body {
    background-color: #fff;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-file-empty:before {
    content: "\e924";
}
.icon-file-picture:before {
    content: "\e927";
}
.icon-file-music:before {
    content: "\e928";
}
.icon-file-video:before {
    content: "\e92a";
}
.icon-file-zip:before {
    content: "\e92b";
}
.icon-circle-down:before {
    content: "\ea43";
}
.icon-file-pdf:before {
    content: "\eadf";
}
.icon-file-word:before {
    content: "\eae1";
}
.icon-file-excel:before {
    content: "\eae2";
}

.p-media-library .e-ml-header {
    font-size: 20px;
    font-weight: 500;
    margin: 16px 0 0;
    padding-left: 30px;
}
.p-media-library .e-ml-table {
    margin-bottom: 32px;
}
    .p-media-library .e-mlt-row {
        cursor: pointer;
    }
    .p-media-library .e-mlt-row.t-header,
    .p-media-library .e-mlt-row.t-no-desc {
        cursor: auto;
    }
    .p-media-library .e-mlt-row.t-desc {
        display: none;
    }
    .p-media-library .e-mlt-row.t-open .e-mltr-data {
        border-bottom: none;
    }
        .p-media-library .e-mlt-row .e-mltr-data.t-desc-control .e-mltrd-icon {
            display: block;
            font-size: 18px;
            transition: All 0.5s ease;
            -webkit-transition: All 0.5s ease;
            -moz-transition: All 0.5s ease;
            -o-transition: All 0.5s ease;
        }
        .p-media-library .e-mlt-row.t-open .e-mltr-data.t-desc-control .e-mltrd-icon {
            transform: rotate(180deg);
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
        }
        .p-media-library .e-mltr-data {
            padding: 0;
            vertical-align: middle;
        }
        .p-media-library .e-mltr-header.t-desc-control,
        .p-media-library .e-mltr-data.t-desc-control {
            width: 4%;
            padding: 0 0.5rem;
        }
        .p-media-library .e-mltr-header.t-title,
        .p-media-library .e-mltr-data.t-title {
            width: 40%;
            text-align: left;
            padding-left: 0;
        }
        .p-media-library .e-mltr-data.t-title .e-mltrd-icon {
            margin-right: 4px;
        }
        .p-media-library .e-mltr-header.t-file-size,
        .p-media-library .e-mltr-data.t-file-size {
            width: 40%;
        }
        .p-media-library .e-mltr-header.t-action,
        .p-media-library .e-mltr-data.t-action {
            width: 16%;
        }
        .p-media-library .e-mltr-data.t-desc {
            padding: 0;
        }
            .p-media-library .e-mltr-data.t-desc .e-mltrd-wrapper {
                display: none;
                padding: 0 0.625rem 1.4rem 8%;
                text-align: left;
            }