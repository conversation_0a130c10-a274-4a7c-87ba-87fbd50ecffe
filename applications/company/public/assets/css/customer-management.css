body {
    background-color: #fff;
}

div.loadingImage {
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    opacity: 0.5;
    text-align: center;
}
div.loadingImage {
    display: none;
}
div.loadingImage > img {
    position: absolute;
    top: 40%;
}

.projectNote {
    background-color: #dff1fc;
    margin-bottom: .25rem;
}

.switch.tiny {
    margin-bottom: 1rem;
}

.tooltip {
    position: relative;
    z-index: 1200;
}

.back-to-project a span {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-style: solid;
    border-width: 5px 0px 5px 8.7px;
    border-color: transparent transparent transparent #005ad0;
}

.back-to-project a:hover span, .back-to-project a:focus span {
    border-color: transparent transparent transparent #1C76AD;
}

.validating-email-message {
    display: none;
}

.validating-email-message .abort-validation {
    display: none;
}

@media screen and (min-width: 40em) {
    #editCustomerModal {
        width: 700px;
    }
}

.callout.primary.t-target {
    border: 2px solid black;
}