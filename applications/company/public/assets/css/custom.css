@charset "UTF-8";
.fc-license-message {
    display: none !important;
}

p {
	line-height: 1.4;}

p.no-margin {
	margin-bottom: 0;}

p span {
	font-size:.9rem;
	font-style:italic;
	color:#939598; }

hr {
	max-width: none;
}

ul,
ol,
dl {
	line-height: 1.4;
}

li span {
	font-size:.9rem;
	font-style:italic;
	color:#939598; }

.map-label {
	background-color: white;
	padding: 0 4px;
	border-radius: 10px;
	font-weight: bold;
}

.error-match {
	display: none;
	margin-top: -0.5rem;
	margin-bottom: 1rem;
	font-size: 0.75rem;
	font-weight: bold;
	color: #ec5840; }
.error-match.is-visible {
	display: block; }

.type-error {
	display: none;
	margin-top: -0.5rem;
	margin-bottom: 1rem;
	font-size: 0.75rem;
	font-weight: bold;
	color: #ec5840; }
.type-error.is-visible {
	display: block; }

.button {
	padding:0.6em 1.5em;
}

.button.secondary {
	color: #005ad0;
	border: 1px solid #005ad0;
	background-color: #ffffff;}

.button.secondary:hover, .button.secondary:focus {
	color: #006EFF;
	background-color: #ffffff;}

.button.hollow, .button.hollow:hover, .button.hollow:focus {
	background-color: #ffffff; }

.button-group.text-center {
	display:block;
}

.button-group .button:not(:last-child) {
	margin-right: -5px;
}


.is-dropdown-submenu > li:hover {
	background-color: #ffffff;
	color: #005ad0;
}

.is-dropdown-submenu > li:hover a {
	color: #005ad0;
}

.label {
	display: inline-block;
	padding: 0.33333rem 0.5rem;
	font-size: 0.8rem;
	line-height: 1;
	white-space: nowrap;
	cursor: default;
	border-radius: 0;
	background: #2199e8;
	color: #fefefe; }
.label.secondary {
	background: #777;
	color: #fefefe; }
.label.success {
	background: #3adb76;
	color: #fefefe; }
.label.warning {
	background: #ffae00;
	color: #fefefe; }
.label.alert {
	background: #ec5840;
	color: #fefefe; }
.label.round {
	border-radius: 1000px; }


.dashboard-title {
	font-family: 'Roboto', sans-serif;
	font-weight:700;
	color:#005ad0;
	font-size: 1.5rem;
	text-transform:uppercase;
	margin: 1rem 0 1rem 0; }	

.button { 
	text-transform:uppercase;
	font-weight:bold;
}	

.button.login {
	background-color: #005ad0;
	color: #ffffff;
}

.button.login:hover, .button.login:focus {
	background-color: #006EFF;
    color: #fefefe;
}

.right {
	float:right; }
	
.left {
	float:left; }	
	
th.headerSortUp { 
    background-image: url(../images/icons/sort-asc.gif);
	cursor: pointer; 
    font-weight: bold; 
    background-repeat: no-repeat; 
    background-position: -2% 50%; 
    padding-left: 20px; } 

th.headerSortDown { 
    background-image: url(../images/icons/sort-desc.gif);
	cursor: pointer; 
    font-weight: bold; 
    background-repeat: no-repeat; 
    background-position: -2% 50%; 
    padding-left: 20px; } 

body .fc {
	line-height:normal; }

div.table{
	display: table;
}
div.table > div{
	display: table-row;
}
div.table > div > div{
	display: table-cell;
	vertical-align: top;
}

div.dashboard-filter-bar{
	width: 100%;
}

div.dashboard-filter-bar div.daySelector{
	text-align: center;
}

div.dashboard-filter-bar div.daySelector > div{
	display: inline-block;
	vertical-align: middle;
	height: 100%;
	padding: .5rem 1rem 0 1rem;
}

div.arrow-left,
div.arrow-right{
	float: initial; /*Override the value from custom.css*/
}
div.dashboard-filter-bar .button-group {
	margin-right: auto; /*Override the value from custom.css*/
	display: flex;

}
div.dashboard-filter-bar .view{
	margin-top: 0; /*Override the value from custom.css*/
}

div.dashboard-filter-bar div.filter{
	width: 10%;
	border-left: 1px solid #b1b4bf;
}
div.dashboard-filter-bar div.todayButton{
	width: 10%;
}
div.dashboard-filter-bar div.daySelector{
	width: 55%;
}
div.dashboard-filter-bar div.viewSelector{
	margin: .5rem 0 0 0;
	/*width:25%;*/
}

.dashboard-filter-bar {
	border-top: 2px solid #b1b4bf;
    border-bottom: 1px solid #b1b4bf;
    background-color: #f6f6f6; }

.dashboard-filter-bar .filter .btn {
	width: 1.7rem;
	height: 1.7rem;
	border-radius: 2px 2px 2px 2px;
	background: #ffffff;
	position: relative;
	border: 1px solid #d1d3d4;
	transition: background-color 0.25s ease-out, color 0.25s ease-out;
	color: #8a8a8a;
	margin: .5rem 0 .3rem .5rem;
}

.dashboard-filter-bar .filter .btn:hover {
	background: #f6f6f6;
}

.dashboard-filter-bar .filter .btn.active {
	background-color: #005ad0;
	color: #ffffff;
}

.dashboard-filter-bar .today {
	color: #d1d3d4;
	font-family: 'Roboto', sans-serif;
	font-size:.7rem;
	background-color:#ffffff;
	border:1px solid #d1d3d4;
	border-radius:2px 2px 2px 2px;
	font-weight: normal;
	padding: 0.6em 1em;
    margin: .5rem 1rem 0rem 1rem; }

.dashboard-filter-bar .today:hover {
	color: #ffffff; }
	
.dashboard-filter-bar .view {
	color: #58595b;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
	font-size:.7rem;
	background-color:#ffffff;
	border:1px solid #d1d3d4;
	border-radius:2px 2px 2px 2px;
	padding: 0.6em 1em;
    margin-top: .9rem }
	
.dashboard-filter-bar .view:hover {
	color:#000000;
	background-color: #ffffff; }	
	
.dashboard-filter-bar .view.active {
	color: #ffffff;
	background-color:#c1c4cd; }	
	
.dashboard-filter-bar .view.active:hover {
	color: #58595b; }	

.dashboard-filter-bar .button-group	{
	margin-right: 1rem;
}

.dashboard-filter-bar .view.view-left {
	border-radius:5px 0px 0px 5px; }
	
.dashboard-filter-bar .view.view-center {
	border-radius:0px; }
	
.dashboard-filter-bar .view.view-right {
	border-radius:0px 5px 5px 0px; }	
	
	
.arrow-right {
  	width: 1.7rem;
  	height: 0;
  	padding-bottom: 1.6rem;
  	border-radius:2px 2px 2px 2px;
  	background: #ffffff;
  	position: relative;
  	border:1px solid #d1d3d4;
  	margin-top:0;
	transition: background-color 0.25s ease-out, color 0.25s ease-out;
	margin-bottom: .3rem;
}

.arrow-right:before {
  	content: '';
 	display: block;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	border-left: 6px solid #8a8a8a;
  	position: absolute;
  	top: 50%;
  	left: 50%;
  	margin: -5px 0 0 -3px;
}
  
.arrow-right:hover {
	background: #f6f6f6;
}
	
.arrow-left {
  	width: 1.7rem;
  	height: 0;
  	padding-bottom: 1.6rem;
  	border-radius:2px 2px 2px 2px;
  	background: #ffffff;
  	position: relative;
  	border:1px solid #d1d3d4;
  	margin-top:0;
  	float: right;
  	transition: background-color 0.25s ease-out, color 0.25s ease-out;
	margin-bottom: .3rem;
}
.arrow-left:before {
  	content: '';
  	display: block;
 	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent; 
	border-right:6px solid #8a8a8a; 
  	position: absolute;
  	top: 50%;
  	left: 50%;
  	margin: -5px 0 0 -4px;
}

.arrow-left:hover {
	background: #f6f6f6;
}

#calendarTitle {
	margin: 0;
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  	font-weight: bold;
	color: #5a5a5c;
	font-size:1rem;
	width: 70%;
  	text-align: center;
}
	
.button-group .button.bar {
	border:1px solid #9ea3b6;
	background-color: #ffffff;
	color: #59595b;
}
		
.button-group .button.bar:hover {
    color: #333333; }
	
.button-group .button.bar.active {
	border:1px solid transparent;
	background-color: #005ad0;
	color: #ffffff; }
	
.button-group .button.bar.active:hover {
    background-color: #006EFF;
    color: #fefefe; }
	
	
.button-group .button.bar.left {
	border-radius: 5px 0px 0px 5px;
	float: none; }
	
.button-group .button.bar.right {
	border-radius: 0px 5px 5px 0px;
	float: none; }

.tabs {
	background: none;
	border: none;
}

.tabs-title > a {
	padding: 1.25rem .8rem;
	font-size: .6rem;
	font-weight: bold;
	background: #e9e9e9;
	text-transform: uppercase;
	border-left: 1px solid #ffffff;
	text-align: center;
}

@media print, screen and (min-width: 63.9375em) {
	#project-tabs .tabs-title > a {
		font-size: .9rem;
	}
}

@media print, screen and (min-width: 74.9375em) {
	#project-tabs .tabs-title > a {
		font-size: .9rem;
	}
}



.tabs-title > a:focus, .tabs-title > a[aria-selected='true'] {
	background: #0d4777;
	color:#ffffff;
	border-left: none;
}

.tabs-content {
	background: none;
	transition: all 0.5s ease;
	margin-top: 1rem;
	border: none;
}

.callout {
	border: 1px solid #b1b4bf;
	border-radius: 0;
	color: #000000;
}

.callout.primary {
	background-color: #f6f6f6; }
.callout.secondary {
	background-color: #dff1fc; }

@media screen and (min-width: 40em) {
	.reveal.small {
		width: 33rem;
	}

	.reveal.tiny {
		width: 33rem;
	}

	.reveal.xtiny {
		width: 33rem;
	}
}

@media screen and (min-width: 63.9375em) {
	.reveal.tiny {
		width: 33rem;
	}
}

@media screen and (min-width: 74.9375em) {
	.reveal.tiny {
		width: 33rem;
	}
}


table {
	border-spacing:0;
}

table thead th, table thead td, table tfoot th, table tfoot td {
	text-align: center;
}

table tbody th, table tbody td {
}

table thead,
table tbody,
table tfoot {
	border: none;
	background-color: transparent }

table tfoot {
	border: none;
	background-color: transparent;
	background: #2089cb;
	color: #ffffff;
}

table tfoot td {
	text-align: center; }
table tbody tr:nth-child(even) {
  background-color:transparent; }

table tbody td {
	text-align: center;
}
table tbody th a.button,
table tbody td a.button {
	margin:0.75rem 0;
}

.column.no-pad, .columns.no-pad {
	padding-left: 0;
	padding-right: 0; }

.column.no-pad, .columns.no-pad-left {
	padding-left: 0; }
.column.no-pad, .columns.no-pad-right {
	padding-right: 0; }

.project-title {
	font-weight:bold;
	color:#005ad0;
	font-size: 1.5rem;
	margin: 1rem 0 1rem 0; }	
	
.page-title {
	font-weight:500;
	color:#005ad0;
	font-size: 1.5rem;
	margin: 1rem 0 1rem 0; }
	
table.phoneTable	 td {
	border-bottom:none; }	
	
table.cpTable	td, table.evaluationTable td {
    border-bottom: none;
}	
	
table.cpTable	td input, table.evaluationTable	td input, table.evaluationTable	td select {
    margin: 0;
}	
	
.deletePhone img, .deleteRow img {
	opacity: .3;
}

.deletePhone img:hover, .deleteRow img:hover {
	opacity: 1;
	}
	
th.headerSortUp { 
    background-image: url(../images/icons/sort-asc.png);
	cursor: pointer; 
    font-weight: bold; 
    background-repeat: no-repeat; 
    background-position: -2% 50%; 
    padding-left: 20px; 
} 


th.headerSortDown { 
    background-image: url(../images/icons/sort-desc.png);
	cursor: pointer; 
    font-weight: bold; 
    background-repeat: no-repeat; 
    background-position: -2% 50%; 
    padding-left: 20px;  
} 

.errorMsg {
		color: red;
    	font-size: .8rem;
    	/*height: .8rem;*/
    	margin: 0 0 .4rem 0;
		}
	
.save-pending {
    background: #F0F8FF;
}

.save-error {
	background:#FFCCCC;
}

.save-success {
	background: #90EE90 !important;
	}

.tabs.vertical .tabs-title > a {
    display: block;
    padding: .5rem .8rem;
    line-height: 1;
    font-size: .9rem;
    font-weight: normal;
    background: #e9e9eb;
    text-transform: none;
    border-left: none;
    text-align: left;
	border-top: 1px solid #ffffff;
}

.tabs.vertical .tabs-title.title > a {
  background: #005ad0;
    color: #ffffff;
}

.tabs.vertical .tabs-title > span {
	display: block;
    background-color: #005ad0;
    color: #ffffff;
    padding: .3rem .8rem;
    font-size: .9rem;
    font-weight: bold;
}

.tabs.vertical .tabs-title > span a {
    color: #ffffff;
    text-decoration: none;
    font-size: .9rem;
   	font-weight: bold;
    font-style: normal;	
}

.tabs.vertical .tabs-title.is-active > a, .tabs.vertical .tabs-title.title.is-active > a {
  	background: #0d4777;
    color: #ffffff;
}

[type='checkbox'], [type='radio'], [type='file'] {
    margin: 0;
}

.button.counter {
    padding-top: 0.78rem;
    padding-right: 0;
    padding-bottom: 0.78rem;
    padding-left: 0;
    font-size: 0.6875rem;
    margin: 0;
    width: 100%;
}

.close-button {
    position: absolute;
    color: #8a8a8a;
    right: 1rem;
    top: 0.5rem;
    font-size: 2em;
    line-height: 1;
    cursor: pointer;
}

.badge {
    display: inline-block;
    padding: .3rem .4rem .2rem .2rem;
    min-width: 1.2rem;
    font-size: 0.7rem;
    text-align: center;
    border-radius: 50%;
    background: #005ad0;
    color: #fefefe;
	position: absolute;
    top: .3rem;
    right: .5rem;
}

.no-margin {
	margin:0;
}

.no-margin-bottom {
	margin-bottom: 0;
}

.mce-btn.mce-btn-small button span.mce-txt {
    color: #005ad0;
    font-weight: bold;
}

.mce-btn.mce-btn-small button i.mce-caret {
    border-top: 4px solid #005ad0;
}
 
.ui-autocomplete .ui-menu-item a {
	font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
}

.ui-menu .ui-menu-item a.ui-state-focus,
.ui-menu .ui-menu-item a.ui-state-active {
	background:#005ad0;
    color:#ffffff;
}

.has-tip {
     border-bottom: none; 
}

.tooltip {
	z-index: 1007;
}

.mce-btn-group:last-child {
    float:right;
    border-left: none !important;
}

hr.roles {
	margin:.8rem auto;
}

.switch {
	margin-bottom: 0;
}

.home-login {
    color: #999999;
    font-size: .7rem;
}


.progress-meter {
  position: relative;
  display: block;
  width: 0%;
  height: 100%;
  background-color: #005ad0;
  border-radius: 8px; }

   .button.black {
  color: #4f4f4f;
     border: 1px solid #ffffff;
    background-color: #ffffff;}
  .button.black:hover, .button.black:focus {
      color: #000000; }    

.servicesLabel {
	font-size: 1rem;
	color:#005AD0;
}

.form-warning {
    display: none;
    margin-top: -0.5rem;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
    color: #ec5840;
}

.fc-event .fc-bg {
   	background: none; 
}

.l2 {
	padding-left: 1em;
}

#filterSelect .select2-container .select2-selection--single {
	height: 40px;
}

#filterSelect .select2-container--default .select2-selection--single {
    background-color: #f6f6f6;
    border-radius: 0;
    border-right: 2px solid #b1b4bf;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: bold;
    color: #58595b;
    font-size: 1rem;
    padding: .5rem .75rem;
    border-left: 0;
    border-top: 0;
    border-bottom: 0;
}

#filterSelect .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 22px;
    color: #58595b
}

#filterSelect .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #58595b;
}

#filterSelect .select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 6px;
    right: 10px;
}

.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
	font-weight: bold;
	line-height: normal;
}

.content-wrapper {
	padding: 0;
}

html.is-reveal-open {
	overflow: inherit;
}
html.is-reveal-open body {
	width: 100%;
}

body.is-reveal-open {
	overflow: hidden;
}
