body {
    background-color: #fff;
}

.locationData {
    font-size: 80%;
    display: none;
}

#scheduledStartDate, #scheduledEndDate, #scheduledStartTime, #scheduledEndTime  {
    background-color: #fefefe;
    cursor: default;
}

.ui-menu-item {
    color: #0a0a0a;
    background-color: #fefefe;
    font-size: 1rem;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: normal;
}

.ui-selectmenu-menu {
    padding: 0;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
}
.ui-selectmenu-menu .ui-menu {
    overflow: auto;
    overflow-x: hidden;
    padding-bottom: 1px;
}
.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
    font-size: 1em;
    font-weight: bold;
    line-height: 1.5;
    padding: 2px 0.4em;
    margin: 0.5em 0 0 0;
    height: auto;
    border: 0;
}
.ui-selectmenu-open {
    color: #0a0a0a;
    background-color: #fefefe;
    font-size: 1rem;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: normal;
    display: block;
}
.ui-selectmenu-text {
    color: #0a0a0a;
    background-color: #fefefe;
    font-size: 1rem;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-weight: normal;
}
.ui-selectmenu-button.ui-button {
    text-align: left;
    white-space: nowrap;
    width: 14em;
    border-radius: 5px;
    height: 2.4375rem;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #cacaca;
    box-shadow: inset 0 1px 2px rgba(10, 10, 10, 0.1);

}
.ui-selectmenu-icon.ui-icon {
    float: right;
    margin-top: 0;
}

.overflow {
    height: 150px;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
    background: #2089cb !important;
    color: #fff !important;
    border-radius: 5px !important;
}

.ui-menu-item-wrapper {
    padding-top: 2px;
    padding-left: 5px;
}

.subsource {
    font-weight: bolder;
}

.select2.select2-container {
    width: 100% !important;
    height: 32px;
}

label .projectNameSelect2 {
    line-height: 0;
}

div.loadingImage {
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #eee;
    opacity: 0.5;
    text-align: center;
}
div.loadingImage {
    display: none;
}
div.loadingImage > img {
    position: absolute;
    top: 40%;
}
#calendarMap {
    height: 320px;
    border: 1px solid #000;
}

.lightbox {
    display: none;
}

.calendar-title {
    font-weight: bold;
    color: #005ad0;
    font-size: 1.5rem;
    padding: .5rem;
    margin: 0;
    border-left: 2px solid #b1b4bf;
    border-top: 2px solid #b1b4bf;
    border-right: 2px solid #b1b4bf;
}

.dashboard-filter-bar {
    border-left:1px solid #b1b4bf;
    border-right:2px solid #b1b4bf;
}

#calendarShow {
    overflow-y: auto;
}

.section-title {
    font-size: 1.1rem;
    color: #005ad0;
}

.section-divider {
    margin-bottom: .5rem;
}

.appointment-summary {
    display:none;
}

#calendarContainer {
    display:none;
}

#calendar {
    overflow:auto;
}

.projectNote {
    background-color: #dff1fc;
    margin-bottom: .25rem;
}

.switch.tiny {
    margin-bottom: 1rem;
}

.duplicateResult {
    display: none;
}

.internalNote {
    background-color: #dff1fc;
}

.top {
    margin-top: 1rem;
}

.bottom {
    margin-bottom: 1rem;
}

[name="contactEmail"].is-invalid-input {
    margin-bottom: .5rem;
}

.validating-email-message {
    display: none;
}

.validating-email-message .abort-validation {
    display: none;
}

.f-f-input.is-invalid-input {
    color: #22252E;
    background-color: #FFFFFF;
    border: 1px #CB4848 solid;
    border-radius: 3px;
    box-shadow: 0 0 0 4px #FFEBEC;
}

.newCustomerDisplay .medium-12.columns .row {
    margin-bottom: 8px;
}
.newCustomerDisplay .medium-12.columns .row.accordion {
    margin-top: 16px;
}

.phoneTable td {
    padding: 0.5rem 0.25rem 0.25rem;
}

.phoneTable tr .description {
    padding-left: 0;
    padding-right: 8px;
}

.phoneTable tr .delete {
    padding-right: 0;
}

.emailTable tr .name {
    padding-left: 0;
    padding-right: 8px;
}

.projectNameSelect2 .select2-search.select2-search--inline {
    height: 30px !important;
}

.projectNameSelect2 .select2-search__field {
    margin-top: 0 !important;
    height: 30px !important;
}

.projectNameSelect2 .selection .select2-selection--multiple {
    height: 32px !important;
    border-radius: 3px !important;
    border: 1px #C2C7D9 solid;
    padding-left: 5px;
    padding-bottom: 5px;
    transition: none;
    color: #22252E;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'%3e%3cpath fill='none' d='M0 0h24v24H0z'/%3e%3cpath d='M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z' fill='rgba(138,146,169,1)'/%3e%3c/svg%3e");
    background-size: 18px 18px;
    background-repeat: no-repeat;
    background-position: right 6px center;
    padding-right: 28px;
    background-origin: border-box;
}

.projectNameSelect2 .select2-selection__rendered {
    height: 30px;
    display: inline !important;
}

.projectNameSelect2 .select2-selection__choice {
    margin-left: 5px;
}

.error-match {
    margin-top: 0.5rem;
    margin-bottom: 0;
}
