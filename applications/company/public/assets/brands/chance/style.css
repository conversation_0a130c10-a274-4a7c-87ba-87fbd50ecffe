a {
    color: #29629C;
    text-decoration: none;
}

a:hover, a:focus {
    color: #717477;
}

.button, .button.disabled, .button[disabled] {
    background-color: #2c2a29;
    color: #fff;
}

.button.disabled:hover, .button[disabled]:hover {
    background-color: #2c2a29;
    color: #717477;
}

.button:hover, .button:focus {
    background-color: #374353;
}

.button.secondary {
    color: #2c2a29;
    background-color: #ffffff;
    border-color: #2c2a29;
}

.button.secondary:hover, .button.secondary:focus {
    color: #4a4b4d;
}

.button.login {
    background-color: #FDD100;
    color: #75787B;
}

a.button {
    text-decoration: none;
}

.button.login:hover, .button.login:focus {
    background-color: #f2c905;
    color: #000000;
}

.project-status {
	border-left-color: #75787B;
}

.projectStatusTab {
	background-color: #2c2a29;
}

.projectStatusTab span {
    color: #fff;
}

.projectStatusTitle {
	color: #000000;
}

.sales-bucket .salesperson {
    color: #2c2a29;
}

.projectStatusStep a {
    text-decoration: none;
}

.button-group .button.bar.active {
    background-color: #2c2a29;
    color: #FDD100;
}

.button-group .button.bar.active:hover {
    background-color: #000000;
}

input:checked ~ .switch-paddle {
    background-color: #2c2a29;
    color: #fff;
}

.switch-paddle {
	color: #75787B;
}

.badge.alert {
    background-color: #ec5840;
    color: #fefefe; 
}

.dashboard-title {
	color: #2c2a29;
}

#filterGroup .filter-section .section-title {
    background-color: #2c2a29;
}

#filterGroup .filter-section .section-title > span {
    color: #ffffff;
}

.dashboard-filter-bar .filter .btn.active {
    background-color: #FDD100;
    color: #75787B;
}

.project-title {
	color: #2c2a29;
}

.tabs-title > a:focus, .tabs-title > a[aria-selected='true'], #to-customer > a:focus,
 #to-customer > a[aria-selected='true'] {
    background-color: #ffffff;
    color: #2c2a29;
}

.tabs-title > a:hover {
    background: #cecece;
    color: #75787B;
}

table thead, table tfoot {
    background-color: #29629C;
    color: #ffffff;
}

.tabs-title > a {
    color: #75787B;
    text-decoration: none;
}

.tabs-title > a[aria-selected='true']:hover {
    color: #000000;
    background: #ffffff;
}

.mce-btn.mce-btn-small button span.mce-txt {
    color: #75787B;
}

.mce-btn.mce-btn-small button i.mce-caret {
    border-top-color: #75787B;
}

.sp-container button {
    background-color: #FDD100;
    color: #75787B;
}

.sp-container button:hover {
    background-color: #FDD100;
    color:#000000;
}

.sp-cancel {
    color: #75787B !important;
}

.sp-cancel:hover {
    color: #000000 !important;
}

.ui-menu .ui-menu-item a.ui-state-focus,
.ui-menu .ui-menu-item a.ui-state-active {
	background-color:#FDD100;
    color:#75787B;
}

.newCustomerDisplay .section-title {
    color: #75787B;
    font-weight: bold;
}

.accordion-title::before {
    color: #75787B;
}

.accordion-title {
    color: #75787B;
}

#marketingMetricsDiv .source {
    background-color: #fbe373;
}

#marketingReport .source, #marketingReport #allMarketing, #overallSales {
    background-color: #fbe373;
}

#overallSalesReport table  tr:nth-child(even), #employeeSalesReport table  tr:nth-child(even) {background: #fbe373}

.heading.mainservice {
    background-color: #fbe373;
}

.calendarTime:hover {
    background-color: #fbe373;
}

.servicesLabel {
    color: #75787B;
}

.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus {
    color: #75787B !important;
    background-color: #FDD100 !important;
}

.mce-menu-item:hover .mce-text, .mce-menu-item.mce-selected .mce-text, .mce-menu-item:focus .mce-text {
    color: #75787B !important;
}

.notesTab, .notesTabText {
    background-color: #75787B;
}

.tabs.vertical .tabs-title > span, .tabs.vertical .tabs-title.title > a {
    background-color: #2c2a29;
    color: #FDD100;
}

.tabs.vertical .tabs-title.is-active > a, .tabs.vertical .tabs-title.title.is-active > a {
   	background-color: #000000;
    color: #ffffff;
}

div.geocodeResult{
	color: #75787B;
}

.registration-title, .signup-title, .forgot-title, .reset-title, .unsubscribe-title, .loginTitle {
	color: #75787B;
    font-weight: bold;
}

.bestDealCallout {
    border-color: #FDD100;
}

.bestDealText {
    color: #75787B;
}

.unlimitedUsers {
    color: #75787B;
    font-weight: bold;
}

.freeTrialText {
    color: #75787B;
    font-weight: bold;
}

.home-login a:hover {
    color:#75787B;
}

.callout.success {
    background-color: #e1faea;
}

#to-customer > a {
    text-decoration: none;
}

ul.thumbnails.image_picker_selector li .thumbnail.selected {
    background: #FDD100;
}

.acct-module .side-menu ul li a {
    text-decoration: none;
}

.acct-module .side-menu ul li.active a {
     color: #75787B;
 }

.drop-content .actions-menu a:hover, a:focus {
    color: #000000;
    text-decoration: none;
}

.back-to-project a span {
    border-color: transparent transparent transparent #75787B;
}

.back-to-project a:hover span, .back-to-project a:focus span {
    border-color: transparent transparent transparent #75787B;
}

#to-customer a span {
    border-color: transparent #75787B transparent transparent;
}

#to-customer a:hover {
    background-color: #cecece;
}

.button.disabled.secondary, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
    background-color: #ffffff;
    color: #fefefe;
}

.crew-options .view:hover {
    color: #ffffff;
}
