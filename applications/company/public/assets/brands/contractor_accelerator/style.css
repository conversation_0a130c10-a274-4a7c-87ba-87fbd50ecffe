a {
    color: #005AD0;
}

a:hover, a:focus {
    color: #006EFF;
}

.button, .button.disabled, .button[disabled] {
	background-color: #005ad0;
    color: #fefefe;
}

.button.disabled:hover, .button[disabled]:hover {
    background-color: #005ad0;
    color: #fefefe;
}

.button:hover, .button:focus {
    background-color: #006EFF;
    color: #fefefe;
}

.button.secondary {
    color: #005ad0;
    border-color: #005ad0;
    background-color: #ffffff;
}

.button.secondary:hover, .button.secondary:focus {
    color: #006EFF;
}

.button[disabled].secondary, .button[disabled].secondary:hover {
    background-color: transparent;
    border-color: #006EFF;
    color: #006EFF;
}

.button.login {
    background-color: #005ad0;
    color: #ffffff;
}

.button.login:hover, .button.login:focus {
    background-color: #006EFF;
    color: #fefefe;
}

#projectStatus {
	border-left-color: #005ad0;
}

.projectStatusTab {
	background-color: #005ad0;
}

.projectStatusTab span {
	color: #fefefe;
}

.projectStatusTitle {
	color: #333333;
}

.sales-bucket .salesperson {
    color: #005ad0;
}

.button-group .button.bar.active {
   	background-color: #005ad0;
    color: #fefefe;
}

.button-group .button.bar.active:hover {
    background-color: #006EFF;
    color: #fefefe;
}

input:checked ~ .switch-paddle {
    background-color: #005ad0;
}

.switch-paddle {
	color: #fefefe;
}

.badge.alert {
    background-color: #ec5840;
    color: #fefefe; 
}

.dashboard-title {
	color: #005ad0;
}

.project-title {
	color: #005ad0;
}

.tabs-title a {
	color: #005ad0;
}

.tabs-title > a:focus, .tabs-title > a[aria-selected='true'], #to-customer > a:focus,
 #to-customer > a[aria-selected='true'] {
    background-color: #0d4777;
    color: #ffffff;
}

table thead, table tfoot {
    background-color: #005ad0;
    color: #ffffff;
}

.tabs-title > a:hover {
    color: #006EFF;
}

.tabs-title > a[aria-selected='true']:hover {
    color: #ffffff;
}

.mce-btn.mce-btn-small button span.mce-txt {
    color: #005ad0;
}

.mce-btn.mce-btn-small button i.mce-caret {
    border-top-color: #005ad0;
}

.sp-container button {
    background-color: #005ad0;
    color: #fefefe;
}

.sp-container button:hover {
    background-color: #006EFF;
    color:#fefefe;
}

.sp-cancel {
    color: #005ad0 !important;
}

.sp-cancel:hover {
    color: #006EFF !important;
}

.ui-menu .ui-menu-item a.ui-state-focus,
.ui-menu .ui-menu-item a.ui-state-active {
	background-color:#005ad0;
    color:#ffffff;
}

.accordion-title {
    color: #005ad0;
}

#marketingMetricsDiv .source {
    background-color: #dff1fc;
}

#marketingReport .source, #marketingReport #allMarketing, #overallSales {
    background-color: #dff1fc;
}

#overallSalesReport table  tr:nth-child(even), #employeeSalesReport table  tr:nth-child(even) {background: #dff1fc}

.heading.mainservice {
    background-color: #dff1fc;
}

.calendarTime:hover {
    background-color: #E4F3FD;
}

.servicesLabel {
    color: #005ad0;
}

.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus {
    color: #ffffff !important;
    background-color: #005ad0 !important;
}

.mce-menu-item:hover .mce-text, .mce-menu-item.mce-selected .mce-text, .mce-menu-item:focus .mce-text {
    color: #ffffff !important;
}

.notesTab, .notesTabText {
    background-color: #005ad0;
}

.tabs.vertical .tabs-title > span, .tabs.vertical .tabs-title.title > a {
    background-color: #005ad0;
    color: #ffffff;
}

.tabs.vertical .tabs-title.is-active > a, .tabs.vertical .tabs-title.title.is-active > a {
   	background-color: #0d4777;
    color: #ffffff;
}

div.geocodeResult{
	color: #005ad0;
}

.registration-title, .signup-title, .forgot-title, .reset-title, .unsubscribe-title {
	color: #005ad0;
}

.bestDealCallout {
    border-color: #005ad0;
}

.bestDealText {
    color: #005ad0;
}

.unlimitedUsers {
    color: #005ad0;
}

.freeTrialText {
    color: #005ad0;
}

.home-login a:hover {
    color:#006EFF;
}

.callout.success {
    background-color: #dbeef9;
}

.callout.success {
    background-color: #e1faea;
}

ul.thumbnails.image_picker_selector li .thumbnail.selected {
    background: #005ad0;
}
