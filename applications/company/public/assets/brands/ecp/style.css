a {
    color: #337ab7;
}

a:hover, a:focus {
    color: #000000;
}

.button, .button.disabled, .button[disabled] {
    background-color: #337ab7;
    color: #ffffff;
}

.button.disabled:hover, .button[disabled]:hover {
    background-color: #337ab7;
    color: #ffffff;
}

.button:hover, .button:focus {
    background-color: #0a0b0b;
    color: #ffffff;
}

.button.secondary {
    color: #0a0b0b;
    border-color: #0a0b0b;
    background-color: #ffffff;
}

.button.secondary, .button.secondary.disabled, .button.secondary[disabled] {
    background-color: #ffffff;
    color: #0a0b0b;
    border-color: #0a0b0b;
}

.button.secondary:hover, .button.secondary:focus {
    color: #0a0b0b;
}

.button.secondary:hover, .button.secondary.disabled:hover, .button.secondary[disabled]:hover {
    background-color: #ffffff;
    color: #0a0b0b;
}

.button.login {
    background-color: #337ab7;
    color: #ffffff;
}

.button.login:hover, .button.login:focus {
    background-color: #0a0b0b;
    color: #fefefe;
}

.project-status {
    border-left-color: #337ab7;
}

.projectStatusTab {
    background-color: #337ab7;
}

.projectStatusTab span {
    color: #ffffff;
}

.projectStatusTitle {
    color: #333333;
}

.sales-bucket .salesperson {
    color: #337ab7;
}

.button-group .button.bar.active {
    background-color: #337ab7;
    color: #ffffff;
}

.button-group .button.bar.active:hover {
    background-color: #0a0b0b;
    color: #ffffff;
}

input:checked ~ .switch-paddle {
    background-color: #337ab7;
}

.switch-paddle {
    color: #ffffff;
}

.badge.alert {
    background-color: #337ab7;
    color: #fefefe;
}

.dashboard-title {
    color: #337ab7;
}

.project-title {
    color: #337ab7;
}

.tabs-title a {
    color: #337ab7;
}

.tabs-title > a:focus, .tabs-title > a[aria-selected='true'], #to-customer > a:focus,
#to-customer > a[aria-selected='true'] {
    background-color: #337ab7;
    color: #ffffff;
}

table thead, table tfoot {
    background-color: #0068B2;
    color: #ffffff;
}

.tabs-title > a:hover {
    color: #000000;
}

.tabs-title > a[aria-selected='true']:hover {
    color: #ffffff;
}

.mce-btn.mce-btn-small button span.mce-txt {
    color: #337ab7;
}

.mce-btn.mce-btn-small button i.mce-caret {
    border-top-color: #337ab7;
}

.sp-container button {
    background-color: #0a0b0b;
    color: #ffffff;
}

.sp-container button:hover {
    background-color: #337ab7;
    color:#ffffff;
}

.sp-cancel {
    color: #337ab7 !important;
}

.sp-cancel:hover {
    color: #000000 !important;
}

.ui-menu .ui-menu-item a.ui-state-focus,
.ui-menu .ui-menu-item a.ui-state-active {
    background-color:#0a0b0b;
    color:#8c8c8c;
}

.accordion-title {
    color: #337ab7;
}

#marketingMetricsDiv .source {
    background-color: #dff1fc;
}

#marketingReport .source, #marketingReport #allMarketing, #overallSales {
    background-color: #dff1fc;
}

#overallSalesReport table  tr:nth-child(even), #employeeSalesReport table  tr:nth-child(even) {background: #dff1fc}

.heading.mainservice {
    background-color: #dff1fc;
}

.calendarTime:hover {
    background-color: #337ab7;
}

.servicesLabel {
    color: #337ab7;
}

.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus {
    color: #8c8c8c !important;
    background-color: #0a0b0b !important;
}

.mce-menu-item:hover .mce-text, .mce-menu-item.mce-selected .mce-text, .mce-menu-item:focus .mce-text {
    color: #8c8c8c !important;
}

.notesTab, .notesTabText {
    background-color: #0a0b0b;
}

.tabs.vertical .tabs-title > span, .tabs.vertical .tabs-title.title > a {
    background-color: #0a0b0b;
    color: #8c8c8c;
}

.tabs.vertical .tabs-title.is-active > a, .tabs.vertical .tabs-title.title.is-active > a {
    background-color: #337ab7;
    color: #ffffff;
}

div.geocodeResult{
    color: #337ab7;
}

.registration-title, .signup-title, .forgot-title, .reset-title, .unsubscribe-title {
    color: #337ab7;
}

.bestDealCallout {
    border-color: #337ab7;
}

.bestDealText {
    color: #337ab7;
}

.unlimitedUsers {
    color: #337ab7;
}

.freeTrialText {
    color: #337ab7;
}

.home-login a:hover {
    color:#337ab7;
}

.callout.success {
    background-color: #e1faea;
}

.dashboard-filter-bar .filter .btn.active {
    background-color: #337ab7;
    color: #ffffff;
}

#filterGroup .filter-section .section-title {
    background: #337ab7;
}

.newCustomerDisplay .section-title {
    color: #337ab7;
}

.calendar-title {
    color:#337ab7;
}

.fancybox-close-small:after {
    color:#337ab7;
}

ul.thumbnails.image_picker_selector li .thumbnail.selected {
    background: #337ab7;
}

#to-customer a:hover span {
    border-color: transparent #000000 transparent transparent;
}
