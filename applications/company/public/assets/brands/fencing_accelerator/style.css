a {
    color: #66cc00;
}

a:hover, a:focus {
    color: #58A401;
}

.button, .button.disabled, .button[disabled] {
	background-color: #66cc00;
    color: #fefefe;
}

.button.disabled:hover, .button[disabled]:hover {
    background-color: #66cc00;
    color: #fefefe;
}

.button:hover, .button:focus {
    background-color: #58A401;
    color: #fefefe;
}

.button.secondary {
    color: #66cc00;
    border-color: #66cc00;
    background-color: #ffffff;
}

.button.secondary:hover, .button.secondary:focus {
    color: #58A401;
}

.button[disabled].secondary, .button[disabled].secondary:hover {
    background-color: transparent;
    border-color: #58A401;
    color: #58A401;
}

.button.login {
    background-color: #66cc00;
    color: #ffffff;
}

.button.login:hover, .button.login:focus {
    background-color: #58A401;
    color: #fefefe;
}

.project-status {
	border-left-color: #b1b4bf;
}

.projectStatusTab {
	background-color: #66cc00;
}

.projectStatusTab span {
	color: #fefefe;
}

.projectStatusTitle {
	color: #333333;
}

.sales-bucket .salesperson {
    color: #66cc00;
}

.sales-bucket .salesperson:hover {
    color: #58A401;
}

.button-group .button.bar.active {
   	background-color: #66cc00;
    color: #fefefe;
}

.button-group .button.bar.active:hover {
    background-color: #58A401;
    color: #fefefe;
}

input:checked ~ .switch-paddle {
    background-color: #66cc00;
}

.switch-paddle {
	color: #fefefe;
}

.badge.alert {
    background-color: #ec5840;
    color: #fefefe; 
}

.dashboard-title {
	color: #4F4F4F;
}

#filterGroup .filter-section .section-title {
    background-color: #66cc00;
}

.project-title {
	color: #4F4F4F;
}

.tabs-title a {
	color: #75787B;
}

.tabs-title > a:hover, #to-customer > a:focus,
#to-customer > a[aria-selected='true'] {
    background: #cecece;
    color: #75787B;
}

.tabs-title > a:focus, .tabs-title > a[aria-selected='true'] {
    background-color: #ffffff;
    color: #4F4F4F;
}

.paginate_button.current {
    background-color: #66cc00;
}

table thead, table tfoot {
    background-color: #4F4F4F;
    color: #ffffff;
}

.tabs-title > a[aria-selected='true']:hover {
    color: #000000;
    background: #ffffff;
}

.mce-btn.mce-btn-small button span.mce-txt {
    color: #66cc00;
}

.mce-btn.mce-btn-small button i.mce-caret {
    border-top-color: #66cc00;
}

.sp-container button {
    background-color: #66cc00;
    color: #fefefe;
}

.sp-container button:hover {
    background-color: #58A401;
    color:#fefefe;
}

.sp-cancel {
    color: #66cc00 !important;
}

.sp-cancel:hover {
    color: #006EFF !important;
}

.ui-menu .ui-menu-item a.ui-state-focus,
.ui-menu .ui-menu-item a.ui-state-active {
	background-color:#66cc00;
    color:#ffffff;
}

.accordion-title {
    color: #66cc00;
}

.newCustomerDisplay .section-title {
    color: #4F4F4B;
}

.progress-meter {
    background-color: #66cc00;
}

#marketingMetricsDiv .source {
    background-color: #e5e4e4;
}

#marketingReport .source, #marketingReport #allMarketing, #overallSales {
    background-color: #e5e4e4;
}

#overallSalesReport table  tr:nth-child(even), #employeeSalesReport table  tr:nth-child(even) {background: #e5e4e4}

.heading.mainservice {
    background-color: #e5e4e4;
}

.calendarTime:hover {
    background-color: #E4F3FD;
}

.servicesLabel {
    color: #66cc00;
}

.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus {
    color: #ffffff !important;
    background-color: #66cc00 !important;
}

.mce-menu-item:hover .mce-text, .mce-menu-item.mce-selected .mce-text, .mce-menu-item:focus .mce-text {
    color: #ffffff !important;
}

.notesTab, .notesTabText {
    background-color: #66cc00;
}

.tabs.vertical .tabs-title > span, .tabs.vertical .tabs-title.title > a {
    background-color: #66cc00;
    color: #ffffff;
}

.tabs.vertical .tabs-title.is-active > a, .tabs.vertical .tabs-title.title.is-active > a {
   	background-color: #0d4777;
    color: #ffffff;
}

div.geocodeResult{
	color: #66cc00;
}

.registration-title, .signup-title, .forgot-title, .reset-title, .unsubscribe-title {
	color: #66cc00;
}

.bestDealCallout {
    border-color: #66cc00;
}

.bestDealText {
    color: #66cc00;
}

.unlimitedUsers {
    color: #66cc00;
}

.freeTrialText {
    color: #66cc00;
}

.home-login a:hover {
    color:#58A401;
}

.callout.success {
    background-color: #dbeef9;
}

.callout.success {
    background-color: #e1faea;
}

ul.thumbnails.image_picker_selector li .thumbnail.selected {
    background: #66cc00;
}

.search-result:hover .search-result-label, .search-result:hover .search-result-description {
    color: #4F4F4F;
}

.back-to-project a {
    color: #75787B;
}

.back-to-project a span {
    border-color: transparent transparent transparent #75787B;
}

.back-to-project a:hover span, .back-to-project a:focus span {
    border-color: transparent transparent transparent #75787B;
}

#to-customer > a {
    color: #75787B;
}

#to-customer a span {
    border-color: transparent #75787B transparent transparent;
}

#to-customer a:hover {
    background-color: #cecece;
}
