$(document).keypress(function (e) {
    if (e.which == 13) {
        if ($('input[name="customer"]').val() != '') {
            searchCustomers();
        }
    }
});

var emailAddress = null;

window.Layout.setModeWindow();
window.Layout.setTitle('Customer Information');

$(document).ready(function () {

    //Get all marketing types for the referral dropdown
    getMarketingTypes();

    //Add Phone Row on Click
    $('#phoneTable .addPhone').click(addPhoneRow);

    //Edit Customer Modal
    $('#editCustomerInfo').click(editCustomer);

    //Edit Customer Modal
    $('#task-feature-not-enabled').click(taskFeatureNotEnabledModal);

    //Close Customer Modal
    $('#closeEditCustomerModal').click(closeEditCustomer);

    //Save Customer Modal
    $('#saveEditCustomerModal').click(saveEditCustomer);

    //Move Property Modal
    $('.callout.primary #moveProperty').each(function(){
        $(this).click(movePropertyModal);
    });

    //Close Move Property Modal
    $('#closeMovePropertyNew').click(closeMoveProperty);
    $('#closeMovePropertyExisting').click(closeMoveProperty);

    $('#existingCustomer').click(function(){
        if (!$('#existingCustomer').hasClass('active')) {
            $('#existingCustomer').addClass('active');
            $('#newCustomer').removeClass('active');
            $('#existingCustomerSection').show();
            $('#newCustomerSection').hide();
        }
    });

    $('#newCustomer').click(function(){
        if (!$('#newCustomer').hasClass('active')) {
            $('#newCustomer').addClass('active');
            $('#existingCustomer').removeClass('active');
            $('#newCustomerSection').show();
            $('#existingCustomerSection').hide();

            //Add Row For Phone
            addPhoneRowUser();

            $('#userPhoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
        }
    });

    $('#searchCustomers').click(searchCustomers);

    $('#userPhoneTable .addPhone').click(addPhoneRowUser);
    $('#userPhoneTable .deletePhone').click(deletePhoneRowUser);
    $('#userPhoneTable input.isPrimary').change(isPrimaryCheckUser);

    $('.movePropertyToExisting').click(movePropertyToExisting);

    $('#movePropertyToNew').click(movePropertyToNew);

    $('#newCustomerSection input[required], #newCustomerSection select[required]').change(function(){
        if ($(this).val() == ''){
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').addClass('is-visible');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }
    });

    //Enable/Disable Email Field On Click
    $('#editCustomerModal input[name="noEmailRequired"]').change(function (){
        if ($('#editCustomerModal input[name="noEmailRequired"]:checked').length > 0){
            $('#editCustomerModal input[name="email"]').prop('disabled', true);
            $('#editCustomerModal input[name="email"]').val('');
            $('#editCustomerModal input[name="email"]').removeClass('customerRequired');
            $('#editCustomerModal input[name="email"]').parent().removeClass('is-invalid-label');
            $('#editCustomerModal input[name="email"]').removeClass('is-invalid-input');
            $('#editCustomerModal input[name="email"]').parent().find('.form-error').removeClass('is-visible');
        } else {
            $('#editCustomerModal input[name="email"]').prop('disabled', false);
            $('#editCustomerModal input[name="email"]').val($('#fetchedEmail').text());
            $('#editCustomerModal input[name="email"]').addClass('customerRequired');
        }
    });

    //Enable/Disable Email Field On Click Move Project Modal
    $('#movePropertyModal input[name="noEmailRequired"]').change(function (){
        if ($('#movePropertyModal input[name="noEmailRequired"]:checked').length > 0){
            $('#movePropertyModal input[name="email"]').prop('disabled', true);
            $('#movePropertyModal input[name="email"]').val('');
            $('#movePropertyModal input[name="email"]').removeAttr('required');
            $('#movePropertyModal input[name="email"]').parent().removeClass('is-invalid-label');
            $('#movePropertyModal input[name="email"]').removeClass('is-invalid-input');
            $('#movePropertyModal input[name="email"]').parent().find('.form-error').removeClass('is-visible');
        } else {
            $('#movePropertyModal input[name="email"]').prop('disabled', false);
            $('#movePropertyModal input[name="email"]').attr('required', true);
        }
    });

    $('#editCustomerModal input.customerRequired').change(function () {
        if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').addClass('is-visible');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }
    });
    //remove required if no email is required
    if (customer_info.no_email_required == 1){
        $('#editCustomerModal input[name="email"]').removeClass('customerRequired');
    }

    //Get Resources
    $.ajax({
        url: window.fx_url.BASE + 'getResources.php',
        dataType: 'json',
        type: 'GET',
        contentType: 'application/x-www-form-urlencoded',
        data: {
            filter: 'sales'
        },
        success: function (response) {
            $('#filterSelect').append('<select disabled id="filterResources"><option value="">View All</option><option selected value="sales">Sales</option><option value="installation">Installation</option></select>');
            if (response != null) {
                $.each(response, function (i, item) {
                    if (item.id != 0) {
                        $('select[name="projectSalesperson"]').append('<option value="' + item.id + '">' + item.title + '</option>');
                    }
                });

                var userID = $('#userID').text();

                if ($('select[name="projectSalesperson"]').has('option[value="' + userID + '"]').length > 0)
                    $('select[name="projectSalesperson"] > option[value="' + userID + '"]').prop('selected', true);

            }

        }, error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }

    });

    $('[name="email"]').on('blur.fx', function() {
        var email = $(this);
        var emailValue = email.val().trim();
        if (emailAddress !== emailValue) {
            emailAddress = emailValue ? emailValue : null;
            if (emailAddress) {
                validateEmailAddress(email);
            }
        }
    });

    $('[data-dismiss-email]').on('click.fx', function() {
        var $this = $(this);
        var email = $this.parent().parent().find('[name="email"]');
        email.parent().removeClass('is-invalid-label');
        email.removeClass('is-invalid-input');
        email.parent().find('.validEmail').removeClass('is-visible');
    });
}); //end of document ready


var getMarketingTypes = function () {
    $('#loading-image').show();
    $.ajax({
        url: window.fx_url.BASE + 'getMarketingTypes.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        success: function (response) {
            var index = 0;
            $.each(response, function (i, item) {
                index = i +1;
                if (item.parentMarketingTypeID == null){ //source
                    $('#referralDropdown').append('<optgroup class="source" data-id="' + item.marketingTypeID + '" label="' + item.marketingTypeName + '"></optgroup>');
                }
                else{ //subsource
                    $('.source[data-id="' + item.parentMarketingTypeID + '"]').append('<option class="subsource" value="' + item.marketingTypeID + '">' + item.marketingTypeName + '</option>');
                }
            });
            $('#loading-image').hide();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var movePropertyModal = function(){
    var idToMove = $(this).parent().parent().parent().attr('id');

    $('#movePropertyModal #idToMove').val(idToMove);

    var pageHeight = $( window ).height() * .85;

    $('#movePropertyModal').css('height',pageHeight);

    $('#movePropertyModal').foundation('open');
};


var closeMoveProperty = function(){
    var movePropertyModal = $('#movePropertyModal');

    movePropertyModal.find('#newCustomerSection input[required], #newCustomerSection select[required]').each(function(){
        $(this).parent().removeClass('is-invalid-label');
        $(this).removeClass('is-invalid-input');
        $(this).parent().find('.form-error').removeClass('is-visible');
    });

    movePropertyModal.find('.error-match, .form-error').removeClass('is-visible');
    movePropertyModal.find('#existingCustomer').addClass('active');
    movePropertyModal.find('#newCustomer').removeClass('active');
    movePropertyModal.find('#existingCustomerSection').show();
    movePropertyModal.find('#newCustomerSection').hide();

    movePropertyModal.find('input[name="customer"]').val('');
    movePropertyModal.find('input[name="firstName"]').val('');
    movePropertyModal.find('input[name="lastName"]').val('');
    movePropertyModal.find('input[name="email"]').val('');
    movePropertyModal.find('input[name="businessName"]').val('');
    movePropertyModal.find('input[name="ownerAddress"]').val('');
    movePropertyModal.find('input[name="ownerAddress2"]').val('');
    movePropertyModal.find('input[name="ownerCity"]').val('');
    movePropertyModal.find('select[name="ownerState"]').val('');
    movePropertyModal.find('input[name="ownerZip"]').val('');

    movePropertyModal.find('#newCustomerSection input[name="projectDescription"]').prop('required', false);

    movePropertyModal.find('.accordion').foundation('up', movePropertyModal.find('.accordion .accordion-item.is-active .accordion-content'));

    movePropertyModal.find('#userPhoneTable tbody tr').not(':first').remove();
    movePropertyModal.find('#existingCustomersTable tbody tr').not(':first').remove();
    movePropertyModal.find('#existingCustomerDisplay').hide();

    movePropertyModal.find('#idToMove').val('');

    movePropertyModal.find('#movePropertyToNew').prop('disabled', false);
    movePropertyModal.find('.validating-email-message').hide();
    movePropertyModal.find('.abort-validation').hide();
    if (validationRequest !== null) {
        validationRequest.abort();
        validationRequest = null;
    }

    movePropertyModal.foundation('close');
};


var addPhoneRowUser = function () {
    $cloneBlankRow = $('#userPhoneTable').find('tbody tr:first').clone().css('display', '');
    $('#userPhoneTable').find('tbody').append($cloneBlankRow);
    $('#userPhoneTable').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheckUser);
    $('#userPhoneTable').find('tbody > tr:last-child .delete > a').click(deletePhoneRowUser);

    $('#userPhoneTable').find('tbody > tr:last-child input.Phone').mask('(*************');

    if (!$('#userPhoneTable input.isPrimary:checked')) {
        $('#userPhoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
    }
};

var deletePhoneRowUser = function () {

    if ($(this).parent().parent().hasClass('dbRow')) {
        $(this).parent().parent().find('select').attr('delete','delete');
        $(this).parent().parent().css('display','none');

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#userPhoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);

        if ($('#userPhoneTable tbody').children().length == 1)
            addPhoneRowUser();
    } else {
        $(this).parent().parent().remove();

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#userPhoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);

        if ($('#userPhoneTable tbody').children().length == 1)
            addPhoneRowUser();
    }
};

var isPrimaryCheckUser = function () {

    $('#userPhoneTable').find('input.isPrimary').prop('checked', false);
    $(this).prop('checked', true);
};

var isEmail = function(email) {
	var re = /^[A-Za-z0-9!#$%&'*+\-/=?^_`{|}~.]+@(?:[A-Za-z0-9\-]+\.[A-Za-z0-9\-]+)+$/;
	return re.test(email);
};

var searchCustomers = function () {
    $('#existingCustomerDisplay').css('display', '');

    $('#existingCustomersTable tbody tr').not(':first').remove();

    var keyword = $('input[name="customer"]').val().trim();

    if (keyword !== '') {
        $.ajax({
            url: window.fx_url.API + 'customers/search',
            dataType: 'json',
            data: {
                term: keyword,
                source: 'property-move'
            },
            success: function (response) {
                if (response.status !== 1) {
                    alert('Unable to complete search, please contact support');
                    return;
                }

                $('input[name="customer"]').blur();

                $.each(response.result, function (i, item) {
                    var $cloneRow = $('#existingCustomersTable tbody tr:first').clone().css('display', '');

                    $cloneRow.find('td.name').text(item.name);
                    $cloneRow.find('td.address').text(item.address);
                    $cloneRow.find('td.phone').text(item.phoneNumber);
                    $cloneRow.find('td.email').text(item.email === null ? 'N/A' : item.email);
                    $cloneRow.find('td.link').html('<a class="button xtiny movePropertyToExisting" id="' + item.id + '">Move</a>');

                    $('#existingCustomersTable tbody').append($cloneRow);

                    $('#existingCustomersTable tbody tr:last-child a.movePropertyToExisting').click(movePropertyToExisting);
                });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    }
};

var movePropertyToExisting = function(){
    $('#loading-image').show();

    var newCutomerID = $(this).attr('id');
    var propertyIDMove = $('#movePropertyModal #idToMove').val();

    $.ajax({
        url: window.fx_url.API + 'properties/move',
        dataType: 'json',
        type: 'POST',
        contentType: 'application/x-www-form-urlencoded',
        data: {
            newCustomerID: newCutomerID,
            propertyIDMove: propertyIDMove
        },
        success: function (response) {
            if (response.success) {
                $('#loading-image').hide();
                window.location.href = "customer-management.php?cid="+newCutomerID;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            alert('Unable to move property, please contact support');
            $('#loading-image').hide();
        }
    });

};


var movePropertyToNew = function() {
    if ($('#newProject a').attr('aria-expanded') === 'true') {
        $('#newCustomerSection input[name="projectDescription"]').prop('required', true);
    }


    var addNewCustomer;

    $('#newCustomerSection input[required], #newCustomerSection select[required]').each(function(){
        if ($.trim( $(this).val() ) == '') {
            //if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').not('.validEmail').addClass('is-visible');
        } else if ($(this).attr('name') == 'email' && $('.validEmail').hasClass('is-visible')) {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.validEmail').addClass('is-visible');
        }  else if ($(this).attr('name') == 'email' && !isEmail($(this).val())) {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }

    });

    if ($(".is-invalid-label")[0]){
        addNewCustomer = 0;
    }

    if (addNewCustomer === 0) {
        return false;
    }

    if ($('#userPhoneTable tbody tr:visible').length > 0) {
        $('small.onePhone').removeClass('is-visible');

        $('#userPhoneTable tbody tr:visible').each(function(){
            if ($(this).find('td input.Phone').val() == '' || $(this).find('td select').val() == '') {
                $('small.fullPhone').addClass('is-visible');
                addNewCustomer = 0;
            } else {
                if ($(this).find('td input.Phone').val().length < 14) {
                    $('small.fullPhone').addClass('is-visible');
                    addNewCustomer = 0;
                } else {
                    $('small.fullPhone').removeClass('is-visible');
                    addNewCustomer = 1;
                }
            }
        });

        if (addNewCustomer == 1) {
            if ($('#userPhoneTable .isPrimary:checked').length < 1) {
                $('small.primary').addClass('is-visible');
                addNewCustomer = 0;
            } else {
                if ($('#userPhoneTable .isPrimary:checked').is(':visible')) {
                    $('small.primary').removeClass('is-visible');
                    addNewCustomer = 1;
                } else {
                    $('small.primary').addClass('is-visible');
                    addNewCustomer = 0;
                }
            }
        }

    } else {
        $('small.onePhone').addClass('is-visible');
        addNewCustomer = 0;
    }

    if ($(".is-visible")[0]){
        addNewCustomer = 0;
    }

    if (addNewCustomer == 1) {
        $('#loading-image').show();

        var firstName = $('#newCustomerSection input[name="firstName"]').val();
        var lastName = $('#newCustomerSection input[name="lastName"]').val();

        var ownerAddress = $('#newCustomerSection input[name="ownerAddress"]').val();
        var ownerAddress2 = $('#newCustomerSection input[name="ownerAddress2"]').val();
        var ownerCity = $('#newCustomerSection input[name="ownerCity"]').val();
        var ownerState = $('#newCustomerSection select[name="ownerState"]').val();
        var ownerZip = $('#newCustomerSection input[name="ownerZip"]').val();

        var email = $('#newCustomerSection input[name="email"]').val();

        var propertyIDMove = $('#movePropertyModal #idToMove').val();
        var projectDescription = $('#newCustomerSection input[name="projectDescription"]').val();

        var noEmailRequired = 0;

        if ($('#movePropertyModal input[name="noEmailRequired"]:checked').length > 0){
            noEmailRequired = 1;
        }

        var projectSalesperson = $('#newCustomerSection select[name="projectSalesperson"]').val();
        var projectNote = $('#newCustomerSection textarea[name="projectNote"]').val();
        var referralDropdown = $('#newCustomerSection select[name="referralDropdown"]').val();

        var businessName = $('#newCustomerSection input[name="businessName"]').val();

        var projectNotePin = 0;
        if ($('input[name="isPinnedNote"]').is(':checked')) {
            projectNotePin = 1;
        }

        function storeUserPhoneTableData() {
            var userPhoneTableData = new Array();
            $('#userPhoneTable tbody tr').each(function(row,tr){
                userPhoneTableData[row]={
                    "phoneDescription":$(tr).find('td:eq(0)').find('select').val(),
                    "phoneNumber":$(tr).find('td:eq(1)').find('input').val(),
                    "isPrimary":$(tr).find('td:eq(2)').find('input:checked').val()
                };
            });
            userPhoneTableData.shift();
            var userPhoneTableArray = userPhoneTableData;
            return JSON.stringify(userPhoneTableArray);
        }

        var userPhoneArray = storeUserPhoneTableData();

        $.ajax({
            url: window.fx_url.API + 'customers/from-property',
            dataType: 'json',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: {
                firstName: firstName,
                lastName: lastName,
                ownerAddress: ownerAddress,
                ownerAddress2: ownerAddress2,
                ownerCity: ownerCity,
                ownerState: ownerState,
                ownerZip: ownerZip,
                email: email,
                businessName: businessName,
                noEmailRequired: noEmailRequired,
                propertyIDMove: propertyIDMove,
                projectDescription: projectDescription,
                projectSalesperson: projectSalesperson,
                projectNote: projectNote,
                projectNotePin: projectNotePin,
                referralDropdown: referralDropdown,
                userPhoneTable: userPhoneArray
            },
            success: function (response) {
                if (response !== '') {

                    var newCutomerID = response.customerID;
                    var propertyIDMove = $('#movePropertyModal #idToMove').val();

                    $.ajax({
                        url: window.fx_url.API + 'properties/move',
                        dataType: 'json',
                        type: 'POST',
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            newCustomerID: newCutomerID,
                            propertyIDMove: propertyIDMove
                        },
                        success: function (response) {
                            if (response.success) {
                                $('#loading-image').hide();
                                window.location.href = "customer-management.php?cid="+newCutomerID;
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                            console.log(jqXHR.responseText);
                            alert('Unable to create customer and move property, please contact support');
                            $('#loading-image').hide();
                        }
                    });
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });

    }
};

var isPrimaryCheck = function () {
    $('#phoneTable').find('input.isPrimary').prop('checked', false);
    $(this).prop('checked', true);
};

var deletePhoneRow = function () {
    if ($(this).parent().parent().hasClass('dbRow')) {
        $(this).parent().parent().find('select').attr('delete','delete');
        $(this).parent().parent().css('display','none');

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTable tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        if ($('#phoneTable tbody').children().length == 1)
            addPhoneRow();
    } else {
        $(this).parent().parent().remove();

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTable tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        //$("div a.action:visible").eq(0).addClass("first");

        if ($('#phoneTable tbody').children().length == 1)
            addPhoneRow();
    }
};

var addPhoneRow = function () {
    $cloneBlankRow = $('#phoneTable').find('tbody tr:first').clone().css('display', '');
    $('#phoneTable').find('tbody').append($cloneBlankRow);
    $('#phoneTable').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheck);
    $('#phoneTable').find('tbody > tr:last-child .delete > a').click(deletePhoneRow);

    $('#userPhoneTable').find('tbody > tr:last-child input.Phone').mask('(*************');

    if ($('#phoneTable input.isPrimary:checked').length <= 0) {
        $('#phoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
    }
};

var taskFeatureNotEnabledModal = function() {
    $('#taskFeatureNotEnabledModal').foundation('open');
};

var editCustomer = function() {
    var loading_image = $('#loading-image');
    loading_image.show();
    //Get Customer Info
    $.ajax({
        url: window.fx_url.BASE + 'getCustomer.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            customerID: $('#customerID').text()
        },
        success: function(response) {
            $('#editCustomerModal input[name="firstName"]').val(response.firstName);
            $('#editCustomerModal input[name="lastName"]').val(response.lastName);
            $('#editCustomerModal input[name="address"]').val(response.ownerAddress);
            $('#editCustomerModal input[name="address2"]').val(response.ownerAddress2);
            $('#editCustomerModal input[name="city"]').val(response.ownerCity);

            if ($('#editCustomerModal select[name="state"]').has('option[value="' + response.ownerState + '"]').length > 0)
                $('#editCustomerModal select[name="state"] > option[value="' + response.ownerState + '"]').prop('selected', true);

            $('#editCustomerModal select[name="state"]').val(response.ownerState);
            $('#editCustomerModal input[name="zip"]').val(response.ownerZip);
            $('#editCustomerModal input[name="email"]').val(response.email);
            emailAddress = response.email;
            $('#editCustomerModal input[name="businessName"]').val(response.businessName);

            if (response.unsubscribed == '1') {
                $('#editCustomerModal input[name="unsubscribed"]').prop('checked', true);
            } else {
                $('#editCustomerModal input[name="unsubscribed"]').prop('checked', false);
            }

            if (response.noEmailRequired == '1') {
                $('#editCustomerModal input[name="noEmailRequired"]').prop('checked', true);
                $('#editCustomerModal input[name="email"]').prop('disabled', true);
                $('#editCustomerModal input[name="email"]').val('');
            } else {
                $('#editCustomerModal input[name="noEmailRequired"]').prop('checked', false);
                $('#editCustomerModal input[name="email"]').prop('disabled', false);
            }

            var customerID = response.customerID;

            //On Success Get Customer Phone
            $.ajax({
                url: window.fx_url.BASE + 'getCustomerPhone.php',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    customerID: customerID
                },
                success: function(response) {
                    //console.log(response);
                    if (response != null) {
                        $.each(response, function (i, item) {

                            // custom override since this type shares the same description name, not the correct way to
                            // handle this but not worth the time for a rework at this point
                            if (item.type === 5) {
                                item.phoneDescription = 'CellNoText';
                            }

                            $cloneRow = $('#editCustomerModal .phoneTable tbody tr:first').clone().css('display', '');

                            $cloneRow.addClass('dbRow');

                            if ($cloneRow.find('select.Description').has('option[value="' + item.phoneDescription + '"]').length > 0)
                                $cloneRow.find('select.Description > option[value="' + item.phoneDescription + '"]').prop('selected', true);

                            $cloneRow.find('input.Phone').val(item.phoneNumber);

                            if (item.isPrimary == '1') {
                                $cloneRow.find('input.isPrimary').prop('checked', true);
                            } else {
                                $cloneRow.find('input.isPrimary').prop('checked', false);
                            }


                            $cloneRow.find('select.Description').attr('sort', item.customerPhoneID);
                            $cloneRow.find('input.Phone').attr('sort', item.customerPhoneID);
                            $cloneRow.find('input.isPrimary').attr('sort', item.customerPhoneID);


                            $('#editCustomerModal .phoneTable tbody').append($cloneRow);
                            $('#editCustomerModal .phoneTable tbody > tr:last-child input.isPrimary ').click(isPrimaryCheck);
                            $('#editCustomerModal .phoneTable tbody > tr:last-child a ').click(deletePhoneRow);

                        });
                    }

                    loading_image.hide();

                    //On Success Display Modal
                    $('#editCustomerModal').foundation('open');

                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to fetch customer phones, please contact support');
                    loading_image.hide();
                }

            });

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            alert('Unable to fetch customer info, please contact support');
            loading_image.hide();
        }

    });


};

var closeEditCustomer = function() {
    var editCustomerModal = $('#editCustomerModal');

    editCustomerModal.find('#phoneTable tbody tr').not(':first').remove();

    editCustomerModal.find('input[name="firstName"]').val('');
    editCustomerModal.find('input[name="lastName"]').val('');
    editCustomerModal.find('input[name="address"]').val('');
    editCustomerModal.find('input[name="address2"]').val('');
    editCustomerModal.find('input[name="city"]').val('');
    editCustomerModal.find('select[name="state"]').val('');
    editCustomerModal.find('input[name="zip"]').val('');
    editCustomerModal.find('input[name="businessName"]').val('');

    var email = editCustomerModal.find('input[name="email"]');
    email.val('');
    email.parent().removeClass('is-invalid-label');
    email.removeClass('is-invalid-input');
    email.parent().find('.validEmail').removeClass('is-visible');

    editCustomerModal.find('small.fullPhone').removeClass('is-visible');
    editCustomerModal.find('small.onePhone').removeClass('is-visible');
    editCustomerModal.find('small.primary').removeClass('is-visible');

    editCustomerModal.find('#saveEditCustomerModal').prop('disabled', false);
    editCustomerModal.find('.validating-email-message').hide();
    editCustomerModal.find('.abort-validation').hide();
    if (validationRequest !== null) {
        validationRequest.abort();
        validationRequest = null;
    }

    editCustomerModal.foundation('close');
};

var saveEditCustomer = function() {

    $('#editCustomerModal input.customerRequired, #editCustomerModal select.customerRequired').each(function () {
        if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').not('.validEmail').addClass('is-visible');
		} else if ($(this).attr('name') == 'email' && $('.validEmail').hasClass('is-visible')) {
			$(this).parent().addClass('is-invalid-label');
			$(this).addClass('is-invalid-input');
			$(this).parent().find('.validEmail').addClass('is-visible');
        } else if ($(this).attr('name') == 'email' && !isEmail($(this).val())) {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }
    });


    if ($('#phoneTable tbody tr:visible').length > 0) {
        $('small.onePhone').removeClass('is-visible');

        $('#phoneTable tbody tr:visible').each(function(){
            if ($(this).find('td input.Phone').val() == '' || $(this).find('td select.Description').val() == '') {
                $('small.fullPhone').addClass('is-visible');
            } else {
                if ($(this).find('td input.Phone').val().length < 14) {
                    $('small.fullPhone').addClass('is-visible');
                } else {
                    $('small.fullPhone').removeClass('is-visible');
                }
            }
        });
    } else {
        $('small.onePhone').addClass('is-visible');
    }

    if ($('#phoneTable .isPrimary:checked').length < 1) {
        $('small.primary').addClass('is-visible');

    } else {
        if ($('#phoneTable .isPrimary:checked').is(':visible')) {
            $('small.primary').removeClass('is-visible');
        } else {
            $('small.primary').addClass('is-visible');
        }
    }

    if (!$('#editCustomerModal input[name="firstName"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="lastName"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="address"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="address2"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="city"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal select[name="state"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="zip"]').parent().hasClass('is-invalid-label') &&
        !$('#editCustomerModal input[name="email"]').parent().hasClass('is-invalid-label') &&
        !$('small.onePhone').hasClass('is-visible') &&
        !$('small.fullPhone').hasClass('is-visible') &&
        !$('small.primary').hasClass('is-visible')
    ) {

        var firstName = $('#editCustomerModal input[name="firstName"]').val();
        var lastName = $('#editCustomerModal input[name="lastName"]').val();
        var address = $('#editCustomerModal input[name="address"]').val();
        var address2 = $('#editCustomerModal input[name="address2"]').val();
        var city = $('#editCustomerModal input[name="city"]').val();
        var state = $('#editCustomerModal select[name="state"]').val();
        var zip = $('#editCustomerModal input[name="zip"]').val();
        var email = $('#editCustomerModal input[name="email"]').val();
        var businessName = $('#editCustomerModal input[name="businessName"]').val();
        var noEmailRequired = 0;
        var unsubscribed = 0;

        if ($('#editCustomerModal input[name="noEmailRequired"]:checked').length > 0){
            noEmailRequired = 1;
        }

        if ($('#editCustomerModal input[name="unsubscribed"]:checked').length > 0){
            unsubscribed = 1;
        }

        storePhoneTableData();

        function storePhoneTableData() {
            var phoneTableData = new Array();
            $('#phoneTable tbody tr').each(function(row,tr){
                phoneTableData[row]={
                    "phoneDelete":$(tr).find('td:eq(0)').find('select').attr('delete'),
                    "customerPhoneID":$(tr).find('td:eq(0)').find('select').attr('sort'),
                    "phoneDescription":$(tr).find('td:eq(0)').find('select').val(),
                    "phoneNumber":$(tr).find('td:eq(1)').find('input').val(),
                    "isPrimary":$(tr).find('td:eq(2)').find('input:checked').val()
                };
            });
            phoneTableData.shift();
            var phoneTableArray = phoneTableData;

            $('loading-image').show();
            $.ajax({
                url: window.fx_url.API + 'customers',
                dataType: "json",
                type: "PUT",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    customerID: $('#customerID').text(),
                    firstName: firstName,
                    lastName: lastName,
                    address: address,
                    address2: address2,
                    city: city,
                    state: state,
                    zip: zip,
                    email: email,
                    businessName: businessName,
                    phoneArray: phoneTableArray,
                    noEmailRequired: noEmailRequired,
                    unsubscribed: unsubscribed,
                },
                success: function(response) {
                    // console.log(response);
                    if (response.success) {
                        $('#phoneTable tbody tr').not(':first').remove();

                        $('#editCustomerModal input[name="firstName"]').val('');
                        $('#editCustomerModal input[name="lastName"]').val('');
                        $('#editCustomerModal input[name="address"]').val('');
                        $('#editCustomerModal input[name="address2"]').val('');
                        $('#editCustomerModal input[name="city"]').val('');
                        $('#editCustomerModal select[name="state"]').val('');
                        $('#editCustomerModal input[name="zip"]').val('');
                        $('#editCustomerModal input[name="email"]').val('');
                        $('#editCustomerModal input[name="businessName"]').val('');

                        $('#editCustomerModal').foundation('close');
                        $('#editCustomerSuccessModal').foundation('open');

                        $('#address').text(address);
                        $('#city').text(city);
                        $('#state').text(state);
                        $('#zip').text(zip);

                        updateCustomerInfo();
                        $('loading-image').hide();

                    } else {
                        $('#editCustomerErrorModal p.error-text').text(response.error);
                        $('#editCustomerErrorModal').foundation('open');
                    }
                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    console.log('Unable to update customer, please contact support');
                }
            });
        }
    }
};

var updateCustomerInfo = function(){
    //Get Customer Info
    $.ajax({
        url: window.fx_url.BASE + 'getCustomer.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            customerID: $('#customerID').text()
        },
        success: function(response) {
            var customerAddressDisplay = '<strong>Billing Address</strong><br>' + response.ownerAddress + (response.ownerAddress2 !== null ? ' ' + response.ownerAddress2 : '') + '</br>' +  response.ownerCity + ', ' + response.ownerState + ' ' +  response.ownerZip;
            $('#customerAddressDisplay').html(customerAddressDisplay);
            var nameDisplay = response.firstName + ' ' + response.lastName;
            var customerName = response.firstName + ' ' + response.lastName;
            updateCustomerContactInfo(response.customerID, response.email, response.unsubscribed, response.noEmailRequired, response.businessName, nameDisplay, customerName);

            if (response.businessName != null){
                nameDisplay = response.businessName;
            }

            $('.project-title').text(nameDisplay);

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }

    });
};

var updateCustomerContactInfo = function(customerID, email, unsubscribed, noEmailRequired, businessName, nameDisplay, customerName){
    var phoneDisplay = '';
    var primary = '';
    $.ajax({
        url: window.fx_url.BASE + 'getCustomerPhone.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            customerID: customerID
        },
        success: function(response) {
            if (response != null) {
                $.each(response, function (i, item) {
                    if (item.isPrimary == 1){
                        primary = '<span>primary</span>';
                    } else {
                        primary = '';
                    }

                    phoneDisplay += item.phoneDescription + ': <a href="tel:' + item.phoneNumber + '">' + item.phoneNumber + '</a> ' + primary + '<br/>';
                });
                var unsubscribedText = '';
                var customerNameSectionDisplay = '';

                if (unsubscribed == 1) {
                    unsubscribedText = ' <span>unsubscribed</span>';
                }
                else {
                    unsubscribedText = '';
                }

                if (noEmailRequired == 1) {
                    unsubscribedText = ' <span>no email</span>';
                }

                if (businessName != null){
                    customerNameSectionDisplay = 'Customer Name: ' + customerName +'<br>';
                }
                var emailDisplay = 'Email: ' + (email !== null ? '<a href="mailto:' + email + '">' + email + '</a>' : '') + unsubscribedText;
                $('#contactInfoCustomer').html('<strong>Contact Information</strong><br />' + customerNameSectionDisplay + phoneDisplay + emailDisplay );
            }

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }

    });
};

var validatingCustomerEmailMessage = $('.validating-email-message.customer'),
    validatingMovePropertyEmailMessage = $('.validating-email-message.move-property'),
    abortValidation = $('.abort-validation'),
    validationRequest = null,
    abortTimer = null;
abortValidation.on('click.fx', function() {
    if (validationRequest === null) {
        return;
    }
    validationRequest.abort();
    validationRequest = null;
});
function validateEmailAddress(email) {
    // if request is already underway, then we cancel it
    if (validationRequest !== null) {
        validationRequest.abort();
    }

    switch(email.data('type')) {
        case 'edit-customer':
            var submitButton = $('#saveEditCustomerModal');
            var validatingEmailMessage = validatingCustomerEmailMessage;
            break;
        case 'move-property':
            var submitButton = $('#movePropertyToNew');
            var validatingEmailMessage = validatingMovePropertyEmailMessage;
            break;
    }

    submitButton.prop('disabled', true);
    validatingEmailMessage.show();
    abortValidation.hide();

    if (abortTimer !== null) {
        clearTimeout(abortTimer);
    }
    abortTimer = setTimeout(function () {
        abortValidation.show();
        abortTimer = null;
    }, 10000);

    validationRequest = $.ajax({
        url:  window.fx_url.API + 'service/email-validation',
        dataType: 'json',
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            email: email.val()
        }
    })
        .done(function (response) {
            switch(response.status) {
                // valid
                case 1:
                    email.parent().removeClass('is-invalid-label');
                    email.removeClass('is-invalid-input');
                    email.parent().find('.validEmail').removeClass('is-visible');
                    break;
                // invalid
                case 2:
                // unknown
                case 3:
                    email.parent().addClass('is-invalid-label');
                    email.addClass('is-invalid-input');
                    email.parent().find('.validEmail').addClass('is-visible');
                    break;
            }
        })
        .fail(function (jqXHR, textStatus, errorThrown) {
            email.parent().removeClass('is-invalid-label');
            email.removeClass('is-invalid-input');
            email.parent().find('.validEmail').removeClass('is-visible');
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        })
        .always(function () {
            submitButton.prop('disabled', false);
            validatingEmailMessage.hide();
            abortValidation.hide();
            if (abortTimer !== null) {
                clearTimeout(abortTimer);
                abortTimer = null;
            }
            validationRequest = null;
        });
}

$(function() {
    $('.phone').mask('(*************');
});