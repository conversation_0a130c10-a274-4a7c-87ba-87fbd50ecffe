(function ($) {
    "use strict";
    var media_library = {
        statuses: {
            OPEN: 1,
            CLOSED: 2
        },
        settings: {
            desc_slide_speed: 'fast',
            open_class: 't-open'
        },
        table: null,
        documents: [],
        active_document: null,
        init: function () {
            window.Layout.setModeWindow();
            window.Layout.setTitle('Media Library');
            this.table = $('.p-media-library .e-ml-table');
            var that = this;
            this.table.find('.e-mlt-row').not('.t-header, .t-no-desc, .t-desc').each(function(i) {
                that.documents.push({
                    item: $(this),
                    status: that.statuses.CLOSED
                });
            });
            for (var i in this.documents) {
                var document = this.documents[i];
                var desc = document.item.next('.e-mlt-row.t-desc');
                if (desc.length !== 1) {
                    delete this.documents[i];
                    continue;
                }
                document.desc = desc;
                document.desc_wrapper = desc.find('.e-mltr-data.t-desc .e-mltrd-wrapper');
                (function(document, i) {
                    document.item.click(function(e) {
                        if (e.target.nodeName === 'A') {
                            return true;
                        }
                        e.preventDefault();
                        switch (document.status) {
                            case that.statuses.CLOSED:
                                if (that.active_document !== null) {
                                    that.close(that.active_document);
                                }
                                that.open(i);
                                break;
                            case that.statuses.OPEN:
                                that.close(i);
                                break;
                        }
                        return false;
                    });
                })(document, i);
            }
        },
        open: function (idx) {
            var document = this.documents[idx];
            if (document === undefined) {
                return;
            }
            document.item.addClass(this.settings.open_class);
            document.desc.show();
            document.desc_wrapper.slideDown(this.settings.desc_slide_speed);
            document.status = this.statuses.OPEN;
            this.active_document = idx;
        },
        close: function (idx) {
            var document = this.documents[idx];
            if (document === undefined) {
                return;
            }
            document.status = this.statuses.CLOSED;
            this.active_document = null;
            var that = this;
            document.desc_wrapper.slideUp(this.settings.desc_slide_speed, function() {
                document.desc.hide();
                document.item.removeClass(that.settings.open_class);
            });
        }
    };
    $().ready(function() {
        media_library.init();
    });
})(jQuery);