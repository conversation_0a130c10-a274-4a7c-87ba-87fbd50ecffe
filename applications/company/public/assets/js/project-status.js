$(document).ready(function () {
    var projectStatus = $('#projectStatus');
    var projectStatusTab =  projectStatus.find('.projectStatusTab');
    var contentContainer = $('.contentContainer');
    var dashboard = $('#dashboard');
    var calendarGroup = $('#calendarGroup');
    var filterContainer = $('#filterGroup');
    var menu_bar = $('.menu-bar');

    if (window.layout.user.is_trial) {
        projectStatusTab.addClass('t-trial');
    }

    function showProjectStatus() {
        projectStatus.removeClass('hidden');
        projectStatusTab.removeClass('closed');
        getProjectStatus();
    }

    function hideProjectStatus() {
        projectStatus.addClass('hidden');
        projectStatusTab.addClass('closed');
    }

    resizePanels();

    //Create Project Status Cookie if it Doesn't Exist
    if (!document.getCookie('project_status')) {
        if ($(document).width() > 767)
            document.setCookie('project_status', JSON.stringify({ "view": true }));
        else document.setCookie('project_status' + JSON.stringify({ "view": false }));
    }

    menu_bar.find('.refresh').click(getProjectStatus);
    menu_bar.find('.sort select').change(function(){
        var $this = $(this);
        $this.blur();

        var local_storage = JSON.parse(localStorage.getItem('project_status'));

        local_storage['sort'] = $this.val();
        localStorage.setItem('project_status', JSON.stringify(local_storage));

        getProjectStatus();
    });
    menu_bar.find('.group input').change(function() {
        var $this = $(this);

        var local_storage = JSON.parse(localStorage.getItem('project_status'));

        local_storage['groupBy'] = $this.is(':checked');
        localStorage.setItem('project_status', JSON.stringify(local_storage));

        getProjectStatus();
    });

    //Read the Cookie Array
    var projectStatusArray;
    try{
        projectStatusArray = JSON.parse(document.getCookie('project_status'));
    }
    catch(error){
        projectStatusArray = { view: $(document).width() > 767 };
        document.setCookie('project_status', JSON.stringify(projectStatusArray));
    }

    //Show/Hide Project Status Section
    if (projectStatusArray['view'] == true) {
        showProjectStatus();
    } else {
        hideProjectStatus();
    }


    //Read the Project Status Cookie for Project Status Sections
    var count;
    var step = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    for (count = 0; count <= step.length; count++) {
        var count_step = count;
        var status_title = $('.projectStatusTitle[status="'+count_step+'"]');

        if (projectStatusArray['step'+count_step] == true) {
            //console.log('true');
            $('.projectStatusStep[statuslist="'+count_step+'"]').show();
            status_title.find('span').eq('0').removeClass('closed').addClass('open');

            status_title.addClass('open');
        }

    }

    //Show Project Status Box
   projectStatusTab.click(function(){
        if (!$(this).hasClass('closed')) {
            hideProjectStatus();

            if ($('#resourceWeekView').hasClass('active')){
                filterCalendar('resourceWeek');
            }

            var projectStatusArray = JSON.parse(document.getCookie('project_status'));
            projectStatusArray['view'] = false;

            document.setCookie('project_status', JSON.stringify(projectStatusArray));

        } else {
           showProjectStatus();

            var projectStatusArray = JSON.parse(document.getCookie('project_status'));
            projectStatusArray['view'] = true;
            document.setCookie('project_status', JSON.stringify(projectStatusArray));
        }
    });


    //Show Project Status Subbox
    $('.projectStatusTitle').click(function(){
        var status_title = $(this);
        var status = status_title.attr('status');
        var status_step = $('.projectStatusStep[statuslist="'+status+'"]');

        if (status_step.is(':visible')) {
            status_step.hide();
            status_title.find('span').eq('0').removeClass('open').addClass('closed');
            status_title.removeClass('open');

            status_step.find('.sales-bucket').removeClass('open');
            status_step.find('.salesperson .arrow').removeClass('open');

            var projectStatusArray = JSON.parse(document.getCookie('project_status'));
            projectStatusArray['step'+status+''] = false;
            document.setCookie('project_status', JSON.stringify(projectStatusArray));

        } else {
            status_step.show();
            status_title.find('span').eq('0').removeClass('closed').addClass('open');
            status_title.addClass('open');

            var projectStatusArray = JSON.parse(document.getCookie('project_status'));
            projectStatusArray['step'+status+''] = true;
            document.setCookie('project_status', JSON.stringify(projectStatusArray));
        }
    });

//End Document Ready
});

var getProjectStatus = function() {
    $('#loading-image.projectStatus').show();

    $('.projectStatusTitle').each(function(){
        $(this).find('span').eq(2).text('(0)');
    });

    $('.projectStatusStep[statuslist="0"],.projectStatusStep[statuslist="1"],.projectStatusStep[statuslist="2"],.projectStatusStep[statuslist="3"],.projectStatusStep[statuslist="4"],.projectStatusStep[statuslist="5"],.projectStatusStep[statuslist="6"],.projectStatusStep[statuslist="7"],.projectStatusStep[statuslist="8"],.projectStatusStep[statuslist="9"],.projectStatusStep[statuslist="10"],.projectStatusStep[statuslist="11"],.projectStatusStep[statuslist="12"]').empty();

    var local_storage = {};
    if (!localStorage.getItem('project_status')) {
        local_storage = {
            'allowGroupBy': true,
            'groupBy': true,
            'sort': 'asc'
        };
        localStorage.setItem('project_status', JSON.stringify(local_storage));
    } else {
        local_storage = JSON.parse(localStorage.getItem('project_status'));
    }

    $.ajax({
        url: window.fx_url.BASE + 'getProjectStatus.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            sort: local_storage['sort'],
            groupBy: local_storage['groupBy']
        },
        success: function (response) {
            var project_status_items = [];
            var project_status_users = [];

            var menu_bar = $('.menu-bar');
            var groupByMenuItem = menu_bar.find('.group');
            var sortMenuItem = menu_bar.find('.sort');

            $.each(response['results'], function (i, item) {
                if ($.inArray(item.salesID, project_status_users) < 0) {
                    project_status_users.push(item.salesID);
                }
                var name_display = item.firstName + ' ' + item.lastName;
                if (item.businessName != null) {
                    name_display = item.businessName;
                }
                if (item.referenceID != undefined && item.referenceID != null) {
                    name_display = name_display + ' <small class="reference-id">#' + item.referenceID + '</small>';
                }

                var inactive = false;
                if (item.userActive !== undefined && item.userActive === '0') {
                    inactive = true;
                }
                project_status_items.push({
                    'status_type': item.statusType,
                    'user_id': item.salesID,
                    'user_inactive': inactive,
                    'user_name': item.salesperson,
                    'name': name_display,
                    'address': item.addressDisplay,
                    'time': item.time,
                    'link': item.link
                });
            });

            local_storage['allowGroupBy'] = project_status_users.length !== 1;
            localStorage.setItem('project_status', JSON.stringify(local_storage));

            if (!local_storage['allowGroupBy']) {
                groupByMenuItem.hide();
            } else if (local_storage['groupBy']) {
                groupByMenuItem.find('input').prop('checked', true);
            }

            sortMenuItem.find('select').val(local_storage['sort']);

            $.each(project_status_items, function (i, item) {

                var status_list = $('.projectStatusStep[statusList="'+item.status_type+'"]');
                var status_item = $('<a href="'+item.link+'"><div class="projectStatusList"><strong>'+item.name+'</strong><br/>'+item.address+'<br/><span>'+item.time+'</span></div></a>');

                if (local_storage['allowGroupBy'] && local_storage['groupBy']) {
                    var sales_bucket = status_list.find('.sales-bucket[data-id="'+item.user_id+'"]');

                    if (sales_bucket.length === 1) {
                        sales_bucket.append(status_item);
                    } else {
                        var inactive = '';
                        if (item.user_inactive) {
                            inactive = ' <i>(Inactive)</i>';
                        }
                        var salesperson = $('<div class="sales-bucket" data-id="'+item.user_id+'"><div class="salesperson"><img class="avatar" src="'+icons.AVATAR+'">'+item.user_name+inactive+'<span class="arrow"></span></div></div>');

                        salesperson.find('.salesperson').click(function(){
                            var salesperson = $(this);
                            var arrow = salesperson.find('.arrow');
                            var sales_bucket = salesperson.parent();
                            if (sales_bucket.hasClass('open')) {
                                sales_bucket.removeClass('open');
                                arrow.removeClass('open');
                            } else {
                                sales_bucket.addClass('open');
                                arrow.addClass('open');
                            }
                        });

                        salesperson.append(status_item);
                        status_list.append(salesperson);
                    }
                } else {
                    status_list.append(status_item);
                }
            });

            $('.projectStatusTitle').each(function(){
                var status = $(this).attr('status');
                var status_container = $('.projectStatusStep[statuslist="'+status+'"]');
                if (status_container.children().length === 0) {
                    status_container.append('<div class="no-items">No Items to Display</div>');
                }
            });

            if (response.LeadCreatedCount || response.LeadCreatedCount == 0) {
                $('.projectStatusTitle[status="0"]').show();
                if (response.LeadCreatedCount > 0) {
                    $('.projectStatusTitle[status="0"]').show();
                    $('.projectStatusTitle[status="0"]').find('span').eq(2).text('('+response.LeadCreatedCount+')');
                } else {
                    $('.projectStatusTitle[status="0"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="0"]').hide();
                $('.projectStatusStep[statuslist="0"]').hide();
            }

            if (response.ProjectCreatedCount || response.ProjectCreatedCount == 0) {
                $('.projectStatusTitle[status="1"]').show();
                if (response.ProjectCreatedCount > 0) {
                    $('.projectStatusTitle[status="1"]').show();
                    $('.projectStatusTitle[status="1"]').find('span').eq(2).text('('+response.ProjectCreatedCount+')');
                } else {
                    $('.projectStatusTitle[status="1"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="1"]').hide();
                $('.projectStatusStep[statuslist="1"]').hide();
            }


            if (response.AppointmentScheduledCount || response.AppointmentScheduledCount == 0) {
                $('.projectStatusTitle[status="2"]').show();
                if (response.AppointmentScheduledCount > 0) {
                    $('.projectStatusTitle[status="2"]').find('span').eq(2).text('('+response.AppointmentScheduledCount+')');
                } else {
                    $('.projectStatusTitle[status="2"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="2"]').hide();
                $('.projectStatusStep[statuslist="2"]').hide();
            }


            if (response.AppointmentCompletedCount || response.AppointmentCompletedCount == 0) {
                $('.projectStatusTitle[status="3"]').show();
                if (response.AppointmentCompletedCount > 0) {
                    $('.projectStatusTitle[status="3"]').show();
                    $('.projectStatusTitle[status="3"]').find('span').eq(2).text('('+response.AppointmentCompletedCount+')');
                } else {
                    $('.projectStatusTitle[status="3"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="3"]').hide();
                $('.projectStatusStep[statuslist="3"]').hide();
            }


            if (response.RepairPlanCreatedCount || response.RepairPlanCreatedCount == 0) {
                $('.projectStatusTitle[status="4"]').show();
                if (response.RepairPlanCreatedCount > 0) {
                    $('.projectStatusTitle[status="4"]').show();
                    $('.projectStatusTitle[status="4"]').find('span').eq(2).text('('+response.RepairPlanCreatedCount+')');
                } else {
                    $('.projectStatusTitle[status="4"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="4"]').hide();
                $('.projectStatusStep[statuslist="4"]').hide();
            }


            if (response.BidCreatedCount || response.BidCreatedCount == 0) {
                $('.projectStatusTitle[status="5"]').show();
                if (response.BidCreatedCount > 0) {
                    $('.projectStatusTitle[status="5"]').show();
                    $('.projectStatusTitle[status="5"]').find('span').eq(2).text('('+response.BidCreatedCount+')');
                } else {
                    $('.projectStatusTitle[status="5"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="5"]').hide();
                $('.projectStatusStep[statuslist="5"]').hide();
            }


            if (response.BidSentCount || response.BidSentCount == 0) {
                $('.projectStatusTitle[status="6"]').show();
                if (response.BidSentCount > 0) {
                    $('.projectStatusTitle[status="6"]').show();
                    $('.projectStatusTitle[status="6"]').find('span').eq(2).text('('+response.BidSentCount+')');
                } else {
                    $('.projectStatusTitle[status="6"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="6"]').hide();
                $('.projectStatusStep[statuslist="6"]').hide();
            }


            if (response.BidAcceptedCount || response.BidAcceptedCount == 0) {
                $('.projectStatusTitle[status="7"]').show();
                if (response.BidAcceptedCount > 0) {
                    $('.projectStatusTitle[status="7"]').show();
                    $('.projectStatusTitle[status="7"]').find('span').eq(2).text('('+response.BidAcceptedCount+')');
                } else {
                    $('.projectStatusTitle[status="7"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="7"]').hide();
                $('.projectStatusStep[statuslist="7"]').hide();
            }


            if (response.BidRejectedCount || response.BidRejectedCount == 0) {
                $('.projectStatusTitle[status="8"]').show();
                if (response.BidRejectedCount > 0) {
                    $('.projectStatusTitle[status="8"]').show();
                    $('.projectStatusTitle[status="8"]').find('span').eq(2).text('('+response.BidRejectedCount+')');
                } else {
                    $('.projectStatusTitle[status="8"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="8"]').hide();
                $('.projectStatusStep[statuslist="8"]').hide();
            }


            if (response.InstallationScheduledCount || response.InstallationScheduledCount == 0) {
                $('.projectStatusTitle[status="9"]').show();
                if (response.InstallationScheduledCount > 0) {
                    $('.projectStatusTitle[status="9"]').show();
                    $('.projectStatusTitle[status="9"]').find('span').eq(2).text('('+response.InstallationScheduledCount+')');
                } else {
                    $('.projectStatusTitle[status="9"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="9"]').hide();
                $('.projectStatusStep[statuslist="9"]').hide();
            }


            if (response.InstallationCompleteCount || response.InstallationCompleteCount == 0) {
                $('.projectStatusTitle[status="10"]').show();
                if (response.InstallationCompleteCount > 0) {
                    $('.projectStatusTitle[status="10"]').show();
                    $('.projectStatusTitle[status="10"]').find('span').eq(2).text('('+response.InstallationCompleteCount+')');
                } else {
                    $('.projectStatusTitle[status="10"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="10"]').hide();
                $('.projectStatusStep[statuslist="10"]').hide();
            }


            if (response.FinalReportSentCount || response.FinalReportSentCount == 0) {
                $('.projectStatusTitle[status="11"]').show();
                if (response.FinalReportSentCount > 0) {
                    $('.projectStatusTitle[status="11"]').show();
                    $('.projectStatusTitle[status="11"]').find('span').eq(2).text('('+response.FinalReportSentCount+')');
                } else {
                    $('.projectStatusTitle[status="11"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="11"]').hide();
                $('.projectStatusStep[statuslist="11"]').hide();
            }


            if (response.CompletedProjectCount || response.CompletedProjectCount == 0) {
                $('.projectStatusTitle[status="12"]').show();
                if (response.CompletedProjectCount > 0) {
                    $('.projectStatusTitle[status="12"]').show();
                    $('.projectStatusTitle[status="12"]').find('span').eq(2).text('('+response.CompletedProjectCount+')');
                } else {
                    $('.projectStatusTitle[status="12"]').find('span').eq(2).text('(0)');
                }
            } else {
                $('.projectStatusTitle[status="12"]').hide();
                $('.projectStatusStep[statuslist="12"]').hide();
            }

            $('#loading-image.projectStatus').hide();

        }, error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }

    });
};
