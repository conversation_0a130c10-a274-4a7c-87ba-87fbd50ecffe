window.Layout.setModeWindow();
window.Layout.setTitle('Marketing');

function getMarketingMetrics(dateFrom, dateTo){
    $('#loading-image').show();
    $.ajax({
        url: window.fx_url.BASE + 'getMarketingMetrics.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            dateFrom: dateFrom,
            dateTo: dateTo
        },
        success: function (response) {
            if(response != '' && response !="false"){
                populateMarketingData(response);
                $('#date-range-noData').hide();
            }
            else{
                $('#marketingMetricsDiv').html('');
                $('#date-range-noData').show();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
    $('#loading-image').hide();
}

function getSubSourceByID(subSourceID){

    $('#loading-image').show();
    var idToEdit = subSourceID;
    $.ajax({
        url: window.fx_url.BASE + 'getMarketingSubSourceByID.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            subSourceID: subSourceID,
        },
        success: function (response) {

            $('#subSourceModalTitle').html("Edit Subsource");
            $('#subSourceName').val(response[0]['marketingTypeName']);
            $('#subsourceID').val(response[0]['marketingTypeID']);

            var parentTypeSelection = document.getElementById("parentTypeSelection");
            parentTypeSelection.remove(0);  //by default these have 'select' in them..remove this
            for(i=0; i<response[1].length; i++){
                var option = document.createElement("option");

                parentTypeSelection.add(option);
                option.text = response[1][i]['marketingTypeName'];
                option.value = response[1][i]['marketingTypeID'];
            }

            var optionToSelect = response[0]['parentMarketingTypeID'];
            $('#parentTypeSelection').val(optionToSelect);
            $('#parentTypeID').val(optionToSelect);
            $('#setupSubSourceModal').foundation('open');
            $('#loading-image').hide();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            $('#loading-image').hide();
        }
    });
};


function setupAdSpend(parentID, subSourceID){
    $.ajax({
        url: window.fx_url.BASE + 'getAllMarketingSubSourcesByParentID.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data:{
            parentID: parentID,
            returnParentNames: 'true'  //set this to true if you want the parent names & ID's in position[1] of the result set
        },
        success: function (response) {
            //populate the parent type combo & select
            //the correct option

            // console.log(response);

            var parentTypeSelection = document.getElementById("adParentTypeSelection");
            parentTypeSelection.remove(0);  //by default these have 'select' in them..remove this
            for(i=0; i<response[1].length; i++){
                var option = document.createElement("option");

                parentTypeSelection.add(option);
                option.text = response[1][i]['marketingTypeName'];
                option.value = response[1][i]['marketingTypeID'];
            }

            var optionToSelect = parentID;
            $('#adParentTypeSelection').val(optionToSelect);
            $('#adParentTypeID').val(optionToSelect);

            //populate the sub-source Combo;
            var subSourceSelect = document.getElementById('adSubSourceSelection');

            subSourceSelect.remove(0);

            for(i=0; i<response[0].length; i++){
                var subSourceOption = document.createElement('option');
                subSourceOption.text = response[0][i]['marketingTypeName'];
                subSourceOption.value = response[0][i]['marketingTypeID'];
                subSourceSelect.add(subSourceOption);
            }

            $('#adSubSourceSelection').val(subSourceID);
            $('#adSubsourceID').val(subSourceID);


            $('#addSpendModal').foundation('open');
            $('#loading-image').hide();


        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            $('#loading-image').hide();
        }
    });


};

function populateMarketingData(allData){
    $('#loading-image').show();
    var metricsHTML = "";
    for(var i in allData){
        var deleteButton = '|<a class="deleteSource"> Delete </a>';
        if (allData[i]['isRepeatBusiness']){
            deleteButton = '';
        }
        //this is a new 'Source' so add a source row
        //Include the total for this row.
        var thisSourceTotal = new Intl.NumberFormat('us-US', { style: 'currency', currency: 'USD' }).format(allData[i].spendAmount);

        //source row
        metricsHTML +='<div class="row source clickable" id="' + allData[i]['marketingTypeID'] + '" data-itemToShow="subsource"> '+
            '<div class="medium-6 columns">' +
            '<img class="arrowRight detailsToggle" src="' + window.fx_url.assets.IMAGE + 'icons/arrow-right.png" /><img class="arrowDown detailsToggle" src="' + window.fx_url.assets.IMAGE + 'icons/arrow-down.png" /></button> <Strong>' + allData[i]['marketingTypeName'] +'</strong>' +
            '</div> ' +
            '<div class="medium-2 columns spend">' +
            thisSourceTotal +
            '</div>'+
            '<div class="medium-4 columns text-right" style="padding-right: 0.6rem;">'+
            '<span style="margin-left: .2rem">&nbsp;</span><a parentID="' + allData[i]['marketingTypeID'] + '" class="addSubSource" target="_blank">Add Source</a> | <a class="editSource" source="' + allData[i]['marketingTypeID'] +'"" >Edit</a> ' + deleteButton +
            ' </div> </div>';

        for(var j in allData[i]['subsources']){
            var subsource = allData[i]['subsources'][j];
            var deleteButton = '|<a class="deleteSubSource"> Delete </a>';
            if (subsource['isRepeatBusiness']){
                deleteButton = '';
            }

            var thisSourceTotal = new Intl.NumberFormat('us-US', { style: 'currency', currency: 'USD' }).format(subsource['spendAmount']);
            metricsHTML +='<div class="row subsource clickable" id="' + subsource['marketingTypeID'] + '" data-belongsTo="' + subsource['parentMarketingTypeID'] + '" data-itemToShow="spendrow">' +
                '<div class="medium-6 columns">' +
                '<p class="subsourceHeading ">'+
                ' <img class="arrowRight detailsToggle" src="' + window.fx_url.assets.IMAGE + 'icons/arrow-right.png" />'+
                '<img class="arrowDown detailsToggle" src="' + window.fx_url.assets.IMAGE + 'icons/arrow-down.png" />'+
                '<Strong>' + subsource['marketingTypeName'] + '</strong>' +
                '</p>'+
                '</div>'+
                '<div class="medium-2 columns spend">'+
                thisSourceTotal +
                '</div>' +
                ' <div class="medium-4 columns text-right">' +
                '<a style="margin-left: 2.2rem;" onclick="setupAdSpend('+ subsource['parentMarketingTypeID'] + ',' + subsource['marketingTypeID'] + ')">Add Spend</a> |<a onclick="getSubSourceByID('+ subsource['marketingTypeID'] + ')"> Edit</a> ' + deleteButton +
                '</div></div>';

            for(var k in subsource){
                if (typeof(subsource[k]) == 'object' && subsource[k] != null){
                    //Add Spend Rows
                    var spendAmount = new Intl.NumberFormat('us-US', { style: 'currency', currency: 'USD' }).format(subsource[k]['spendAmount']);
                    var allDates = defaultDates(subsource[k]['startDate'],subsource[k]['endDate']);
                    var spendStart = allDates['startDate'];
                    var spendEnd = allDates['endDate'];

                    if (subsource[k]['marketingSpendID'] != null){
                        metricsHTML += '<div class="row spendrow clickable" id="' + subsource[k]['marketingSpendID'] +'" data-belongsTo="' + subsource['marketingTypeID'] + '">' +
                            '<div class="medium-6 columns">' +
                            '<p class="spendrowHeading"> ' +
                            '<Strong>' + spendStart + " to " + spendEnd + '</strong>' +
                            ' </p>' +
                            '</div>' +
                            '<div class="medium-2 columns spend">' +
                            spendAmount +
                            '</div>' +
                            '<div class="medium-4 columns text-right">' +
                            '<a style="margin-left: 7.95rem" onclick="editSpendRow('+ subsource[k]['marketingSpendID'] + ',' +
                            + subsource['marketingTypeID'] + ',' + subsource['parentMarketingTypeID'] + ')">Edit</a> |<a class="deleteSpend"> Delete </a>'+
                            ' </div>' +
                            '</div>';
                    }
                }
            }
        }
    }
    $('#loading-image').hide();
    $('#marketingMetricsDiv').html('');
    $('#marketingMetricsDiv').append(metricsHTML);
    //open all sections
    $('.arrowRight').click();
};

//returns a startDate & endDate
//if no dates are provided it will return the first day & last day of the current month
//if dates are provided, it will return those dates as 'M/dd/YYYY'
//based on dates returned from server as string.
function defaultDates(startDate, endDate){
    var  monthArray = ['1','2','3','4','5','6','7','8','9','10','11','12'];
    if (startDate === undefined  || endDate === null) {
        var today = new Date();
        var month = monthArray[today.getMonth()]; //date returns months 0 indexed...
        var year = today.getFullYear();
        var defaultStartDate = month + '/01/' + year;

        var defaultEndDate = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59); //last day of the current month
        var lastDay = defaultEndDate.getDate();
        defaultEndDate = month + "/" + lastDay + "/" + year;

        return {
            'startDate': defaultStartDate,
            'endDate': defaultEndDate
        };
    }

    startTimeStamp = new Date(startDate.replace(/-/g, '/'));
    endTimeStamp = new Date(endDate.replace(/-/g, '/'));

    var start_today = startTimeStamp;
    var start_month = monthArray[start_today.getMonth()];
    var start_year = start_today.getFullYear();
    var start_date = start_today.getDate();
    defaultStartDate = start_month + "/" + start_date + "/" + start_year;

    var end_today = endTimeStamp;
    var end_month = monthArray[end_today.getMonth()];
    var end_year = end_today.getFullYear();
    var end_date = end_today.getDate();
    defaultEndDate = end_month + "/" + end_date + "/" + end_year;

    return {
        'startDate': defaultStartDate,
        'endDate': defaultEndDate
    };

}

function marketingMetricsEditSpend(adEditSpendID, adEditTypeID, adEditPaidDate, adEditStartDate, adEditEndDate, adEditSpendAmount){

    $.ajax({
        url: window.fx_url.BASE + 'marketing-editSpend.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data:{
            adEditSpendID: adEditSpendID,
            adEditTypeID: adEditTypeID,
            adEditPaidDate: adEditPaidDate,
            adEditStartDate: adEditStartDate,
            adEditEndDate: adEditEndDate,
            adEditSpendAmount:adEditSpendAmount
        },
        success: function (response) {
            //populate the parent type combo & select
            //the correct option
            var allDates = defaultDates();
            var defaultStartDate = allDates['startDate'];
            var defaultEndDate = allDates['endDate'];

            $('#closeAdEditSpend').trigger('click');

            getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
            $('#loading-image').hide();

        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            $('#loading-image').hide();
        }
    });
}

function marketingMetricsAddSpend(adTypeID, adPaidDate, adStartDate, adEndDate, adSpendAmount){

    //ajax call.

    $.ajax({
        url: window.fx_url.BASE + 'marketing-AddSpend.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data:{
            adTypeID: adTypeID,
            adPaidDate: adPaidDate,
            adStartDate: adStartDate,
            adEndDate: adEndDate,
            adSpendAmount:adSpendAmount
        },
        success: function (response) {
            //populate the parent type combo & select
            //the correct option
            var allDates = defaultDates();
            var defaultStartDate = allDates['startDate'];
            var defaultEndDate = allDates['endDate'];

            $('#closeAdSpend').trigger('click');

            getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
            $('#loading-image').hide();

        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
            $('#loading-image').hide();
        }
    });



}

function editSpendRow(spendRowID, typeID, parentTypeID){
    //get the spendRow with all relevant other data by ID
    $.ajax({
        url: window.fx_url.BASE + 'getSpendRowByID.php',
        dataType:'JSON',
        type:'POST',
        contentType:'application/x-www-form-urlencoded',
        data:{
            spendRowID: spendRowID,
            parentTypeID: parentTypeID
        },
        success: function(response){
            //response[0] is the spend row record
            //response[1] is the list of all top-level marketing types (sources)
            //response[2] is the list of all sub-sources;

            //open the adEditSpendModal
            var parentTypeSelection = document.getElementById("adEditParentTypeSelection");
            parentTypeSelection.remove(0);  //by default these have 'select' in them..remove this
            for(i=0; i<response[1].length; i++){
                var option = document.createElement("option");

                parentTypeSelection.add(option);
                option.text = response[1][i]['marketingTypeName'];
                option.value = response[1][i]['marketingTypeID'];
            }

            var optionToSelect = parentTypeID;
            $('#adEditParentTypeSelection').val(optionToSelect);
            $('#adEditParentTypeID').val(optionToSelect);

            //populate the sub-source Combo;
            var subSourceSelect = document.getElementById('adEditSubSourceSelection');

            subSourceSelect.remove(0);

            for(i=0; i<response[2].length; i++){
                var subSourceOption = document.createElement('option');
                subSourceOption.text = response[2][i]['marketingTypeName'];
                subSourceOption.value = response[2][i]['marketingTypeID'];
                subSourceSelect.add(subSourceOption);
            }

            $('#adEditSubSourceSelection').val(typeID);
            $('#adEditSubsourceID').val(typeID);

            var allDates = defaultDates(response[0]['spendDate'], null);
            var paidDateFormatted = allDates['startDate'];

            if(paidDateFormatted=='12/31/1969'){
                paidDateFormatted ='';
            }

            var allDates = defaultDates(response[0]['startDate'],response[0]['endDate'] );

            var startDateFormatted = allDates['startDate'];
            var endDateFormatted = allDates['endDate'];

            $('#adEditPaidDate').val(paidDateFormatted);
            $('#adEditStartDate').val(startDateFormatted);
            $('#adEditEndDate').val(endDateFormatted);
            $('#adEditSpendAmount').val(response[0]['spendAmount']);
            $('#adEditSpendRowID').val(spendRowID);

            $('#editSpendModal').foundation('open');
            $('#loading-image').hide();

        },
        error: function(jqXHR, textStatus, errorThrown){
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);

        }

    });

}

//Document Ready Starts
$(document).ready(function(){
    $('#closeAdEditSpend').click(function(){
        $('#adStartDate').val('');
        $('.evaluationData, .date-picker').not('#dateFilterFrom').not('#dateFilterTo').each(function(){
            $(this).val('');
            $(this).removeClass('is-invalid-input');

            var errorField = '#' + $(this).attr('id') + 'Error';
            $(errorField).hide();
        });

        $('#adEditSubSourceSelection').empty();
        $('#adEditParentTypeSelection').empty();
        $('#editSpendModal').foundation('close');

    });

    $('.evaluationData').change(function(){
        $(this).removeClass('is-invalid-input');
        var error = '#' + $(this).attr('id') + 'Error';
        $(error).hide();
    });

    $('#closeAdSpend').click(function(){
        $('#adStartDate').val('');
        $('.evaluationData, .date-picker').not('#dateFilterFrom').not('#dateFilterTo').each(function(){
            $(this).val('');
            $(this).removeClass('is-invalid-input');
        })

        $('.form-error').each(function(){
            $(this).hide();
        });
        $('#adParentTypeSelection').empty();
        $('#adSubSourceSelection').empty();

        $('#addSpendModal').foundation('close');
    });

    $('#adEditParentTypeSelection').change(function(){
        $('#adEditParentTypeID').val($(this).val());

        //populate the subsources
        var parentID = $('#adEditParentTypeID').val();

        $.ajax({
            url: window.fx_url.BASE + 'getAllMarketingSubSourcesByParentID.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data:{
                parentID: parentID,
                returnParentNames: 'false'  //set this to true if you want the parent names & ID's in position[1] of the result set
            },
            success: function (response) {
                $('#loading-image').show();

                //populate the sub-source Combo;
                var subSourceSelect = document.getElementById('adEditSubSourceSelection');
                var saveBtn = document.getElementById('editAdSpend');

                $('#adEditSubSourceSelection').empty();

                subSourceSelect.remove(0);

                if(Array.isArray(response)) {
                    for (i = 0; i < response[0].length; i++) {
                        var subSourceOption = document.createElement('option');
                        subSourceOption.text = response[0][i]['marketingTypeName'];
                        subSourceOption.value = response[0][i]['marketingTypeID'];
                        subSourceSelect.add(subSourceOption);
                    }
                    $('#adEditSubSourceSelection').val(response[0][0]['marketingTypeID']);
                    $('#adEditSubsourceID').val(response[0][0]['marketingTypeID']);
                    subSourceSelect.disabled = false;
                    saveBtn.disabled = false;
                } else {
                    var subSourceOption = document.createElement('option');
                    subSourceOption.text = '--- None ---';
                    subSourceSelect.add(subSourceOption);
                    subSourceSelect.disabled = true;
                    saveBtn.disabled = true;
                }

                $('#loading-image').hide();

            }
        });

    });

    $('#adParentTypeSelection').change(function(){

        $('#adParentTypeID').val($(this).val());

        // console.log("parentID " + $('#adParentTypeID').val());

        //populate the subsources
        var parentID = $('#adParentTypeID').val();

        $.ajax({
            url: window.fx_url.BASE + 'getAllMarketingSubSourcesByParentID.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data:{
                parentID: parentID,
                returnParentNames: 'false'  //set this to true if you want the parent names & ID's in position[1] of the result set
            },
            success: function (response) {
                $('#loading-image').show();

                //populate the sub-source Combo;
                var subSourceSelect = document.getElementById('adSubSourceSelection');
                var saveBtn = document.getElementById('saveAdSpend');

                $('#adSubSourceSelection').empty();

                subSourceSelect.remove(0);

                if(Array.isArray(response)) {
                    for (i = 0; i < response[0].length; i++) {
                        var subSourceOption = document.createElement('option');
                        subSourceOption.text = response[0][i]['marketingTypeName'];
                        subSourceOption.value = response[0][i]['marketingTypeID'];
                        subSourceSelect.add(subSourceOption);
                    }
                    $('#adSubSourceSelection').val(response[0][0]['marketingTypeID']);
                    $('#adSubsourceID').val(response[0][0]['marketingTypeID']);
                    subSourceSelect.disabled = false;
                    saveBtn.disabled = false;

                } else {
                    var subSourceOption = document.createElement('option');
                    subSourceOption.text = '--- None ---';
                    subSourceSelect.add(subSourceOption);
                    subSourceSelect.disabled = true;
                    saveBtn.disabled = true;
                }

                $('#loading-image').hide();
            }
        });
    });

    $('#adEditSubSourceSelection').change(function(){
        $('#adEditSubsourceID').val($(this).val());
    })

    $('#adSubSourceSelection').change(function(){
        $('#adSubsourceID').val($(this).val());
        // console.log("subsource ID " + $('#adSubsourceID').val());
    });


    $('#editAdSpend').click(function(){

        //validate data first

        var saveRecord = 0;
        var adEditSpendRowID = $('#adEditSpendRowID').val();

        var adEditParentTypeID = $('#adEditParentTypeID').val();
        var adEditTypeID = $('#adEditSubsourceID').val();
        var adEditPaidDate = $('#adEditPaidDate').val();
        var adEditStartDate = Date.parse($('#adEditStartDate').val());
        var adEditEndDate = Date.parse($('#adEditEndDate').val());
        var adEditSpendAmount = $('#adEditSpendAmount').val();


        if (isNaN(adEditStartDate)===true){
            $('#adEditStartDateError').show();
            $('#adEditStartDate').addClass("is-invalid-input");
            saveRecord -=1;
        }else{
            $('#adEditStartDateError').hide();
            $('#adEditStartDate').removeClass();("is-invalid-input");
            saveRecord +=1;
            adEditStartDate = $('#adEditStartDate').val();

        }

        if(isNaN(adEditEndDate)===true){
            $('#adEditEndDateError').show();
            $('#adEditEndDate').addClass('is-invalid-input');
            saveRecord -=1;
        }else{
            $('#adEditEndDateError').hide();
            $('#adEditEndDate').removeClass('is-invalid-input');
            saveRecord +=1;
            adEditEndDate = $('#adEditEndDate').val();
        }

        if(isNaN(adEditSpendAmount)===true || adEditSpendAmount==''){
            $('#adEditSpendAmountError').show();
            $('#adEditSpendAmount').addClass('is-invalid-input');
            saveRecord -=1;
        }else{
            $('#adEditSpendAmountError').hide();
            $('#adEditSpendAmount').removeClass('is-invalid-input');
            saveRecord +=1;
        }

        if (saveRecord<3){
            return false;
        }

        //if you get here, validation passed.
        marketingMetricsEditSpend(adEditSpendRowID, adEditTypeID, adEditPaidDate, adEditStartDate, adEditEndDate, adEditSpendAmount);

    });


    $('#saveAdSpend').click(function(){

        //validate data first

        var saveRecord = 0;

        var adParentTypeID = $('#adParentTypeID').val();
        var adTypeID = $('#adSubsourceID').val();
        var adPaidDate = $('#adPaidDate').val();
        var adStartDate = Date.parse($('#adStartDate').val());
        var adEndDate = Date.parse($('#adEndDate').val());
        var adSpendAmount = $('#adSpendAmount').val();

        if (isNaN(adStartDate)===true){
            $('#adStartDateError').show();
            $('#adStartDate').addClass("is-invalid-input");
            saveRecord -=1;
        }else{
            $('#adStartDateError').hide();
            $('#adStartDate').removeClass();("is-invalid-input");
            saveRecord +=1;
            adStartDate = $('#adStartDate').val();

        }

        if(isNaN(adEndDate)===true){
            $('#adEndDateError').show();
            $('#adEndDate').addClass('is-invalid-input');
            saveRecord -=1;
        }else{
            $('#adEndDateError').hide();
            $('#adEndDate').removeClass('is-invalid-input');
            saveRecord +=1;
            adEndDate = $('#adEndDate').val();
        }

        if(isNaN(adSpendAmount)===true || adSpendAmount==''){
            $('#adSpendAmountError').show();
            $('#adSpendAmount').addClass('is-invalid-input');
            saveRecord -=1;
        }else{
            $('#adSpendAmountError').hide();
            $('#adSpendAmount').removeClass('is-invalid-input');
            saveRecord +=1;
        }

        if (saveRecord<3){
            return false;
        }

        //if you get here, validation is successful.
        $('#loading-image').show();

        // console.log(adTypeID + " " + adPaidDate +" "+ adStartDate +" "+ adEndDate + " " + adSpendAmount);

        marketingMetricsAddSpend(adTypeID, adPaidDate, adStartDate, adEndDate, adSpendAmount);


    });

    $('#closeSubSource').click(function(){
        $('#parentTypeSelection').empty();
        $('#setupSubSourceModal').foundation('close');

    })

    $('#sourceName').change(function(){
        if ($('#sourceName').val()!=''){
            $('#sourceNameError').hide();
            $('#sourceNameError').removeClass("is-invalid-input");
            $('#sourceNameLabel').removeClass("is-invalid-label");
        }
    })

    $('#saveSubSource').click(function(){

        var subSourceID = $('#subsourceID').val();
        var sourceID = $('#parentTypeID').val();
        var subSourceName = $('#subSourceName').val();

        if(!subSourceName==''){
            $('#sourceNameError').hide();
            $('#sourceNameError').removeClass("is-invalid-input");
            $('#sourceNameLabel').removeClass("is-invalid-label");
            $.ajax({
                url: window.fx_url.BASE + 'marketingSubSource-edit.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    subSourceID: subSourceID,
                    sourceID: sourceID,
                    subSourceName: subSourceName
                },
                success: function (response) {
                    // console.log(response);
                    if(response=="OK"){
                        $('#updateMessageText').html('');
                        $('#updateMessageText').html('Update was successful.');
                        $('#updateMessage').foundation('open');

                    }else{
                        $('#updateMessageText').html('');
                        $('#updateMessageText').html('There was a problem with the update.');
                        $('#updateMessage').foundation('open');
                    }

                    getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());

                    $('#parentTypeSelection').empty();

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }else{
            $('#sourceNameError').show();
            $('#sourceNameError').addClass("is-invalid-input");
            $('#sourceNameLabel').addClass("is-invalid-label");
        }

    })

    $('#parentTypeSelection').change(function(){
        $('#parentTypeID').val($('#parentTypeSelection').val());
    })
    $(function () {

        allDates = defaultDates();

        defaultEndDate = allDates['endDate'];
        defaultStartDate = allDates['startDate'];


        $("#dateFilterFrom").datepicker({
            defaultDate:defaultStartDate,
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#dateFilterFrom').val(defaultStartDate);

        $("#dateFilterTo").datepicker({
            defaultDate: defaultEndDate,
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#dateFilterTo').val(defaultEndDate);

        $('#adPaidDate').datepicker({
            defaultDate: defaultStartDate,
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#adStartDate').datepicker({
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#adEndDate').datepicker({
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#adEditPaidDate').datepicker({
            changeMonth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#adEditStartDate').datepicker({
            defaultDate: defaultStartDate,
            changeMOnth: true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        $('#adEditEndDate').datepicker({
            changeMonth:true,
            numberOfMonths: 1,
            onSelect: function() {
                $(this).change();
            }
        });

        //have the form populated with the current months data automatically when the page is loaded
        getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
    });

    $('#dateFilterFrom').change(function(){

        var fromDate = new Date($(this).val());
        var toDate = new Date ($('#dateFilterTo').val());


        if (fromDate > toDate){
            var  monthArray = ['1','2','3','4','5','6','7','8','9','10','11','12'];
            var month = monthArray[fromDate.getMonth()]; //date returns months 0 indexed...
            var year = fromDate.getFullYear();
            var defaultStartDate = month + '/01/' + year;
            var defaultEndDate = new Date(fromDate.getFullYear(), fromDate.getMonth() + 1, 0, 23, 59, 59);

            var lastDay = defaultEndDate.getDate();
            defaultEndDate = month + "/" + lastDay + "/" + year;

            $('#dateFilterTo').val(defaultEndDate);
            $('#dateFilterFrom').val(defaultStartDate);

            getMarketingMetrics(defaultStartDate, defaultEndDate);
            $('#date-range-error').show();
        }else{
            getMarketingMetrics(moment(fromDate).format('MM/DD/YYYY'), moment(toDate).format('MM/DD/YYYY'));
            $('#date-range-error').hide();
        }
    });

    $('#dateFilterTo').change(function(){

        var fromDate = new Date($('#dateFilterFrom').val());
        var toDate = new Date ($(this).val());

        if (toDate < fromDate){
            var  monthArray = ['1','2','3','4','5','6','7','8','9','10','11','12'];
            var month = monthArray[toDate.getMonth()]; //date returns months 0 indexed...
            var year = toDate.getFullYear();
            var defaultStartDate = month + '/01/' + year;
            var defaultEndDate = new Date(toDate.getFullYear(), toDate.getMonth() + 1, 0, 23, 59, 59);

            var lastDay = defaultEndDate.getDate();
            defaultEndDate = month + "/" + lastDay + "/" + year;

            $('#dateFilterTo').val(defaultEndDate);
            $('#dateFilterFrom').val(defaultStartDate);

            getMarketingMetrics(defaultStartDate, defaultEndDate);
            $('#date-range-error').show();
        }else{
            getMarketingMetrics(moment(fromDate).format('MM/DD/YYYY'), moment(toDate).format('MM/DD/YYYY'));
            $('#date-range-error').hide();
        }
    })

    $("#marketingMetricsDiv, .detailsToggle").on('click', '.detailsToggle',function(e){
        var rowToHide = $(this).closest('.clickable');
        $(".clickable").removeClass("active");
        rowToHide.addClass("active");
        $(".clickable.active").find(".arrowRight, .arrowDown").toggle();
        rowToHide.removeClass("active");

        var adTypeID = rowToHide.prop("id");
        var itemToShow = rowToHide.attr('data-itemToShow');
        var temp = $('.clickable.'+itemToShow + '[data-belongsTo="'  + adTypeID + '"]');
        if (itemToShow != undefined){
            temp.each(function(){
                var displayStatus = $(this).css('display');
                if(displayStatus=='flex'){
                    $(this).css('display','none');
                    if ($(this).hasClass('subsource')){
                        //if you are closing a sub-source, you need to hide all those spendrows that are open as well'

                        //reset arrows on subsource
                        var rightArrow = $(this).find(".arrowRight");

                        if (rightArrow.css('display')!='inline-block'){
                            $(this).find(".arrowRight, .arrowDown").toggle();
                        }

                        //close all spendrows
                        $('.clickable.spendrow[data-belongsTo="' + $(this).attr('id') + '"]').each(function(){
                            $(this).css('display', 'none');
                        })
                    }
                }
                else{
                    $(this).css('display','flex');
                }
            });
        }
    });

    $('#addParentTypeSelection').focusout(function(){
        if($(this).find('option:selected').val()!='0'){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        }
    });

    $('#addSubSourceName').focusout(function(){
        if($(this).val()!=''){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        }
    });

    $('#editSourceName').focusout(function(){
        if($(this).val()!=''){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        }
    });

    $('#addSourceName').focusout(function(){
        if($(this).val()!=''){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        }
    });

    var addSubSource = function (parentID) {
        $.ajax({
            url: window.fx_url.BASE + 'getMarketingSources.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            success: function (response) {
                $('#addParentTypeSelection').empty();
                $('#addParentTypeSelection').append('<option value="0">Select</option>');
                if (response != null){
                    $.each(response, function (i, item) {
                        $('#addParentTypeSelection').append('<option value="' + item.marketingTypeID + '">' + item.marketingTypeName + '</option>');
                    });
                }
                $('#addParentTypeSelection').find('option[value="' + parentID + '"]').prop('selected', true);
                $('#addSubSourceModal').foundation('open');
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    var saveAddSubSource = function () {
        var parentMarketingTypeID = $('#addParentTypeSelection option:selected').val();
        var marketingTypeName = $('#addSubSourceName').val();
        var validated = true;

        if (parentMarketingTypeID == 0){
            $('#addParentTypeSelection').addClass('is-invalid-input');
            validated = false;
        }

        if (marketingTypeName == ''){
            $('#addSubSourceName').addClass('is-invalid-input');
            validated = false;
        }
        if (validated){
            $.ajax({
                url: window.fx_url.BASE + 'saveNewSubSource.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    parentMarketingTypeID: parentMarketingTypeID,
                    marketingTypeName: marketingTypeName,
                },
                success: function (response) {
                    getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
                    $('#addSubSourceName').val('');
                    $('#addSubSourceModal').foundation('close');
                    $('#updateMessage').foundation('open');
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    };

    var editSource = function (sourceID) {
        var sourceName = $('[source="' + sourceID + '"]').parent().parent().find('strong').text();
        $('#editSourceName').val(sourceName);
        $('#sourceID').val(sourceID);
        $('#editSourceModal').foundation('open');
    };

    var saveEditSource = function () {
        var marketingTypeName = $('#editSourceName').val();
        var marketingTypeID = $('#sourceID').val();
        var validated = true;

        if (marketingTypeName == ''){
            $('#editSourceName').addClass('is-invalid-input');
            validated = false;
        }
        if (validated){
            $.ajax({
                url: window.fx_url.BASE + 'editSource.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    marketingTypeID: marketingTypeID,
                    marketingTypeName: marketingTypeName,
                },
                success: function (response) {
                    $('[source="' + marketingTypeID + '"]').parent().parent().find('strong').text(marketingTypeName);
                    $('#editSourceModal').foundation('close');
                    $('#updateMessage').foundation('open');
                    getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    };

    var saveAddSource = function () {
        var marketingTypeName = $('#addSourceName').val();
        var validated = true;

        if (marketingTypeName == ''){
            $('#addSourceName').addClass('is-invalid-input');
            validated = false;
        }
        if (validated){
            $.ajax({
                url: window.fx_url.BASE + 'saveNewSource.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    marketingTypeName: marketingTypeName,
                },
                success: function (response) {
                    getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
                    $('#date-range-noData').hide();
                    $('#addSourceName').val('');
                    $('#addSourceModal').foundation('close');
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    };

    var deleteMarketingItem = function (itemID, itemType) {
        $.ajax({
            url: window.fx_url.BASE + 'deleteMarketingItem.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                itemID: itemID,
                itemType: itemType,
            },
            success: function (response) {
                getMarketingMetrics($('#dateFilterFrom').val(), $('#dateFilterTo').val());
                $('#deleteItemModal').foundation('close');
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    $('body').on('click', '.editSource',function(){
        editSource($(this).attr('source'));
    });

    $('#saveEditSource').click(saveEditSource);

    $('body').on('click', '.addSubSource',function(){
        addSubSource($(this).attr('parentID'));
    });

    $('#saveAddSubSource').click(saveAddSubSource);

    $('#addSource').click(function(){
        $('#addSourceModal').foundation('open');
    });
    $('#saveAddSource').click(saveAddSource);

    $('#cancelAddSource').click(function (){
        $('#addSourceName').val('');
        $('#addSourceModal').foundation('close');
    });

    $('#cancelAddSubSource').click(function (){
        $('#addSubSourceName').val('');
        $('#addSubSourceModal').foundation('close');
    });

    $('body').on('click', '.deleteSource',function(){
        var marketingTypeID = $(this).parent().parent().attr('id');
        var name = $(this).parent().parent().find('strong').text();

        $('#deleteItemModal h3').text('Delete Category');
        $('#deleteItemModal p').text('Are you sure you want to delete the category "' + name + '"? All sources tied to this category and its spend items will also be deleted.');
        $('#itemID').val(marketingTypeID);
        $('#itemType').val('source');

        $('#deleteItemModal').foundation('open');
    });

    $('body').on('click', '.deleteSubSource',function(){
        var marketingTypeID = $(this).parent().parent().attr('id');
        var name = $(this).parent().parent().find('strong').text();

        $('#deleteItemModal h3').text('Delete Source');
        $('#deleteItemModal p').text('Are you sure you want to delete the source "' + name + '"? All spend items tied to this source will also be deleted.');
        $('#itemID').val(marketingTypeID);
        $('#itemType').val('subsource');

        $('#deleteItemModal').foundation('open');
    });

    $('body').on('click', '.deleteSpend',function(){
        var marketingSpendID = $(this).parent().parent().attr('id');
        var parentMarketingTypeID = $(this).parent().parent().attr('data-belongsto');
        var name = $('#' + parentMarketingTypeID).find('strong').text();
        var date = $(this).parent().parent().find('strong').text();
        var amount = $(this).parent().parent().find('.spend').text();

        $('#deleteItemModal h3').text('Delete Marketing Spend');
        $('#deleteItemModal p').text('Are you sure you want to delete the marketing spend "' + name + '" from ' + date + ' (' + amount + ')' + '?');
        $('#itemID').val(marketingSpendID);
        $('#itemType').val('spend');

        $('#deleteItemModal').foundation('open');
    });

    $('#yesDeleteItem').click(function(){
        var itemID = $('#itemID').val();
        var itemType = $('#itemType').val();
        $('#itemID').val('');
        $('#itemType').val('');

        deleteMarketingItem(itemID, itemType);
    });

    $('#cancelDeleteItem').click(function (){
        $('#itemID').val('');
        $('#itemType').val('');
        $('#deleteItemModal').foundation('close');
    });

    //Document Ready Ends
});