/*!
FullCalendar Scheduler v1.6.2
Docs & License: https://fullcalendar.io/scheduler/
(c) 2017 <PERSON>
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","moment","fullcalendar"],e):"object"==typeof exports?module.exports=e(require("jquery"),require("moment"),require("fullcalendar")):e(jQuery,moment)}(function(e,t){var r,o,s,n,i,l,u,h,c,a,p,d,f,g,y,v,R,w,m,S,b,C,E,H,T,_,I,D,x,G,B,F,W,M,L,z,k,P,O,A,q,N,V,U,j,Y,Q,X,$,K,J,Z,ee,te,re,oe,se,ne,ie,le,ue,he,ce,ae,pe,de,fe,ge,ye,ve,<PERSON>,we,me,<PERSON>,be,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,_e,<PERSON><PERSON>,<PERSON>,xe,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,ze,ke,<PERSON>e,<PERSON><PERSON>,A<PERSON>,qe,Ne,Ve,Ue=function(e,t){function r(){this.constructor=e}for(var o in t)je.call(t,o)&&(e[o]=t[o]);return r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype,e},je={}.hasOwnProperty,Ye=[].indexOf||function(e){for(var t=0,r=this.length;t<r;t++)if(t in this&&this[t]===e)return t;return-1},Qe=[].slice;return d=e.fullCalendar,d.schedulerVersion="1.6.2",9!==d.internalApiVersion?void d.warn("v"+d.schedulerVersion+" of FullCalendar Scheduler is incompatible with v"+d.version+" of the core.\nPlease see http://fullcalendar.io/support/ for more information."):(o=d.Calendar,n=d.Class,K=d.View,f=d.Grid,be=d.intersectRanges,ue=d.debounce,He=d.isInt,we=d.getScrollbarWidths,h=d.DragListener,Se=d.htmlEscape,re=d.computeGreatestUnit,ke=d.proxy,ee=d.capitaliseFirstLetter,Z=d.applyAll,c=d.EmitterMixin,v=d.ListenerMixin,pe=d.durationHasTime,ae=d.divideRangeByDuration,ce=d.divideDurationByDuration,Ie=d.multiplyDuration,Le=d.parseFieldSpecs,te=d.compareByFieldSpecs,de=d.flexibleCompare,Ce=d.intersectRects,l=d.CoordCache,fe=d.getContentRect,ge=d.getOuterRect,ie=d.createObject,C=d.Promise,U=d.TaskQueue,ye=function(e){return e.find("> td").filter(function(e,t){return t.rowSpan<=1})},a=function(t){function r(){r.__super__.constructor.apply(this,arguments),this.requestMovingEnd=ue(this.reportMovingEnd,500)}var o,s;return Ue(r,t),r.mixin(c),r.mixin(v),r.prototype.canvas=null,r.prototype.isScrolling=!1,r.prototype.isTouching=!1,r.prototype.isTouchedEver=!1,r.prototype.isMoving=!1,r.prototype.isTouchScrollEnabled=!0,r.prototype.preventTouchScrollHandler=null,r.prototype.render=function(){return r.__super__.render.apply(this,arguments),this.canvas&&(this.canvas.render(),this.canvas.el.appendTo(this.scrollEl)),this.bindHandlers()},r.prototype.destroy=function(){return r.__super__.destroy.apply(this,arguments),this.unbindHandlers()},r.prototype.disableTouchScroll=function(){return this.isTouchScrollEnabled=!1,this.bindPreventTouchScroll()},r.prototype.enableTouchScroll=function(){if(this.isTouchScrollEnabled=!0,!this.isTouching)return this.unbindPreventTouchScroll()},r.prototype.bindPreventTouchScroll=function(){if(!this.preventTouchScrollHandler)return this.scrollEl.on("touchmove",this.preventTouchScrollHandler=d.preventDefault)},r.prototype.unbindPreventTouchScroll=function(){if(this.preventTouchScrollHandler)return this.scrollEl.off("touchmove",this.preventTouchScrollHandler),this.preventTouchScrollHandler=null},r.prototype.bindHandlers=function(){return this.listenTo(this.scrollEl,{scroll:this.reportScroll,touchstart:this.reportTouchStart,touchend:this.reportTouchEnd})},r.prototype.unbindHandlers=function(){return this.stopListeningTo(this.scrollEl)},r.prototype.reportScroll=function(){return this.isScrolling||this.reportScrollStart(),this.trigger("scroll"),this.isMoving=!0,this.requestMovingEnd()},r.prototype.reportScrollStart=function(){if(!this.isScrolling)return this.isScrolling=!0,this.trigger("scrollStart",this.isTouching)},r.prototype.requestMovingEnd=null,r.prototype.reportMovingEnd=function(){if(this.isMoving=!1,!this.isTouching)return this.reportScrollEnd()},r.prototype.reportScrollEnd=function(){if(this.isScrolling)return this.trigger("scrollEnd"),this.isScrolling=!1},r.prototype.reportTouchStart=function(){return this.isTouching=!0,this.isTouchedEver=!0},r.prototype.reportTouchEnd=function(){if(this.isTouching&&(this.isTouching=!1,this.isTouchScrollEnabled&&this.unbindPreventTouchScroll(),!this.isMoving))return this.reportScrollEnd()},r.prototype.getScrollLeft=function(){var e,t,r;if(e=this.scrollEl.css("direction"),t=this.scrollEl[0],r=t.scrollLeft,"rtl"===e)switch(s){case"positive":r=r+t.clientWidth-t.scrollWidth;break;case"reverse":r=-r}return r},r.prototype.setScrollLeft=function(e){var t,r;if(t=this.scrollEl.css("direction"),r=this.scrollEl[0],"rtl"===t)switch(s){case"positive":e=e-r.clientWidth+r.scrollWidth;break;case"reverse":e=-e}return r.scrollLeft=e},r.prototype.getScrollFromLeft=function(){var e,t,r;if(e=this.scrollEl.css("direction"),t=this.scrollEl[0],r=t.scrollLeft,"rtl"===e)switch(s){case"negative":r=r-t.clientWidth+t.scrollWidth;break;case"reverse":r=-r-t.clientWidth+t.scrollWidth}return r},r.prototype.getNativeScrollLeft=function(){return this.scrollEl[0].scrollLeft},r.prototype.setNativeScrollLeft=function(e){return this.scrollEl[0].scrollLeft=e},s=null,o=function(){var t,r,o;return t=e('<div style=" position: absolute top: -1000px; width: 1px; height: 1px; overflow: scroll; direction: rtl; font-size: 14px; ">A</div>').appendTo("body"),r=t[0],o=r.scrollLeft>0?"positive":(r.scrollLeft=1,t.scrollLeft>0?"reverse":"negative"),t.remove(),o},e(function(){return s=o()}),r}(d.Scroller),i=function(t){function r(){r.__super__.constructor.apply(this,arguments),"clipped-scroll"===this.overflowX&&(this.overflowX="scroll",this.isHScrollbarsClipped=!0),"clipped-scroll"===this.overflowY&&(this.overflowY="scroll",this.isVScrollbarsClipped=!0)}return Ue(r,t),r.prototype.isHScrollbarsClipped=!1,r.prototype.isVScrollbarsClipped=!1,r.prototype.renderEl=function(){var t;return t=r.__super__.renderEl.apply(this,arguments),e('<div class="fc-scroller-clip" />').append(t)},r.prototype.updateSize=function(){var e,t,r;return t=this.scrollEl,r=we(t),e={marginLeft:0,marginRight:0,marginTop:0,marginBottom:0},this.isHScrollbarsClipped&&(e.marginTop=-r.top,e.marginBottom=-r.bottom),this.isVScrollbarsClipped&&(e.marginLeft=-r.left,e.marginRight=-r.right),t.css(e),t.toggleClass("fc-no-scrollbars",(this.isHScrollbarsClipped||"hidden"===this.overflowX)&&(this.isVScrollbarsClipped||"hidden"===this.overflowY)&&!(r.top||r.bottom||r.left||r.right))},r.prototype.getScrollbarWidths=function(){var e;return e=we(this.scrollEl),this.isHScrollbarsClipped&&(e.top=0,e.bottom=0),this.isVScrollbarsClipped&&(e.left=0,e.right=0),e},r}(a),N=function(){function t(){this.gutters={}}return t.prototype.el=null,t.prototype.contentEl=null,t.prototype.bgEl=null,t.prototype.gutters=null,t.prototype.width=null,t.prototype.minWidth=null,t.prototype.render=function(){return this.el=e('<div class="fc-scroller-canvas"> <div class="fc-content"></div> <div class="fc-bg"></div> </div>'),this.contentEl=this.el.find(".fc-content"),this.bgEl=this.el.find(".fc-bg")},t.prototype.setGutters=function(t){return t?e.extend(this.gutters,t):this.gutters={},this.updateSize()},t.prototype.setWidth=function(e){return this.width=e,this.updateSize()},t.prototype.setMinWidth=function(e){return this.minWidth=e,this.updateSize()},t.prototype.clearWidth=function(){return this.width=null,this.minWidth=null,this.updateSize()},t.prototype.updateSize=function(){var e;return e=this.gutters,this.el.toggleClass("fc-gutter-left",Boolean(e.left)).toggleClass("fc-gutter-right",Boolean(e.right)).toggleClass("fc-gutter-top",Boolean(e.top)).toggleClass("fc-gutter-bottom",Boolean(e.bottom)).css({paddingLeft:e.left||"",paddingRight:e.right||"",paddingTop:e.top||"",paddingBottom:e.bottom||"",width:null!=this.width?this.width+(e.left||0)+(e.right||0):"",minWidth:null!=this.minWidth?this.minWidth+(e.left||0)+(e.right||0):""}),this.bgEl.css({left:e.left||"",right:e.right||"",top:e.top||"",bottom:e.bottom||""})},t}(),q=function(){function e(e,t){var r,o,s,n;for(this.axis=e,this.scrollers=t,s=this.scrollers,r=0,o=s.length;r<o;r++)n=s[r],this.initScroller(n)}return e.prototype.axis=null,e.prototype.scrollers=null,e.prototype.masterScroller=null,e.prototype.initScroller=function(e){return e.scrollEl.on("wheel mousewheel DomMouseScroll MozMousePixelScroll",function(t){return function(){t.assignMasterScroller(e)}}(this)),e.on("scrollStart",function(t){return function(){if(!t.masterScroller)return t.assignMasterScroller(e)}}(this)).on("scroll",function(t){return function(){var r,o,s,n,i;if(e===t.masterScroller){for(n=t.scrollers,i=[],r=0,o=n.length;r<o;r++)if(s=n[r],s!==e)switch(t.axis){case"horizontal":i.push(s.setNativeScrollLeft(e.getNativeScrollLeft()));break;case"vertical":i.push(s.setScrollTop(e.getScrollTop()));break;default:i.push(void 0)}else i.push(void 0);return i}}}(this)).on("scrollEnd",function(t){return function(){if(e===t.masterScroller)return t.unassignMasterScroller()}}(this))},e.prototype.assignMasterScroller=function(e){var t,r,o,s;for(this.unassignMasterScroller(),this.masterScroller=e,s=this.scrollers,t=0,r=s.length;t<r;t++)o=s[t],o!==e&&o.disableTouchScroll()},e.prototype.unassignMasterScroller=function(){var e,t,r,o;if(this.masterScroller){for(o=this.scrollers,e=0,t=o.length;e<t;e++)r=o[e],r.enableTouchScroll();this.masterScroller=null}},e.prototype.update=function(){var e,t,r,o,s,n,i,l,u,h,c,a,p;for(e=function(){var e,t,r,o;for(r=this.scrollers,o=[],e=0,t=r.length;e<t;e++)a=r[e],o.push(a.getScrollbarWidths());return o}.call(this),l=u=h=i=0,r=0,s=e.length;r<s;r++)p=e[r],l=Math.max(l,p.left),u=Math.max(u,p.right),h=Math.max(h,p.top),i=Math.max(i,p.bottom);for(c=this.scrollers,t=o=0,n=c.length;o<n;t=++o)a=c[t],p=e[t],a.canvas.setGutters("horizontal"===this.axis?{left:l-p.left,right:u-p.right}:{top:h-p.top,bottom:i-p.bottom})},e}(),O=function(){function t(e,t){this.allowPointerEvents=null!=t&&t,this.scroller=e,this.sprites=[],e.on("scroll",function(t){return function(){return e.isTouchedEver?(t.isTouch=!0,t.isForcedRelative=!0):(t.isTouch=!1,t.isForcedRelative=!1,t.handleScroll())}}(this)),e.on("scrollEnd",function(e){return function(){return e.handleScroll()}}(this))}return t.prototype.scroller=null,t.prototype.scrollbarWidths=null,t.prototype.sprites=null,t.prototype.viewportRect=null,t.prototype.contentOffset=null,t.prototype.isHFollowing=!0,t.prototype.isVFollowing=!1,t.prototype.allowPointerEvents=!1,t.prototype.containOnNaturalLeft=!1,t.prototype.containOnNaturalRight=!1,t.prototype.minTravel=0,t.prototype.isTouch=!1,t.prototype.isForcedRelative=!1,t.prototype.setSprites=function(t){var r,o,s;if(this.clearSprites(),t instanceof e)return this.sprites=function(){var r,o,n;for(n=[],r=0,o=t.length;r<o;r++)s=t[r],n.push(new A(e(s),this));return n}.call(this);for(r=0,o=t.length;r<o;r++)s=t[r],s.follower=this;return this.sprites=t},t.prototype.clearSprites=function(){var e,t,r,o;for(r=this.sprites,e=0,t=r.length;e<t;e++)o=r[e],o.clear();return this.sprites=[]},t.prototype.handleScroll=function(){return this.updateViewport(),this.updatePositions()},t.prototype.cacheDimensions=function(){var e,t,r,o;for(this.updateViewport(),this.scrollbarWidths=this.scroller.getScrollbarWidths(),this.contentOffset=this.scroller.canvas.el.offset(),r=this.sprites,e=0,t=r.length;e<t;e++)o=r[e],o.cacheDimensions()},t.prototype.updateViewport=function(){var e,t,r;return t=this.scroller,e=t.getScrollFromLeft(),r=t.getScrollTop(),this.viewportRect={left:e,right:e+t.getClientWidth(),top:r,bottom:r+t.getClientHeight()}},t.prototype.forceRelative=function(){var e,t,r,o,s;if(!this.isForcedRelative){for(this.isForcedRelative=!0,r=this.sprites,o=[],e=0,t=r.length;e<t;e++)s=r[e],s.doAbsolute?o.push(s.assignPosition()):o.push(void 0);return o}},t.prototype.clearForce=function(){var e,t,r,o,s;if(this.isForcedRelative&&!this.isTouch){for(this.isForcedRelative=!1,r=this.sprites,o=[],e=0,t=r.length;e<t;e++)s=r[e],o.push(s.assignPosition());return o}},t.prototype.update=function(){return this.cacheDimensions(),this.updatePositions()},t.prototype.updatePositions=function(){var e,t,r,o;for(r=this.sprites,e=0,t=r.length;e<t;e++)o=r[e],o.updatePosition()},t.prototype.getContentRect=function(e){return fe(e,this.contentOffset)},t.prototype.getBoundingRect=function(e){return ge(e,this.contentOffset)},t}(),A=function(){function e(e,t){this.el=e,this.follower=null!=t?t:null,this.isBlock="block"===this.el.css("display"),this.el.css("position","relative")}return e.prototype.follower=null,e.prototype.el=null,e.prototype.absoluteEl=null,e.prototype.naturalRect=null,e.prototype.parentRect=null,e.prototype.containerRect=null,e.prototype.isEnabled=!0,e.prototype.isHFollowing=!1,e.prototype.isVFollowing=!1,e.prototype.doAbsolute=!1,e.prototype.isAbsolute=!1,e.prototype.isCentered=!1,e.prototype.rect=null,e.prototype.isBlock=!1,e.prototype.naturalWidth=null,e.prototype.disable=function(){if(this.isEnabled)return this.isEnabled=!1,this.resetPosition(),this.unabsolutize()},e.prototype.enable=function(){if(!this.isEnabled)return this.isEnabled=!0,this.assignPosition()},e.prototype.clear=function(){return this.disable(),this.follower=null,this.absoluteEl=null},e.prototype.cacheDimensions=function(){var e,t,r,o,s,n,i,l;return o=!1,s=!1,r=!1,this.naturalWidth=this.el.width(),this.resetPosition(),t=this.follower,i=this.naturalRect=t.getBoundingRect(this.el),l=this.el.parent(),this.parentRect=t.getBoundingRect(l),e=this.containerRect=_e(t.getContentRect(l),i),n=t.minTravel,t.containOnNaturalLeft&&(e.left=i.left),t.containOnNaturalRight&&(e.right=i.right),t.isHFollowing&&Re(e)-Re(i)>=n&&(r="center"===this.el.css("text-align"),o=!0),t.isVFollowing&&ve(e)-ve(i)>=n&&(s=!0),this.isHFollowing=o,this.isVFollowing=s,this.isCentered=r},e.prototype.updatePosition=function(){return this.computePosition(),this.assignPosition()},e.prototype.resetPosition=function(){return this.el.css({top:"",left:""})},e.prototype.computePosition=function(){var e,t,r,o,s,n,i,l;return i=this.follower.viewportRect,r=this.parentRect,e=this.containerRect,l=Ce(i,r),o=null,t=!1,l&&(o=ne(this.naturalRect),n=Ce(o,r),(this.isCentered&&!Oe(i,r)||n&&!Oe(i,n))&&(t=!0,this.isHFollowing&&(this.isCentered?(s=Re(o),o.left=(l.left+l.right)/2-s/2,o.right=o.left+s):me(o,i)||(t=!1),me(o,e)&&(t=!1)),this.isVFollowing&&(Ve(o,i)||(t=!1),Ve(o,e)&&(t=!1)),Oe(i,o)||(t=!1))),this.rect=o,this.doAbsolute=t},e.prototype.assignPosition=function(){var e,t;if(this.isEnabled)return this.rect?this.doAbsolute&&!this.follower.isForcedRelative?(this.absolutize(),this.absoluteEl.css({top:this.rect.top-this.follower.viewportRect.top+this.follower.scrollbarWidths.top,left:this.rect.left-this.follower.viewportRect.left+this.follower.scrollbarWidths.left,width:this.isBlock?this.naturalWidth:""})):(t=this.rect.top-this.naturalRect.top,e=this.rect.left-this.naturalRect.left,this.unabsolutize(),this.el.toggleClass("fc-following",Boolean(t||e)).css({top:t,left:e})):this.unabsolutize()},e.prototype.absolutize=function(){if(!this.isAbsolute)return this.absoluteEl||(this.absoluteEl=this.buildAbsoluteEl()),this.absoluteEl.appendTo(this.follower.scroller.el),this.el.css("visibility","hidden"),this.isAbsolute=!0},e.prototype.unabsolutize=function(){if(this.isAbsolute)return this.absoluteEl.detach(),this.el.css("visibility",""),this.isAbsolute=!1},e.prototype.buildAbsoluteEl=function(){var e;return e=this.el.clone().addClass("fc-following"),e.css({position:"absolute","z-index":1e3,"font-weight":this.el.css("font-weight"),"font-size":this.el.css("font-size"),"font-family":this.el.css("font-family"),"text-decoration":this.el.css("text-decoration"),color:this.el.css("color"),"padding-top":this.el.css("padding-top"),"padding-bottom":this.el.css("padding-bottom"),"padding-left":this.el.css("padding-left"),"padding-right":this.el.css("padding-right")}),this.follower.allowPointerEvents||e.css("pointer-events","none"),e},e}(),ne=function(e){return{left:e.left,right:e.right,top:e.top,bottom:e.bottom}},Re=function(e){return e.right-e.left},ve=function(e){return e.bottom-e.top},Oe=function(e,t){return Ae(e,t)&&qe(e,t)},Ae=function(e,t){return t.left>=e.left&&t.right<=e.right},qe=function(e,t){return t.top>=e.top&&t.bottom<=e.bottom},me=function(e,t){return e.left<t.left?(e.right=t.left+Re(e),e.left=t.left,!0):e.right>t.right&&(e.left=t.right-Re(e),e.right=t.right,!0)},Ve=function(e,t){return e.top<t.top?(e.bottom=t.top+ve(e),e.top=t.top,!0):e.bottom>t.bottom&&(e.top=t.bottom-ve(e),e.bottom=t.bottom,!0)},_e=function(e,t){return{left:Math.min(e.left,t.left),right:Math.max(e.right,t.right),top:Math.min(e.top,t.top),bottom:Math.max(e.bottom,t.bottom)}},s=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.prototype.resourceManager=null,r.prototype.initialize=function(){return this.resourceManager=new x(this)},r.prototype.instantiateView=function(e){var t,r;return t=this.getViewSpec(e),r=t["class"],this.opt("resources")&&t.options.resources!==!1&&(t.queryResourceClass?r=t.queryResourceClass(t)||r:t.resourceClass&&(r=t.resourceClass)),new r(this,t)},r.prototype.getResources=function(){return Array.prototype.slice.call(this.resourceManager.topLevelResources)},r.prototype.addResource=function(e,t){var r;null==t&&(t=!1),r=this.resourceManager.addResource(e),t&&this.view.scrollToResource&&r.then(function(e){return function(t){return e.view.scrollToResource(t)}}(this))},r.prototype.removeResource=function(e){return this.resourceManager.removeResource(e)},r.prototype.refetchResources=function(){this.resourceManager.clear(),this.view.flash("initialResources")},r.prototype.rerenderResources=function(){this.resourceManager.resetCurrentResources()},r.prototype.isSpanAllowed=function(e,t){var o,s;return!("object"==typeof t&&(o=this.getEventResourceIds(t),o.length&&!(e.resourceId&&(s=e.resourceId,Ye.call(o,s)>=0))))&&r.__super__.isSpanAllowed.apply(this,arguments)},r.prototype.mutateSeg=function(t,r,o){var s,n,i,l;return r.resourceId&&(i=(null!=(l=t.resource)?l.id:void 0)||t.resourceId,n=r.resourceId,s=this.getEventResourceIds(t.event),i!==n&&(s=s.filter(function(e){return e!==i&&e!==n}),s.push(n)),r=e.extend({},r),this.setEventResourceIds(r,s)),this.mutateEvent(t.event,r,o)},r.prototype.getPeerEvents=function(e,t){var o,s,n,i,l,u,h,c,a,p,d,f,g,y;for(f=r.__super__.getPeerEvents.apply(this,arguments),p=e.resourceId?[e.resourceId]:t?this.getEventResourceIds(t):[],o=[],n=0,u=f.length;n<u;n++){if(d=f[n],s=!1,y=this.getEventResourceIds(d),y.length&&p.length){for(i=0,h=y.length;i<h;i++)for(g=y[i],l=0,c=p.length;l<c;l++)if(a=p[l],a===g){s=!0;break}}else s=!0;s&&o.push(d)}return o},r.prototype.spanContainsSpan=function(e,t){return(!e.resourceId||e.resourceId===t.resourceId)&&r.__super__.spanContainsSpan.apply(this,arguments)},r.prototype.getCurrentBusinessHourEvents=function(e){var t,o,s,n,i,l,u,h,c,a,p,d;for(i=this.resourceManager.getFlatResources(),o=!1,l=0,c=i.length;l<c;l++)d=i[l],d.businessHours&&(o=!0);if(o){for(t=[],u=0,a=i.length;u<a;u++)for(d=i[u],n=this.computeBusinessHourEvents(e,d.businessHours||this.opt("businessHours")),h=0,p=n.length;h<p;h++)s=n[h],s.resourceId=d.id,t.push(s);return t}return r.__super__.getCurrentBusinessHourEvents.apply(this,arguments)},r.prototype.buildSelectSpan=function(e,t,o){var s;return s=r.__super__.buildSelectSpan.apply(this,arguments),o&&(s.resourceId=o),s},r.prototype.getResourceById=function(e){return this.resourceManager.getResourceById(e)},r.prototype.normalizeEvent=function(e){return r.__super__.normalizeEvent.apply(this,arguments),null==e.resourceId&&(e.resourceId=null),null!=e.resourceIds?e.resourceIds:e.resourceIds=null},r.prototype.getEventResourceId=function(e){return this.getEventResourceIds(e)[0]},r.prototype.getEventResourceIds=function(e){var t,r,o,s,n,i,l,u;if(u=String(null!=(n=null!=(i=e[this.getEventResourceField()])?i:e.resourceId)?n:""))return[u];if(e.resourceIds){for(s=[],l=e.resourceIds,t=0,r=l.length;t<r;t++)u=l[t],o=String(null!=u?u:""),o&&s.push(o);return s}return[]},r.prototype.setEventResourceId=function(e,t){return this.setEventResourceIds(e,t?[t]:[])},r.prototype.setEventResourceIds=function(e,t){return e[this.getEventResourceField()]=1===t.length?t[0]:null,e.resourceIds=t.length>1?t:null},r.prototype.getEventResourceField=function(){return this.opt("eventResourceField")||"resourceId"},r.prototype.getResourceEvents=function(t){var r;return r="object"==typeof t?t:this.getResourceById(t),r?this.clientEvents(function(t){return function(o){return e.inArray(r.id,t.getEventResourceIds(o))!==-1}}(this)):[]},r.prototype.getEventResource=function(e){return this.getEventResources(e)[0]},r.prototype.getEventResources=function(e){var t,r,o,s,n,i,l;if(t="object"==typeof e?e:this.clientEvents(e)[0],l=[],t)for(s=this.getEventResourceIds(t),r=0,o=s.length;r<o;r++)i=s[r],n=this.getResourceById(i),n&&l.push(n);return l},r}(o),o.prototype=s.prototype,Me=K.prototype.setElement,We=K.prototype.removeElement,Fe=K.prototype.onBaseRender,o.defaults.refetchResourcesOnNavigate=!1,K.prototype.canHandleSpecificResources=!1,K.prototype.isDestroying=!1,K.prototype.setElement=function(){return Me.apply(this,arguments),this.watchResources()},K.prototype.removeElement=function(){return this.isDestroying=!0,this.unwatchResources(),We.apply(this,arguments),this.isDestroying=!1},K.prototype.onBaseRender=function(){return ze(this.calendar.opt("schedulerLicenseKey"),this.el),Fe.apply(this,arguments)},K.prototype.watchResources=function(){var e,t;return t=[],e=["initialResources"],this.opt("refetchResourcesOnNavigate")&&t.push("dateProfile"),this.opt("filterResourcesWithEvents")&&e.push("currentEvents"),this.watch("initialResources",t,function(e){return function(t){return e.getInitialResources(t.dateProfile)}}(this)),this.watch("bindingResources",e,function(e){return function(t){e.bindResourceChanges(t.currentEvents),e.setResources(t.initialResources,t.currentEvents)}}(this),function(e){return function(){e.unbindResourceChanges(),e.unsetResources()}}(this))},K.prototype.unwatchResources=function(){return this.unwatch("initialResources"),this.unwatch("bindingResources")},K.prototype.getInitialResources=function(e){return e?this.calendar.resourceManager.getResources(e.activeRange.start,e.activeRange.end):this.calendar.resourceManager.getResources()},K.prototype.bindResourceChanges=function(e){return this.listenTo(this.calendar.resourceManager,{set:function(t){return function(r){return t.setResources(r,e)}}(this),unset:function(e){return function(){return e.unsetResources()}}(this),reset:function(t){return function(r){return t.resetResources(r,e)}}(this),add:function(t){return function(r,o){return t.addResource(r,o,e)}}(this),remove:function(t){return function(r,o){return t.removeResource(r,o,e)}}(this)})},K.prototype.unbindResourceChanges=function(){return this.stopListeningTo(this.calendar.resourceManager)},K.watch("displayingEvents",["displayingDates","hasEvents","currentResources"],function(e){return this.requestEventsRender(this.get("currentEvents"))},function(){return this.requestEventsUnrender()}),K.prototype.setResources=function(e,t){return t&&(e=this.filterResourcesWithEvents(e,t)),this.set("currentResources",e),this.set("hasResources",!0),this.handleResourcesSet(e)},K.prototype.unsetResources=function(){return this.unset("currentResources"),this.unset("hasResources"),this.handleResourcesUnset()},K.prototype.resetResources=function(e,t){return this.startBatchRender(),this.unsetResources(),this.setResources(e,t),this.stopBatchRender()},K.prototype.addResource=function(e,t,r){var o;return this.canHandleSpecificResources?(r&&(o=this.filterResourcesWithEvents([e],r),o.length||(e=null)),e?(this.set("currentResources",t),this.handleResourceAdd(e)):void 0):this.resetResources(t,r)},K.prototype.removeResource=function(e,t,r){return this.canHandleSpecificResources?(this.set("currentResources",t),this.handleResourceRemove(e)):this.resetResources(t,r)},K.prototype.handleResourcesSet=function(e){},K.prototype.handleResourcesUnset=function(e){},K.prototype.handleResourceAdd=function(e){},K.prototype.handleResourceRemove=function(e){},K.prototype.filterResourcesWithEvents=function(e,t){var r,o,s,n,i,l,u,h;for(h={},o=0,n=t.length;o<n;o++)for(r=t[o],l=this.calendar.getEventResourceIds(r),s=0,i=l.length;s<i;s++)u=l[s],h[u]=!0;return J(e,h)},J=function(e,t){var r,o,s,n,i,l;for(s=[],n=0,i=e.length;n<i;n++)l=e[n],l.children.length?(r=J(l.children,t),(r.length||t[l.id])&&(o=ie(l),o.children=r,s.push(o))):t[l.id]&&s.push(l);return s},De=f.prototype.getSegCustomClasses,xe=f.prototype.getSegDefaultBackgroundColor,Ge=f.prototype.getSegDefaultBorderColor,Be=f.prototype.getSegDefaultTextColor,f.prototype.getSegCustomClasses=function(e){var t,r,o,s,n;for(t=De.apply(this,arguments),s=this.getSegResources(e),r=0,o=s.length;r<o;r++)n=s[r],t=t.concat(n.eventClassName||[]);return t},f.prototype.getSegDefaultBackgroundColor=function(e){var t,r,o,s,n;for(s=this.getSegResources(e),r=0,o=s.length;r<o;r++)for(t=s[r];t;){if(n=t.eventBackgroundColor||t.eventColor)return n;t=t._parent}return xe.apply(this,arguments)},f.prototype.getSegDefaultBorderColor=function(e){var t,r,o,s,n;for(s=this.getSegResources(e),r=0,o=s.length;r<o;r++)for(t=s[r];t;){if(n=t.eventBorderColor||t.eventColor)return n;t=t._parent}return Ge.apply(this,arguments)},f.prototype.getSegDefaultTextColor=function(e){var t,r,o,s,n;for(s=this.getSegResources(e),r=0,o=s.length;r<o;r++)for(t=s[r];t;){if(n=t.eventTextColor)return n;t=t._parent}return Be.apply(this,arguments)},f.prototype.getSegResources=function(e){return e.resource?[e.resource]:this.view.calendar.getEventResources(e.event)},x=function(t){function r(e){this.calendar=e,this.initializeCache()}return Ue(r,t),r.mixin(c),r.resourceGuid=1,r.ajaxDefaults={dataType:"json",cache:!1},r.prototype.calendar=null,r.prototype.fetchId=0,r.prototype.topLevelResources=null,r.prototype.resourcesById=null,r.prototype.fetching=null,r.prototype.currentStart=null,r.prototype.currentEnd=null,r.prototype.getResources=function(e,t){var r;return r=!e&&!this.currentStart||e&&this.currentStart&&e.isSame(this.currentStart)&&t.isSame(this.currentEnd),this.fetching&&r?this.fetching:this.fetchResources(e,t)},r.prototype.fetchResources=function(e,t){var r;return r=this.fetchId+=1,this.fetching=C.construct(function(o){return function(s,n){return o.fetchResourceInputs(function(e){return r===o.fetchId?(o.setResources(e),s(o.topLevelResources)):n()},e,t)}}(this))},r.prototype.fetchResourceInputs=function(t,o,s){var n,i,l,u;switch(n=this.calendar,l=n.opt("resources"),u=n.opt("timezone"),"string"===e.type(l)&&(l={url:l}),e.type(l)){case"function":return this.calendar.pushLoading(),l(function(e){return function(r){return e.calendar.popLoading(),t(r)}}(this),o,s,n.opt("timezone"));case"object":return n.pushLoading(),i={},o&&s&&(i[n.opt("startParam")]=o.format(),i[n.opt("endParam")]=s.format(),u&&"local"!==u&&(i[n.opt("timezoneParam")]=u)),e.ajax(e.extend({data:i},r.ajaxDefaults,l)).then(function(e){return function(e){return n.popLoading(),t(e)}}(this));case"array":return t(l);default:return t([])}},r.prototype.getResourceById=function(e){return this.resourcesById[e]},r.prototype.getFlatResources=function(){var e,t;t=[];for(e in this.resourcesById)t.push(this.resourcesById[e]);return t},r.prototype.initializeCache=function(){return this.topLevelResources=[],this.resourcesById={}},r.prototype.setResources=function(e){var t,r,o,s,n,i,l;for(l=Boolean(this.topLevelResources),this.initializeCache(),n=function(){var t,r,o;for(o=[],t=0,r=e.length;t<r;t++)s=e[t],o.push(this.buildResource(s));return o}.call(this),i=function(){var e,t,r;for(r=[],e=0,t=n.length;e<t;e++)o=n[e],this.addResourceToIndex(o)&&r.push(o);return r}.call(this),t=0,r=i.length;t<r;t++)o=i[t],this.addResourceToTree(o);return l?this.trigger("reset",this.topLevelResources):this.trigger("set",this.topLevelResources),this.calendar.publiclyTrigger("resourcesSet",null,this.topLevelResources)},r.prototype.resetCurrentResources=function(){if(this.topLevelResources)return this.trigger("reset",this.topLevelResources)},r.prototype.clear=function(){return this.topLevelResources=null,this.fetching=null},r.prototype.addResource=function(e){return this.fetching?this.fetching.then(function(t){return function(){var r;return r=t.buildResource(e),!!t.addResourceToIndex(r)&&(t.addResourceToTree(r),t.trigger("add",r,t.topLevelResources),r)}}(this)):C.reject()},r.prototype.addResourceToIndex=function(e){var t,r,o,s;if(this.resourcesById[e.id])return!1;for(this.resourcesById[e.id]=e,s=e.children,r=0,o=s.length;r<o;r++)t=s[r],this.addResourceToIndex(t);return!0},r.prototype.addResourceToTree=function(e){var t,r,o,s;if(!e.parent){if(r=String(null!=(o=e.parentId)?o:"")){if(t=this.resourcesById[r],!t)return!1;e.parent=t,s=t.children}else s=this.topLevelResources;s.push(e)}return!0},r.prototype.removeResource=function(e){var t;return t="object"==typeof e?e.id:e,this.fetching?this.fetching.then(function(e){return function(){var r;return r=e.removeResourceFromIndex(t),r&&(e.removeResourceFromTree(r),e.trigger("remove",r,e.topLevelResources)),r}}(this)):C.reject()},r.prototype.removeResourceFromIndex=function(e){var t,r,o,s,n;if(n=this.resourcesById[e]){for(delete this.resourcesById[e],s=n.children,r=0,o=s.length;r<o;r++)t=s[r],this.removeResourceFromIndex(t.id);return n}return!1},r.prototype.removeResourceFromTree=function(e,t){var r,o,s,n;for(null==t&&(t=this.topLevelResources),r=o=0,s=t.length;o<s;r=++o){if(n=t[r],n===e)return e.parent=null,t.splice(r,1),!0;if(this.removeResourceFromTree(e,n.children))return!0}return!1},r.prototype.buildResource=function(t){var o,s,n,i,l;return l=e.extend({},t),l.id=String(null!=(i=t.id)?i:"_fc"+r.resourceGuid++),n=t.eventClassName,l.eventClassName=function(){switch(e.type(n)){case"string":return n.split(/\s+/);case"array":return n;default:return[]}}(),l.children=function(){var e,r,n,i,u;for(i=null!=(n=t.children)?n:[],u=[],e=0,r=i.length;e<r;e++)s=i[e],o=this.buildResource(s),o.parent=l,u.push(o);return u}.call(this),l},r}(n),o.defaults.filterResourcesWithEvents=!1,L={isResourcesRendered:!1,resourceTextFunc:null,setElement:function(){return K.prototype.setElement.apply(this,arguments),this.watch("displayingResources",["hasResources"],function(e){return function(){return e.requestResourcesRender(e.get("currentResources"))}}(this),function(e){return function(){return e.requestResourcesUnrender()}}(this)),this.watch("displayingEvents",["displayingDates","hasEvents","displayingResources"],function(e){return function(){return e.requestEventsRender(e.get("currentEvents"))}}(this),function(e){return function(){return e.requestEventsUnrender()}}(this))},queryScroll:function(){var t;return t=K.prototype.queryScroll.apply(this,arguments),this.isResourcesRendered&&e.extend(t,this.queryResourceScroll()),t},applyScroll:function(e){if(K.prototype.applyScroll.apply(this,arguments),this.isResourcesRendered)return this.applyResourceScroll(e)},queryResourceScroll:function(){return{}},applyResourceScroll:function(){},bindBaseRenderHandlers:function(){var e,t;return t=!1,e=!1,this.on("resourcesRendered.baseHandler",function(){if(!t&&(t=!0,e))return this.onBaseRender()}),this.on("datesRendered.baseHandler",function(){if(!e&&(e=!0,t))return this.onBaseRender()}),this.on("before:resourcesUnrendered.baseHandler",function(){if(t)return t=!1}),this.on("before:datesUnrendered.baseHandler",function(){if(e)return e=!1,this.onBeforeBaseUnrender()})},handleResourcesSet:function(e){},handleResourcesUnset:function(){},handleResourceAdd:function(e){return this.requestResourceRender(e)},handleResourceRemove:function(e){return this.requestResourceUnrender(e)},requestResourcesRender:function(e){return this.renderQueue.queue(function(t){return function(){return t.executeResourcesRender(e)}}(this),"resource","init")},requestResourcesUnrender:function(){return this.renderQueue.queue(function(e){return function(){return e.executeResourcesUnrender()}}(this),"resource","destroy")},requestResourceRender:function(e){return this.renderQueue.queue(function(t){return function(){return t.executeResourceRender(e)}}(this),"resource","add")},requestResourceUnrender:function(e){return this.renderQueue.queue(function(t){return function(){return t.executeResourceUnrender(e)}}(this),"resource","remove")},executeResourcesRender:function(e){return this.renderResources(e),
this.isResourcesRendered=!0,this.trigger("resourcesRendered")},executeResourcesUnrender:function(){return this.trigger("before:resourcesUnrendered"),this.unrenderResources(),this.isResourcesRendered=!1},executeResourceRender:function(e){return this.renderResource(e)},executeResourceUnrender:function(e){return this.unrenderResource(e)},renderResources:function(e){},unrenderResources:function(){},renderResource:function(e){},unrenderResource:function(e){},isEventDraggable:function(e){return this.isEventResourceEditable(e)||K.prototype.isEventDraggable.call(this,e)},isEventResourceEditable:function(e){var t,r,o;return null!=(t=null!=(r=null!=(o=e.resourceEditable)?o:(e.source||{}).resourceEditable)?r:this.opt("eventResourceEditable"))?t:this.isEventGenerallyEditable(e)},getResourceText:function(e){return this.getResourceTextFunc()(e)},getResourceTextFunc:function(){var e;return this.resourceTextFunc?this.resourceTextFunc:(e=this.opt("resourceText"),"function"!=typeof e&&(e=function(e){return e.title||e.id}),this.resourceTextFunc=e)},triggerDayClick:function(e,t,r){var o;return o=this.calendar.resourceManager,this.publiclyTrigger("dayClick",t,this.calendar.applyTimezone(e.start),r,this,o.getResourceById(e.resourceId))},triggerSelect:function(e,t){var r;return r=this.calendar.resourceManager,this.publiclyTrigger("select",null,this.calendar.applyTimezone(e.start),this.calendar.applyTimezone(e.end),t,this,r.getResourceById(e.resourceId))},triggerExternalDrop:function(e,t,r,o,s){if(this.publiclyTrigger("drop",r[0],t.start,o,s,t.resourceId),e)return this.publiclyTrigger("eventReceive",null,e)},reportExternalDrop:function(){var e,t,r,o;return t=arguments[0],e=arguments[1],r=3<=arguments.length?Qe.call(arguments,2):[],e=this.normalizeDropLocation(e),(o=K.prototype.reportExternalDrop).call.apply(o,[this,t,e].concat(Qe.call(r)))},normalizeDropLocation:function(t){var r;return r=e.extend({},t),delete r.resourceId,this.calendar.setEventResourceId(r,t.resourceId),r}},$=e.extend({},L,{setElement:function(){var e,t,r;return L.setElement.apply(this,arguments),t=!1,e=!1,r=!1,this.watch("dateProfileOnly",["dateProfile"],function(e){return function(){return r=!0}}(this)),this.watch("displayingDatesOnly",["dateProfile","?currentResources"],function(e){return function(o){if(!o.currentResources&&!e.isDestroying)return t=!0,e.renderQueue.queue(function(){return e.executeDateRender(o.dateProfile,!r),r=!1},"date","init")}}(this),function(e){return function(){if(t)return t=!1,e.renderQueue.queue(function(){return e.executeDateUnrender()},"date","destroy")}}(this)),this.watch("displayingDates",["dateProfile","currentResources"],function(t){return function(o){if(!t.isDestroying)return e=!0,t.renderQueue.queue(function(){return t.setResourcesOnGrids(o.currentResources),t.executeDateRender(o.dateProfile,!r),r=!1,t.trigger("resourcesRendered")},"resource","init")}}(this),function(t){return function(){if(e)return e=!1,t.renderQueue.queue(function(){return t.trigger("before:resourcesUnrendered"),t.unsetResourcesOnGrids(),t.executeDateUnrender()},"resource","destroy")}}(this)),this.watch("displayingResources",["displayingDates"],function(e){return function(){return!0}}(this))},setResourcesOnGrids:function(e){},unsetResourcesOnGrids:function(){}}),D={allowCrossResource:!0,eventRangeToSpans:function(t,r){var o,s,n,i,l;if(i=this.view.calendar.getEventResourceIds(r),i.length){for(l=[],o=0,s=i.length;o<s;o++)n=i[o],l.push(e.extend({},t,{resourceId:n}));return l}return d.isBgEvent(r)?f.prototype.eventRangeToSpans.apply(this,arguments):[]},fabricateHelperEvent:function(e,t){var r;return r=f.prototype.fabricateHelperEvent.apply(this,arguments),this.view.calendar.setEventResourceId(r,e.resourceId),r},computeEventDrop:function(e,t,r){var o;return o=this.view.isEventStartEditable(r)?f.prototype.computeEventDrop.apply(this,arguments):d.pluckEventDateProps(r),o&&(this.view.isEventResourceEditable(r)?o.resourceId=t.resourceId:o.resourceId=e.resourceId),o},computeExternalDrop:function(e,t){var r;return r=f.prototype.computeExternalDrop.apply(this,arguments),r&&(r.resourceId=e.resourceId),r},computeEventResize:function(e,t,r,o){var s;if(this.allowCrossResource||t.resourceId===r.resourceId)return s=f.prototype.computeEventResize.apply(this,arguments),s&&(s.resourceId=t.resourceId),s},computeSelectionSpan:function(e,t){var r;if(this.allowCrossResource||e.resourceId===t.resourceId)return r=f.prototype.computeSelectionSpan.apply(this,arguments),r&&(r.resourceId=e.resourceId),r}},I={flattenedResources:null,resourceCnt:0,datesAboveResources:!1,allowCrossResource:!1,setResources:function(e){return this.flattenedResources=this.flattenResources(e),this.resourceCnt=this.flattenedResources.length,this.updateDayTableCols()},unsetResources:function(){return this.flattenedResources=null,this.resourceCnt=0,this.updateDayTableCols()},flattenResources:function(e){var t,r,o,s;return r=this.view.opt("resourceOrder"),r?(t=Le(r),s=function(e,r){return te(e,r,t)}):s=null,o=[],this.accumulateResources(e,s,o),o},accumulateResources:function(e,t,r){var o,s,n,i,l;for(t?(l=e.slice(0),l.sort(t)):l=e,i=[],o=0,s=l.length;o<s;o++)n=l[o],r.push(n),i.push(this.accumulateResources(n.children,t,r));return i},updateDayTableCols:function(){return this.datesAboveResources=this.view.opt("groupByDateAndResource"),d.DayTableMixin.updateDayTableCols.call(this)},computeColCnt:function(){return(this.resourceCnt||1)*this.daysPerRow},getColDayIndex:function(e){return this.isRTL&&(e=this.colCnt-1-e),this.datesAboveResources?Math.floor(e/(this.resourceCnt||1)):e%this.daysPerRow},getColResource:function(e){return this.flattenedResources[this.getColResourceIndex(e)]},getColResourceIndex:function(e){return this.isRTL&&(e=this.colCnt-1-e),this.datesAboveResources?e%(this.resourceCnt||1):Math.floor(e/this.daysPerRow)},indicesToCol:function(e,t){var r;return r=this.datesAboveResources?t*(this.resourceCnt||1)+e:e*this.daysPerRow+t,this.isRTL&&(r=this.colCnt-1-r),r},renderHeadTrHtml:function(){return this.resourceCnt?this.daysPerRow>1?this.datesAboveResources?this.renderHeadDateAndResourceHtml():this.renderHeadResourceAndDateHtml():this.renderHeadResourceHtml():d.DayTableMixin.renderHeadTrHtml.call(this)},renderHeadResourceHtml:function(){var e,t,r,o,s;for(s=[],r=this.flattenedResources,e=0,t=r.length;e<t;e++)o=r[e],s.push(this.renderHeadResourceCellHtml(o));return this.wrapTr(s,"renderHeadIntroHtml")},renderHeadResourceAndDateHtml:function(){var e,t,r,o,s,n,i,l,u,h;for(h=[],t=[],i=this.flattenedResources,o=0,n=i.length;o<n;o++)for(u=i[o],h.push(this.renderHeadResourceCellHtml(u,null,this.daysPerRow)),r=s=0,l=this.daysPerRow;s<l;r=s+=1)e=this.dayDates[r].clone(),t.push(this.renderHeadResourceDateCellHtml(e,u));return this.wrapTr(h,"renderHeadIntroHtml")+this.wrapTr(t,"renderHeadIntroHtml")},renderHeadDateAndResourceHtml:function(){var e,t,r,o,s,n,i,l,u,h;for(t=[],h=[],r=o=0,i=this.daysPerRow;o<i;r=o+=1)for(e=this.dayDates[r].clone(),t.push(this.renderHeadDateCellHtml(e,this.resourceCnt)),l=this.flattenedResources,s=0,n=l.length;s<n;s++)u=l[s],h.push(this.renderHeadResourceCellHtml(u,e));return this.wrapTr(t,"renderHeadIntroHtml")+this.wrapTr(h,"renderHeadIntroHtml")},renderHeadResourceCellHtml:function(e,t,r){return'<th class="fc-resource-cell" data-resource-id="'+e.id+'"'+(t?' data-date="'+t.format("YYYY-MM-DD")+'"':"")+(r>1?' colspan="'+r+'"':"")+">"+Se(this.view.getResourceText(e))+"</th>"},renderHeadResourceDateCellHtml:function(e,t,r){return this.renderHeadDateCellHtml(e,r,'data-resource-id="'+t.id+'"')},processHeadResourceEls:function(t){return t.find(".fc-resource-cell").each(function(t){return function(r,o){var s;return s=t.datesAboveResources?t.getColResource(r):t.flattenedResources[t.isRTL?t.flattenedResources.length-1-r:r],t.view.publiclyTrigger("resourceRender",s,s,e(o),e())}}(this))},renderBgCellsHtml:function(e){var t,r,o,s,n,i;if(this.resourceCnt){for(o=[],t=s=0,n=this.colCnt;s<n;t=s+=1)r=this.getCellDate(e,t),i=this.getColResource(t),o.push(this.renderResourceBgCellHtml(r,i));return o.join("")}return d.DayTableMixin.renderBgCellsHtml.call(this,e)},renderResourceBgCellHtml:function(e,t){return this.renderBgCellHtml(e,'data-resource-id="'+t.id+'"')},wrapTr:function(e,t){return this.isRTL?(e.reverse(),"<tr>"+e.join("")+this[t]()+"</tr>"):"<tr>"+this[t]()+e.join("")+"</tr>"},computePerResourceBusinessHourSegs:function(e){var t,r,o,s,n,i,l,u,h,c,a,p,d,f,g;if(this.flattenedResources){for(r=!1,p=this.flattenedResources,i=0,h=p.length;i<h;i++)f=p[i],f.businessHours&&(r=!0);if(r){for(t=[],d=this.flattenedResources,l=0,c=d.length;l<c;l++){for(f=d[l],o=f.businessHours||this.view.calendar.opt("businessHours"),n=this.buildBusinessHourEvents(e,o),u=0,a=n.length;u<a;u++)s=n[u],s.resourceId=f.id;g=this.eventsToSegs(n),t.push.apply(t,g)}return t}}return null}},_=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.mixin(D),r.mixin(I),r.prototype.getHitSpan=function(e){var t;return t=r.__super__.getHitSpan.apply(this,arguments),this.resourceCnt&&(t.resourceId=this.getColResource(e.col).id),t},r.prototype.spanToSegs=function(t){var r,o,s,n,i,l,u,h,c,a,p,d,f;if(c=this.resourceCnt,o=this.datesAboveResources?this.sliceRangeByDay(t):this.sliceRangeByRow(t),c){for(d=[],n=0,u=o.length;n<u;n++)for(f=o[n],a=i=0,h=c;i<h;a=i+=1)p=this.flattenedResources[a],t.resourceId&&t.resourceId!==p.id||(r=e.extend({},f),r.resource=p,this.isRTL?(r.leftCol=this.indicesToCol(a,f.lastRowDayIndex),r.rightCol=this.indicesToCol(a,f.firstRowDayIndex)):(r.leftCol=this.indicesToCol(a,f.firstRowDayIndex),r.rightCol=this.indicesToCol(a,f.lastRowDayIndex)),d.push(r));return d}for(s=0,l=o.length;s<l;s++)f=o[s],this.isRTL?(f.leftCol=f.lastRowDayIndex,f.rightCol=f.firstRowDayIndex):(f.leftCol=f.firstRowDayIndex,f.rightCol=f.lastRowDayIndex);return o},r.prototype.renderBusinessHours=function(){var e;return e=this.computePerResourceBusinessHourSegs(!0),e?this.renderFill("businessHours",e,"bgevent"):r.__super__.renderBusinessHours.apply(this,arguments)},r}(d.DayGrid),F=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.mixin(D),r.mixin(I),r.prototype.getHitSpan=function(e){var t;return t=r.__super__.getHitSpan.apply(this,arguments),this.resourceCnt&&(t.resourceId=this.getColResource(e.col).id),t},r.prototype.spanToSegs=function(t){var r,o,s,n,i,l,u,h,c,a,p,d,f;if(c=this.resourceCnt,o=this.sliceRangeByTimes(t),c){for(d=[],n=0,u=o.length;n<u;n++)for(f=o[n],a=i=0,h=c;i<h;a=i+=1)p=this.flattenedResources[a],t.resourceId&&t.resourceId!==p.id||(r=e.extend({},f),r.resource=p,r.col=this.indicesToCol(a,f.dayIndex),d.push(r));return d}for(s=0,l=o.length;s<l;s++)f=o[s],f.col=f.dayIndex;return o},r.prototype.renderBusinessHours=function(){var e;return e=this.computePerResourceBusinessHourSegs(!1),e?this.renderBusinessSegs(e):r.__super__.renderBusinessHours.apply(this,arguments)},r}(d.TimeGrid),Y=function(e){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,e),r.prototype.timeGrid=null,r.prototype.isScrolled=!1,r.prototype.usesMinMaxTime=!0,r.prototype.initialize=function(){return this.timeGrid=this.instantiateGrid()},r.prototype.instantiateGrid=function(){return new j(this)},r.prototype.setDateProfileForRendering=function(e){return r.__super__.setDateProfileForRendering.apply(this,arguments),this.timeGrid.initScaleProps(),this.timeGrid.setRange(this.renderRange)},r.prototype.getFallbackDuration=function(){return this.timeGrid.computeFallbackDuration()},r.prototype.renderSkeleton=function(){return this.el.addClass("fc-timeline"),this.opt("eventOverlap")===!1&&this.el.addClass("fc-no-overlap"),this.el.html(this.renderSkeletonHtml()),this.renderTimeGridSkeleton()},r.prototype.renderSkeletonHtml=function(){return'<table> <thead class="fc-head"> <tr> <td class="fc-time-area '+this.widgetHeaderClass+'"></td> </tr> </thead> <tbody class="fc-body"> <tr> <td class="fc-time-area '+this.widgetContentClass+'"></td> </tr> </tbody> </table>'},r.prototype.renderTimeGridSkeleton=function(){return this.timeGrid.setElement(this.el.find("tbody .fc-time-area")),this.timeGrid.headEl=this.el.find("thead .fc-time-area"),this.timeGrid.renderSkeleton(),this.isScrolled=!1,this.timeGrid.bodyScroller.on("scroll",ke(this,"handleBodyScroll"))},r.prototype.handleBodyScroll=function(e,t){if(e){if(!this.isScrolled)return this.isScrolled=!0,this.el.addClass("fc-scrolled")}else if(this.isScrolled)return this.isScrolled=!1,this.el.removeClass("fc-scrolled")},r.prototype.unrenderSkeleton=function(){return this.timeGrid.removeElement(),this.handleBodyScroll(0),r.__super__.unrenderSkeleton.apply(this,arguments)},r.prototype.renderDates=function(){return this.timeGrid.renderDates()},r.prototype.unrenderDates=function(){return this.timeGrid.unrenderDates()},r.prototype.renderBusinessHours=function(){return this.timeGrid.renderBusinessHours()},r.prototype.unrenderBusinessHours=function(){return this.timeGrid.unrenderBusinessHours()},r.prototype.getNowIndicatorUnit=function(){return this.timeGrid.getNowIndicatorUnit()},r.prototype.renderNowIndicator=function(e){return this.timeGrid.renderNowIndicator(e)},r.prototype.unrenderNowIndicator=function(){return this.timeGrid.unrenderNowIndicator()},r.prototype.hitsNeeded=function(){return this.timeGrid.hitsNeeded()},r.prototype.hitsNotNeeded=function(){return this.timeGrid.hitsNotNeeded()},r.prototype.prepareHits=function(){return this.timeGrid.prepareHits()},r.prototype.releaseHits=function(){return this.timeGrid.releaseHits()},r.prototype.queryHit=function(e,t){return this.timeGrid.queryHit(e,t)},r.prototype.getHitSpan=function(e){return this.timeGrid.getHitSpan(e)},r.prototype.getHitEl=function(e){return this.timeGrid.getHitEl(e)},r.prototype.updateWidth=function(){return this.timeGrid.updateWidth()},r.prototype.setHeight=function(e,t){var r;return r=t?"auto":e-this.timeGrid.headHeight()-this.queryMiscHeight(),this.timeGrid.bodyScroller.setHeight(r)},r.prototype.queryMiscHeight=function(){return this.el.outerHeight()-this.timeGrid.headScroller.el.outerHeight()-this.timeGrid.bodyScroller.el.outerHeight()},r.prototype.computeInitialDateScroll=function(){var e,r;return e=0,this.timeGrid.isTimeScale&&(r=this.opt("scrollTime"),r&&(r=t.duration(r),e=this.timeGrid.dateToCoord(this.activeRange.start.clone().time(r)))),{left:e}},r.prototype.queryDateScroll=function(){return{left:this.timeGrid.bodyScroller.getScrollLeft()}},r.prototype.applyDateScroll=function(e){if(null!=e.left)return this.timeGrid.headScroller.setScrollLeft(e.left),this.timeGrid.bodyScroller.setScrollLeft(e.left)},r.prototype.renderEvents=function(e){return this.timeGrid.renderEvents(e),this.updateWidth()},r.prototype.unrenderEvents=function(){return this.timeGrid.unrenderEvents(),this.updateWidth()},r.prototype.renderDrag=function(e,t){return this.timeGrid.renderDrag(e,t)},r.prototype.unrenderDrag=function(){return this.timeGrid.unrenderDrag()},r.prototype.getEventSegs=function(){return this.timeGrid.getEventSegs()},r.prototype.renderSelection=function(e){return this.timeGrid.renderSelection(e)},r.prototype.unrenderSelection=function(){return this.timeGrid.unrenderSelection()},r}(K),le=d.cssToStr,j=function(t){function r(){r.__super__.constructor.apply(this,arguments),this.slotWidth=this.opt("slotWidth")}return Ue(r,t),r.prototype.slotDates=null,r.prototype.slotCnt=null,r.prototype.snapCnt=null,r.prototype.snapsPerSlot=null,r.prototype.snapDiffToIndex=null,r.prototype.snapIndexToDiff=null,r.prototype.headEl=null,r.prototype.slatContainerEl=null,r.prototype.slatEls=null,r.prototype.containerCoordCache=null,r.prototype.slatCoordCache=null,r.prototype.slatInnerCoordCache=null,r.prototype.headScroller=null,r.prototype.bodyScroller=null,r.prototype.joiner=null,r.prototype.follower=null,r.prototype.eventTitleFollower=null,r.prototype.timeWindowMs=null,r.prototype.slotDuration=null,r.prototype.snapDuration=null,r.prototype.duration=null,r.prototype.labelInterval=null,r.prototype.headerFormats=null,r.prototype.isTimeScale=null,r.prototype.largeUnit=null,r.prototype.emphasizeWeeks=!1,r.prototype.titleFollower=null,r.prototype.segContainerEl=null,r.prototype.segContainerHeight=null,r.prototype.bgSegContainerEl=null,r.prototype.helperEls=null,r.prototype.innerEl=null,r.prototype.opt=function(e){return this.view.opt(e)},r.prototype.isValidDate=function(e){var t;return!this.view.isHiddenDay(e)&&(!this.isTimeScale||(t=e.time()-this.view.minTime,t=(t%864e5+864e5)%864e5,t<this.timeWindowMs))},r.prototype.computeDisplayEventTime=function(){return!this.isTimeScale},r.prototype.computeDisplayEventEnd=function(){return!1},r.prototype.computeEventTimeFormat=function(){return this.opt("extraSmallTimeFormat")},r.prototype.normalizeGridDate=function(e){var t;return this.isTimeScale?(t=e.clone(),t.hasTime()||t.time(0)):(t=e.clone().stripTime(),this.largeUnit&&t.startOf(this.largeUnit)),t},r.prototype.normalizeGridRange=function(e){var t,r;return this.isTimeScale?r={start:this.normalizeGridDate(e.start),end:this.normalizeGridDate(e.end)}:(r=this.view.computeDayRange(e),this.largeUnit&&(r.start.startOf(this.largeUnit),t=r.end.clone().startOf(this.largeUnit),t.isSame(r.end)&&t.isAfter(r.start)||t.add(this.slotDuration),r.end=t)),r},r.prototype.rangeUpdated=function(){var e,t;for(this.start=this.normalizeGridDate(this.start),this.end=this.normalizeGridDate(this.end),this.timeWindowMs=this.view.maxTime-this.view.minTime,this.isTimeScale&&(this.start.add(this.view.minTime),this.end.subtract(1,"day").add(this.view.maxTime)),t=[],e=this.start.clone();e<this.end;)this.isValidDate(e)&&t.push(e.clone()),e.add(this.slotDuration);return this.slotDates=t,this.updateGridDates()},r.prototype.updateGridDates=function(){var e,t,r,o,s;for(o=-1,t=0,r=[],s=[],e=this.start.clone();e<this.end;)this.isValidDate(e)?(o++,r.push(o),s.push(t)):r.push(o+.5),e.add(this.snapDuration),t++;return this.snapDiffToIndex=r,this.snapIndexToDiff=s,this.snapCnt=o+1,this.slotCnt=this.snapCnt/this.snapsPerSlot},r.prototype.spanToSegs=function(e){var t,r;return t=this.normalizeGridRange(e),this.computeDateSnapCoverage(e.start)<this.computeDateSnapCoverage(e.end)&&(r=be(t,this))?(r.isStart&&!this.isValidDate(r.start)&&(r.isStart=!1),r.isEnd&&r.end&&!this.isValidDate(r.end.clone().subtract(1))&&(r.isEnd=!1),[r]):[]},r.prototype.prepareHits=function(){return this.buildCoords()},r.prototype.queryHit=function(e,t){var r,o,s,n,i,l,u,h,c,a,p,d;if(d=this.snapsPerSlot,n=this.slatCoordCache,r=this.containerCoordCache,r.isPointInBounds(e,t)&&(i=n.getHorizontalIndex(e),null!=i))return h=n.getWidth(i),this.isRTL?(u=n.getRightOffset(i),s=(u-e)/h,o=Math.floor(s*d),c=i*d+o,p=u-o/d*h,a=p-(o+1)/d*h):(l=n.getLeftOffset(i),s=(e-l)/h,o=Math.floor(s*d),c=i*d+o,a=l+o/d*h,p=l+(o+1)/d*h),{snap:c,component:this,left:a,right:p,top:r.getTopOffset(0),bottom:r.getBottomOffset(0)}},r.prototype.getHitSpan=function(e){return this.getSnapRange(e.snap)},r.prototype.getHitEl=function(e){return this.getSnapEl(e.snap)},r.prototype.getSnapRange=function(e){var t,r;return r=this.start.clone(),r.add(Ie(this.snapDuration,this.snapIndexToDiff[e])),t=r.clone().add(this.snapDuration),{start:r,end:t}},r.prototype.getSnapEl=function(e){return this.slatEls.eq(Math.floor(e/this.snapsPerSlot))},r.prototype.renderSkeleton=function(){return this.headScroller=new i({overflowX:"clipped-scroll",overflowY:"hidden"}),this.headScroller.canvas=new N,this.headScroller.render(),this.headEl.append(this.headScroller.el),this.bodyScroller=new i,this.bodyScroller.canvas=new N,this.bodyScroller.render(),this.el.append(this.bodyScroller.el),this.innerEl=this.bodyScroller.canvas.contentEl,this.slatContainerEl=e('<div class="fc-slats"/>').appendTo(this.bodyScroller.canvas.bgEl),this.segContainerEl=e('<div class="fc-event-container"/>').appendTo(this.bodyScroller.canvas.contentEl),this.bgSegContainerEl=this.bodyScroller.canvas.bgEl,this.containerCoordCache=new l({els:this.bodyScroller.canvas.el,isHorizontal:!0,isVertical:!0}),this.joiner=new q("horizontal",[this.headScroller,this.bodyScroller]),this.follower=new O(this.headScroller,(!0)),this.eventTitleFollower=new O(this.bodyScroller),this.eventTitleFollower.minTravel=50,this.isRTL?this.eventTitleFollower.containOnNaturalRight=!0:this.eventTitleFollower.containOnNaturalLeft=!0,r.__super__.renderSkeleton.apply(this,arguments)},r.prototype.headColEls=null,r.prototype.slatColEls=null,r.prototype.renderDates=function(){var e,t,r,o,s;for(this.headScroller.canvas.contentEl.html(this.renderHeadHtml()),this.headColEls=this.headScroller.canvas.contentEl.find("col"),this.slatContainerEl.html(this.renderSlatHtml()),this.slatColEls=this.slatContainerEl.find("col"),this.slatEls=this.slatContainerEl.find("td"),this.slatCoordCache=new l({els:this.slatEls,isHorizontal:!0}),this.slatInnerCoordCache=new l({els:this.slatEls.find("> div"),isHorizontal:!0,offsetParent:this.bodyScroller.canvas.el}),s=this.slotDates,t=r=0,o=s.length;r<o;t=++r)e=s[t],this.view.publiclyTrigger("dayRender",null,e,this.slatEls.eq(t));if(this.follower)return this.follower.setSprites(this.headEl.find("tr:not(:last-child) .fc-cell-text"))},r.prototype.unrenderDates=function(){return this.follower&&this.follower.clearSprites(),this.headScroller.canvas.contentEl.empty(),this.slatContainerEl.empty(),this.headScroller.canvas.clearWidth(),this.bodyScroller.canvas.clearWidth()},r.prototype.renderHeadHtml=function(){var e,t,r,o,s,n,i,l,u,h,c,a,p,f,g,y,v,R,w,m,S,b,C,E,H,T,_,I,D,x,G,B,F,W,M,L,z,k,P;for(v=this.labelInterval,s=this.headerFormats,t=function(){var e,t,r;for(r=[],e=0,t=s.length;e<t;e++)o=s[e],r.push([]);return r}(),R=null,x=null,z=this.slotDates,L=[],W=function(){var e,t,r;for(r=[],e=0,t=s.length;e<t;e++)o=s[e],r.push(d.queryMostGranularFormatUnit(o));return r}(),f=0,w=z.length;f<w;f++){for(r=z[f],P=r.week(),p=this.emphasizeWeeks&&null!==x&&x!==P,B=g=0,m=s.length;g<m;B=++g)o=s[B],F=t[B],R=F[F.length-1],a=s.length>1&&B<s.length-1,I=null,a?(k=r.format(o),R&&R.text===k?R.colspan+=1:I=this.buildCellObject(r,k,W[B])):!R||He(ae(this.start,r,v))?(k=r.format(o),I=this.buildCellObject(r,k,W[B])):R.colspan+=1,I&&(I.weekStart=p,F.push(I));L.push({weekStart:p}),x=P}for(u=v>this.slotDuration,c=1===this.slotDuration.as("days"),i="<table>",i+="<colgroup>",y=0,S=z.length;y<S;y++)r=z[y],i+="<col/>";for(i+="</colgroup>",i+="<tbody>",l=T=0,b=t.length;T<b;l=++T){for(F=t[l],h=l===t.length-1,i+="<tr"+(u&&h?' class="fc-chrono"':"")+">",_=0,C=F.length;_<C;_++)e=F[_],n=[this.view.widgetHeaderClass],e.weekStart&&n.push("fc-em-cell"),c&&(n=n.concat(this.getDayClasses(e.date,!0))),i+='<th class="'+n.join(" ")+'" data-date="'+e.date.format()+'"'+(e.colspan>1?' colspan="'+e.colspan+'"':"")+'><div class="fc-cell-content">'+e.spanHtml+"</div></th>";i+="</tr>"}for(i+="</tbody></table>",M="<table>",M+="<colgroup>",D=0,E=L.length;D<E;D++)e=L[D],M+="<col/>";for(M+="</colgroup>",M+="<tbody><tr>",l=G=0,H=L.length;G<H;l=++G)e=L[l],r=z[l],M+=this.slatCellHtml(r,e.weekStart);return M+="</tr></tbody></table>",this._slatHtml=M,i},r.prototype.buildCellObject=function(e,t,r){var o;return e=e.clone(),o=this.view.buildGotoAnchorHtml({date:e,type:r,forceOff:!r},{"class":"fc-cell-text"},Se(t)),{text:t,spanHtml:o,date:e,colspan:1}},r.prototype.renderSlatHtml=function(){return this._slatHtml},r.prototype.slatCellHtml=function(e,t){var r;return this.isTimeScale?(r=[],r.push(He(ae(this.start,e,this.labelInterval))?"fc-major":"fc-minor")):(r=this.getDayClasses(e),r.push("fc-day")),r.unshift(this.view.widgetContentClass),t&&r.push("fc-em-cell"),'<td class="'+r.join(" ")+'" data-date="'+e.format()+'"><div /></td>'},r.prototype.businessHourSegs=null,r.prototype.renderBusinessHours=function(){var e;if(!this.largeUnit)return e=this.businessHourSegs=this.buildBusinessHourSegs(!this.isTimeScale),this.renderFill("businessHours",e,"bgevent")},r.prototype.unrenderBusinessHours=function(){return this.unrenderFill("businessHours")},r.prototype.nowIndicatorEls=null,r.prototype.getNowIndicatorUnit=function(){if(this.isTimeScale)return re(this.slotDuration)},r.prototype.renderNowIndicator=function(t){var r,o,s;return s=[],t=this.normalizeGridDate(t),t>=this.start&&t<this.end&&(r=this.dateToCoord(t),o=this.isRTL?{right:-r}:{left:r},s.push(e("<div class='fc-now-indicator fc-now-indicator-arrow'></div>").css(o).appendTo(this.headScroller.canvas.el)[0]),s.push(e("<div class='fc-now-indicator fc-now-indicator-line'></div>").css(o).appendTo(this.bodyScroller.canvas.el)[0])),this.nowIndicatorEls=e(s)},r.prototype.unrenderNowIndicator=function(){if(this.nowIndicatorEls)return this.nowIndicatorEls.remove(),this.nowIndicatorEls=null},r.prototype.explicitSlotWidth=null,r.prototype.defaultSlotWidth=null,r.prototype.updateWidth=function(){var e,t,r,o,s,n;if(o=this.headColEls,o?(n=Math.round(this.slotWidth||(this.slotWidth=this.computeSlotWidth())),r=n*this.slotDates.length,t="",s=n,e=this.bodyScroller.getClientWidth(),e>r&&(t=e,r="",s=Math.floor(e/this.slotDates.length))):(r="",t=""),this.headScroller.canvas.setWidth(r),this.headScroller.canvas.setMinWidth(t),this.bodyScroller.canvas.setWidth(r),this.bodyScroller.canvas.setMinWidth(t),o&&this.headColEls.slice(0,-1).add(this.slatColEls.slice(0,-1)).width(s),this.headScroller.updateSize(),this.bodyScroller.updateSize(),this.joiner.update(),o&&(this.buildCoords(),this.updateSegPositions(),this.view.updateNowIndicator()),this.follower&&this.follower.update(),this.eventTitleFollower)return this.eventTitleFollower.update()},r.prototype.computeSlotWidth=function(){var t,r,o,s,n,i;return o=0,r=this.headEl.find("tr:last-child th .fc-cell-text"),r.each(function(t,r){var s;return s=e(r).outerWidth(),o=Math.max(o,s)}),t=o+1,i=ce(this.labelInterval,this.slotDuration),n=Math.ceil(t/i),s=this.headColEls.eq(0).css("min-width"),s&&(s=parseInt(s,10),s&&(n=Math.max(n,s))),n},r.prototype.buildCoords=function(){return this.containerCoordCache.build(),this.slatCoordCache.build(),this.slatInnerCoordCache.build()},r.prototype.computeDateSnapCoverage=function(e){var t,r,o;return r=ae(this.start,e,this.snapDuration),r<0?0:r>=this.snapDiffToIndex.length?this.snapCnt:(o=Math.floor(r),t=this.snapDiffToIndex[o],He(t)?t+=r-o:t=Math.ceil(t),t)},r.prototype.dateToCoord=function(e){var t,r,o,s,n;return n=this.computeDateSnapCoverage(e),o=n/this.snapsPerSlot,s=Math.floor(o),s=Math.min(s,this.slotCnt-1),r=o-s,t=this.slatInnerCoordCache,this.isRTL?t.getRightPosition(s)-t.getWidth(s)*r-this.containerCoordCache.getWidth(0):t.getLeftPosition(s)+t.getWidth(s)*r},r.prototype.rangeToCoords=function(e){return this.isRTL?{right:this.dateToCoord(e.start),left:this.dateToCoord(e.end)}:{left:this.dateToCoord(e.start),right:this.dateToCoord(e.end)}},r.prototype.headHeight=function(){var e;return e=this.headScroller.canvas.contentEl.find("table"),e.height.apply(e,arguments)},r.prototype.updateSegPositions=function(){var e,t,r,o,s;for(s=(this.segs||[]).concat(this.businessHourSegs||[]),t=0,r=s.length;t<r;t++)o=s[t],e=this.rangeToCoords(o),o.el.css({left:o.left=e.left,right:-(o.right=e.right)})},r.prototype.renderFgSegs=function(e){return e=this.renderFgSegEls(e),this.renderFgSegsInContainers([[this,e]]),this.updateSegFollowers(e),e},r.prototype.unrenderFgSegs=function(){return this.clearSegFollowers(),this.unrenderFgContainers([this])},r.prototype.renderFgSegsInContainers=function(e){var t,r,o,s,n,i,l,u,h,c,a,p,d,f,g,y,v,R,w,m,S,b,C,E,H;for(o=0,i=e.length;o<i;o++)for(w=e[o],t=w[0],H=w[1],s=0,l=H.length;s<l;s++)E=H[s],r=this.rangeToCoords(E),E.el.css({left:E.left=r.left,right:-(E.right=r.right)});for(n=0,u=e.length;n<u;n++)for(m=e[n],t=m[0],H=m[1],f=0,h=H.length;f<h;f++)E=H[f],E.el.appendTo(t.segContainerEl);for(g=0,c=e.length;g<c;g++){for(S=e[g],t=S[0],H=S[1],y=0,a=H.length;y<a;y++)E=H[y],E.height=E.el.outerHeight(!0);this.buildSegLevels(H),t.segContainerHeight=se(H)}for(C=[],v=0,p=e.length;v<p;v++){for(b=e[v],t=b[0],H=b[1],R=0,d=H.length;R<d;R++)E=H[R],E.el.css("top",E.top);C.push(t.segContainerEl.height(t.segContainerHeight))}return C},r.prototype.buildSegLevels=function(e){var t,r,o,s,n,i,l,u,h,c,a,p,d,f;for(d=[],this.sortEventSegs(e),o=0,i=e.length;o<i;o++){for(f=e[o],f.above=[],h=0;h<d.length;){for(r=!1,a=d[h],s=0,l=a.length;s<l;s++)c=a[s],Ne(f,c)&&(f.above.push(c),r=!0);if(!r)break;h+=1}for((d[h]||(d[h]=[])).push(f),h+=1;h<d.length;){for(p=d[h],n=0,u=p.length;n<u;n++)t=p[n],Ne(f,t)&&t.above.push(f);h+=1}}return d},r.prototype.unrenderFgContainers=function(e){var t,r,o,s;for(s=[],r=0,o=e.length;r<o;r++)t=e[r],t.segContainerEl.empty(),t.segContainerEl.height(""),s.push(t.segContainerHeight=null);return s},r.prototype.fgSegHtml=function(e,t){var r,o,s,n,i,l;return o=e.event,s=this.view.isEventDraggable(o),i=e.isStart&&this.view.isEventResizableFromStart(o),n=e.isEnd&&this.view.isEventResizableFromEnd(o),r=this.getSegClasses(e,s,i||n),r.unshift("fc-timeline-event","fc-h-event"),l=this.getEventTimeText(o),'<a class="'+r.join(" ")+'" style="'+le(this.getSegSkinCss(e))+'"'+(o.url?' href="'+Se(o.url)+'"':"")+'><div class="fc-content">'+(l?'<span class="fc-time">'+Se(l)+"</span>":"")+'<span class="fc-title">'+(o.title?Se(o.title):"&nbsp;")+'</span></div><div class="fc-bg" />'+(i?'<div class="fc-resizer fc-start-resizer"></div>':"")+(n?'<div class="fc-resizer fc-end-resizer"></div>':"")+"</a>"},r.prototype.updateSegFollowers=function(e){var t,r,o,s,n;if(this.eventTitleFollower){for(s=[],t=0,r=e.length;t<r;t++)o=e[t],n=o.el.find(".fc-title"),n.length&&s.push(new A(n));return this.eventTitleFollower.setSprites(s)}},r.prototype.clearSegFollowers=function(){if(this.eventTitleFollower)return this.eventTitleFollower.clearSprites()},r.prototype.segDragStart=function(){if(r.__super__.segDragStart.apply(this,arguments),this.eventTitleFollower)return this.eventTitleFollower.forceRelative()},r.prototype.segDragEnd=function(){if(r.__super__.segDragEnd.apply(this,arguments),this.eventTitleFollower)return this.eventTitleFollower.clearForce()},r.prototype.segResizeStart=function(){if(r.__super__.segResizeStart.apply(this,arguments),this.eventTitleFollower)return this.eventTitleFollower.forceRelative()},r.prototype.segResizeEnd=function(){if(r.__super__.segResizeEnd.apply(this,arguments),this.eventTitleFollower)return this.eventTitleFollower.clearForce()},r.prototype.renderHelper=function(e,t){var r;return r=this.eventToSegs(e),r=this.renderFgSegEls(r),this.renderHelperSegsInContainers([[this,r]],t)},r.prototype.renderHelperSegsInContainers=function(t,r){var o,s,n,i,l,u,h,c,a,p,d,f,g,y,v,R,w,m;for(i=[],w=[],l=0,c=t.length;l<c;l++)for(g=t[l],o=g[0],m=g[1],u=0,a=m.length;u<a;u++)R=m[u],s=this.rangeToCoords(R),R.el.css({left:R.left=s.left,right:-(R.right=s.right)}),r&&r.resourceId===(null!=(y=o.resource)?y.id:void 0)?R.el.css("top",r.el.css("top")):R.el.css("top",0);for(h=0,p=t.length;h<p;h++)for(v=t[h],o=v[0],m=v[1],n=e('<div class="fc-event-container fc-helper-container"/>').appendTo(o.innerEl),i.push(n[0]),f=0,d=m.length;f<d;f++)R=m[f],n.append(R.el),w.push(R.el[0]);return this.helperEls?this.helperEls=this.helperEls.add(e(i)):this.helperEls=e(i),e(w)},r.prototype.unrenderHelper=function(){if(this.helperEls)return this.helperEls.remove(),this.helperEls=null},r.prototype.renderEventResize=function(e,t){var r,o,s,n;for(o=this.eventToSpans(e),s=0,n=o.length;s<n;s++)r=o[s],this.renderHighlight(r);return this.renderEventLocationHelper(e,t)},r.prototype.unrenderEventResize=function(){return this.unrenderHighlight(),this.unrenderHelper()},r.prototype.renderFill=function(e,t,r){return t=this.renderFillSegEls(e,t),this.renderFillInContainers(e,[[this,t]],r),t},r.prototype.renderFillInContainers=function(e,t,r){var o,s,n,i,l,u;for(l=[],s=0,n=t.length;s<n;s++)i=t[s],o=i[0],u=i[1],l.push(this.renderFillInContainer(e,o,u,r));return l},r.prototype.renderFillInContainer=function(t,r,o,s){var n,i,l,u,h;if(o.length){for(s||(s=t.toLowerCase()),n=e('<div class="fc-'+s+'-container" />').appendTo(r.bgSegContainerEl),l=0,u=o.length;l<u;l++)h=o[l],i=this.rangeToCoords(h),h.el.css({left:h.left=i.left,right:-(h.right=i.right)
}),h.el.appendTo(n);return this.elsByFill[t]?this.elsByFill[t]=this.elsByFill[t].add(n):this.elsByFill[t]=n}},r.prototype.renderDrag=function(e,t){var r,o,s,n;if(t)return this.renderEventLocationHelper(e,t);for(o=this.eventToSpans(e),s=0,n=o.length;s<n;s++)r=o[s],this.renderHighlight(r);return null},r.prototype.unrenderDrag=function(){return this.unrenderHelper(),this.unrenderHighlight()},r}(f),se=function(e){var t,r,o,s;for(o=0,t=0,r=e.length;t<r;t++)s=e[t],o=Math.max(o,oe(s));return o},oe=function(e){return null==e.top&&(e.top=se(e.above)),e.top+e.height},Ne=function(e,t){return e.left<t.right&&e.right>t.left},S=18,w=6,R=200,m=1e3,u={months:1},P=[{years:1},{months:1},{days:1},{hours:1},{minutes:30},{minutes:15},{minutes:10},{minutes:5},{minutes:1},{seconds:30},{seconds:15},{seconds:10},{seconds:5},{seconds:1},{milliseconds:500},{milliseconds:100},{milliseconds:10},{milliseconds:1}],j.prototype.initScaleProps=function(){var r,o,s;return this.labelInterval=this.queryDurationOption("slotLabelInterval"),this.slotDuration=this.queryDurationOption("slotDuration"),this.validateLabelAndSlot(),this.ensureLabelInterval(),this.ensureSlotDuration(),r=this.opt("slotLabelFormat"),s=e.type(r),this.headerFormats="array"===s?r:"string"===s?[r]:this.computeHeaderFormats(),this.isTimeScale=pe(this.slotDuration),this.largeUnit=this.isTimeScale?void 0:(o=re(this.slotDuration),/year|month|week/.test(o)?o:void 0),this.emphasizeWeeks=1===this.slotDuration.as("days")&&this.view.currentRangeAs("weeks")>=2&&!this.opt("businessHours"),this.snapDuration=(r=this.opt("snapDuration"))?t.duration(r):this.slotDuration,this.minResizeDuration=this.snapDuration,this.snapsPerSlot=ce(this.slotDuration,this.snapDuration)},j.prototype.queryDurationOption=function(e){var r,o;if(o=this.opt(e),null!=o&&(r=t.duration(o),+r))return r},j.prototype.validateLabelAndSlot=function(){var e,t,r,o;if(e=this.view.currentRange,this.labelInterval&&(t=ae(e.start,e.end,this.labelInterval),t>m&&(d.warn("slotLabelInterval results in too many cells"),this.labelInterval=null)),this.slotDuration&&(r=ae(e.start,e.end,this.slotDuration),r>m&&(d.warn("slotDuration results in too many cells"),this.slotDuration=null)),this.labelInterval&&this.slotDuration&&(o=ce(this.labelInterval,this.slotDuration),!He(o)||o<1))return d.warn("slotLabelInterval must be a multiple of slotDuration"),this.slotDuration=null},j.prototype.computeFallbackDuration=function(){var e,r,o,s,n;if(e=null,this.labelInterval||this.slotDuration)for(n=this.ensureLabelInterval(),o=P.length-1;o>=0&&(r=P[o],e=t.duration(r),s=ce(e,n),!(s>=S));o+=-1);else e=t.duration(u);return e},j.prototype.ensureLabelInterval=function(){var e,r,o,s,n,i,l,u,h,c;if(e=this.view.currentRange,i=this.labelInterval,!i){if(this.slotDuration){for(o=0,l=P.length;o<l;o++)if(r=P[o],c=t.duration(r),h=ce(c,this.slotDuration),He(h)&&h<=w){i=c;break}i||(i=this.slotDuration)}else for(s=0,u=P.length;s<u&&(r=P[s],i=t.duration(r),n=ae(e.start,e.end,i),!(n>=S));s++);this.labelInterval=i}return i},j.prototype.ensureSlotDuration=function(){var e,r,o,s,n,i,l,u,h;if(e=this.view.currentRange,l=this.slotDuration,!l){for(s=this.ensureLabelInterval(),o=0,n=P.length;o<n;o++)if(r=P[o],h=t.duration(r),u=ce(s,h),He(u)&&u>1&&u<=w){l=h;break}l&&(i=ae(e.start,e.end,l),i>R&&(l=null)),l||(l=s),this.slotDuration=l}return l},j.prototype.computeHeaderFormats=function(){var e,t,r,o,s,n,i;switch(n=this.view,o=this.labelInterval,s=re(o),i=this.opt("weekNumbers"),e=t=r=null,"week"!==s||i||(s="day"),s){case"year":e="YYYY";break;case"month":n.currentRangeAs("years")>1&&(e="YYYY"),t="MMM";break;case"week":n.currentRangeAs("years")>1&&(e="YYYY"),t=this.opt("shortWeekFormat");break;case"day":n.currentRangeAs("years")>1?e=this.opt("monthYearFormat"):n.currentRangeAs("months")>1&&(e="MMMM"),i&&(t=this.opt("weekFormat")),r="dd D";break;case"hour":i&&(e=this.opt("weekFormat")),n.currentRangeAs("days")>1&&(t=this.opt("dayOfMonthFormat")),r=this.opt("smallTimeFormat");break;case"minute":o.asMinutes()/60>=w?(e=this.opt("hourFormat"),t="[:]mm"):e=this.opt("mediumTimeFormat");break;case"second":o.asSeconds()/60>=w?(e="LT",t="[:]ss"):e="LTS";break;case"millisecond":e="LTS",t="[.]SSS"}return[].concat(e||[],t||[],r||[])},d.views.timeline={"class":Y,defaults:{eventResizableFromStart:!0}},d.views.timelineDay={type:"timeline",duration:{days:1}},d.views.timelineWeek={type:"timeline",duration:{weeks:1}},d.views.timelineMonth={type:"timeline",duration:{months:1}},d.views.timelineYear={type:"timeline",duration:{years:1}},M=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.mixin(L),r.prototype.canHandleSpecificResources=!0,r.prototype.resourceGrid=null,r.prototype.tbodyHash=null,r.prototype.joiner=null,r.prototype.dividerEls=null,r.prototype.superHeaderText=null,r.prototype.isVGrouping=null,r.prototype.isHGrouping=null,r.prototype.groupSpecs=null,r.prototype.colSpecs=null,r.prototype.orderSpecs=null,r.prototype.rowHierarchy=null,r.prototype.resourceRowHash=null,r.prototype.nestingCnt=0,r.prototype.isNesting=null,r.prototype.dividerWidth=null,r.prototype.initialize=function(){return r.__super__.initialize.apply(this,arguments),this.processResourceOptions(),this.resourceGrid=new V(this),this.rowHierarchy=new k(this),this.resourceRowHash={}},r.prototype.instantiateGrid=function(){return new W(this)},r.prototype.processResourceOptions=function(){var e,t,r,o,s,n,i,l,u,h,c,a,p,d,f,g,y,v,R,w,m,S;for(e=this.opt("resourceColumns")||[],f=this.opt("resourceLabelText"),o="Resources",S=null,e.length?S=f:e.push({labelText:f||o,text:this.getResourceTextFunc()}),w=[],s=[],i=[],c=!1,h=!1,a=0,g=e.length;a<g;a++)r=e[a],r.group?s.push(r):w.push(r);for(w[0].isMain=!0,s.length?(i=s,c=!0):(l=this.opt("resourceGroupField"),l&&(h=!0,i.push({field:l,text:this.opt("resourceGroupText"),render:this.opt("resourceGroupRender")}))),t=Le(this.opt("resourceOrder")),m=[],p=0,y=t.length;p<y;p++){for(R=t[p],u=!1,d=0,v=i.length;d<v;d++)if(n=i[d],n.field===R.field){n.order=R.order,u=!0;break}u||m.push(R)}return this.superHeaderText=S,this.isVGrouping=c,this.isHGrouping=h,this.groupSpecs=i,this.colSpecs=s.concat(w),this.orderSpecs=m},r.prototype.renderSkeleton=function(){return r.__super__.renderSkeleton.apply(this,arguments),this.renderResourceGridSkeleton(),this.tbodyHash={spreadsheet:this.resourceGrid.tbodyEl,event:this.timeGrid.tbodyEl},this.joiner=new q("vertical",[this.resourceGrid.bodyScroller,this.timeGrid.bodyScroller]),this.initDividerMoving()},r.prototype.renderSkeletonHtml=function(){return'<table> <thead class="fc-head"> <tr> <td class="fc-resource-area '+this.widgetHeaderClass+'"></td> <td class="fc-divider fc-col-resizer '+this.widgetHeaderClass+'"></td> <td class="fc-time-area '+this.widgetHeaderClass+'"></td> </tr> </thead> <tbody class="fc-body"> <tr> <td class="fc-resource-area '+this.widgetContentClass+'"></td> <td class="fc-divider fc-col-resizer '+this.widgetHeaderClass+'"></td> <td class="fc-time-area '+this.widgetContentClass+'"></td> </tr> </tbody> </table>'},r.prototype.renderResourceGridSkeleton=function(){return this.resourceGrid.el=this.el.find("tbody .fc-resource-area"),this.resourceGrid.headEl=this.el.find("thead .fc-resource-area"),this.resourceGrid.renderSkeleton()},r.prototype.initDividerMoving=function(){var e;return this.dividerEls=this.el.find(".fc-divider"),this.dividerWidth=null!=(e=this.opt("resourceAreaWidth"))?e:this.resourceGrid.tableWidth,null!=this.dividerWidth&&this.positionDivider(this.dividerWidth),this.dividerEls.on("mousedown",function(e){return function(t){return e.dividerMousedown(t)}}(this))},r.prototype.dividerMousedown=function(e){var t,r,o,s,n;return r=this.opt("isRTL"),s=30,o=this.el.width()-30,n=this.getNaturalDividerWidth(),t=new h({dragStart:function(e){return function(){return e.dividerEls.addClass("fc-active")}}(this),drag:function(e){return function(t,i){var l;return l=r?n-t:n+t,l=Math.max(l,s),l=Math.min(l,o),e.dividerWidth=l,e.positionDivider(l),e.updateWidth()}}(this),dragEnd:function(e){return function(){return e.dividerEls.removeClass("fc-active")}}(this)}),t.startInteraction(e)},r.prototype.getNaturalDividerWidth=function(){return this.el.find(".fc-resource-area").width()},r.prototype.positionDivider=function(e){return this.el.find(".fc-resource-area").width(e)},r.prototype.renderEvents=function(e){return this.timeGrid.renderEvents(e),this.syncRowHeights(),this.updateWidth()},r.prototype.unrenderEvents=function(){return this.timeGrid.unrenderEvents(),this.syncRowHeights(),this.updateWidth()},r.prototype.updateWidth=function(){if(r.__super__.updateWidth.apply(this,arguments),this.resourceGrid.updateWidth(),this.joiner.update(),this.cellFollower)return this.cellFollower.update()},r.prototype.updateHeight=function(e){if(r.__super__.updateHeight.apply(this,arguments),e)return this.syncRowHeights()},r.prototype.setHeight=function(e,t){var r,o;return o=this.syncHeadHeights(),r=t?"auto":e-o-this.queryMiscHeight(),this.timeGrid.bodyScroller.setHeight(r),this.resourceGrid.bodyScroller.setHeight(r)},r.prototype.queryMiscHeight=function(){return this.el.outerHeight()-Math.max(this.resourceGrid.headScroller.el.outerHeight(),this.timeGrid.headScroller.el.outerHeight())-Math.max(this.resourceGrid.bodyScroller.el.outerHeight(),this.timeGrid.bodyScroller.el.outerHeight())},r.prototype.syncHeadHeights=function(){var e;return this.resourceGrid.headHeight("auto"),this.timeGrid.headHeight("auto"),e=Math.max(this.resourceGrid.headHeight(),this.timeGrid.headHeight()),this.resourceGrid.headHeight(e),this.timeGrid.headHeight(e),e},r.prototype.renderResources=function(e){var t,r,o;for(this.batchRows(),t=0,r=e.length;t<r;t++)o=e[t],this.insertResource(o);return this.rowHierarchy.show(),this.unbatchRows(),this.reinitializeCellFollowers()},r.prototype.unrenderResources=function(){return this.batchRows(),this.rowHierarchy.removeChildren(),this.unbatchRows(),this.reinitializeCellFollowers()},r.prototype.renderResource=function(e){return this.insertResource(e),this.reinitializeCellFollowers()},r.prototype.unrenderResource=function(e){var t;if(t=this.getResourceRow(e.id))return this.batchRows(),t.remove(),this.unbatchRows(),this.reinitializeCellFollowers()},r.prototype.cellFollower=null,r.prototype.reinitializeCellFollowers=function(){var t,r,o,s,n,i;for(this.cellFollower&&this.cellFollower.clearSprites(),this.cellFollower=new O(this.resourceGrid.bodyScroller,(!0)),this.cellFollower.isHFollowing=!1,this.cellFollower.isVFollowing=!0,s=[],n=this.rowHierarchy.getNodes(),r=0,o=n.length;r<o;r++)i=n[r],i instanceof X&&i.groupTd&&(t=i.groupTd.find(".fc-cell-content"),t.length&&s.push(t[0]));return this.cellFollower.setSprites(e(s))},r.prototype.insertResource=function(e,t){var r,o,s,n,i,l,u;for(u=new B(this,e),null==t&&(n=e.parentId,n&&(t=this.getResourceRow(n))),t?this.insertRowAsChild(u,t):this.insertRow(u),i=e.children,l=[],o=0,s=i.length;o<s;o++)r=i[o],l.push(this.insertResource(r,u));return l},r.prototype.insertRow=function(e,t,r){var o;return null==t&&(t=this.rowHierarchy),null==r&&(r=this.groupSpecs),r.length?(o=this.ensureResourceGroup(e,t,r[0]),o instanceof g?this.insertRowAsChild(e,o):this.insertRow(e,o,r.slice(1))):this.insertRowAsChild(e,t)},r.prototype.insertRowAsChild=function(e,t){return t.addChild(e,this.computeChildRowPosition(e,t))},r.prototype.computeChildRowPosition=function(e,t){var r,o,s,n,i,l;if(this.orderSpecs.length)for(i=t.children,o=s=0,n=i.length;s<n;o=++s)if(l=i[o],r=this.compareResources(l.resource||{},e.resource||{}),r>0)return o;return null},r.prototype.compareResources=function(e,t){return te(e,t,this.orderSpecs)},r.prototype.ensureResourceGroup=function(e,t,r){var o,s,n,i,l,u,h,c,a,p,d;if(n=(e.resource||{})[r.field],s=null,r.order)for(a=t.children,i=l=0,h=a.length;l<h;i=++l){if(d=a[i],o=de(d.groupValue,n)*r.order,0===o){s=d;break}if(o>0)break}else for(p=t.children,i=u=0,c=p.length;u<c;i=++u)if(d=p[i],d.groupValue===n){s=d;break}return s||(s=this.isVGrouping?new X(this,r,n):new g(this,r,n),t.addChild(s,i)),s},r.prototype.pairSegsWithRows=function(e){var t,r,o,s,n,i,l,u;for(s=[],n={},t=0,r=e.length;t<r;t++)u=e[t],i=u.resourceId,i&&(l=this.getResourceRow(i),l&&(o=n[i],o||(o=[l,[]],s.push(o),n[i]=o),o[1].push(u)));return s},r.prototype.rowAdded=function(e){var t,r;return e instanceof B&&(this.resourceRowHash[e.resource.id]=e,this.timeGrid.assignRowBusinessHourSegs(e)),r=this.isNesting,t=Boolean(this.nestingCnt+=e.depth?1:0),r!==t&&(this.el.toggleClass("fc-nested",t),this.el.toggleClass("fc-flat",!t)),this.isNesting=t},r.prototype.rowRemoved=function(e){var t,r;return e instanceof B&&(delete this.resourceRowHash[e.resource.id],this.timeGrid.destroyRowBusinessHourSegs(e)),r=this.isNesting,t=Boolean(this.nestingCnt-=e.depth?1:0),r!==t&&(this.el.toggleClass("fc-nested",t),this.el.toggleClass("fc-flat",!t)),this.isNesting=t},r.prototype.batchRowDepth=0,r.prototype.shownRowBatch=null,r.prototype.hiddenRowBatch=null,r.prototype.batchRows=function(){if(!this.batchRowDepth++)return this.shownRowBatch=[],this.hiddenRowBatch=[]},r.prototype.unbatchRows=function(){if(!--this.batchRowDepth)return this.hiddenRowBatch.length&&this.rowsHidden(this.hiddenRowBatch),this.shownRowBatch.length&&this.rowsShown(this.shownRowBatch),this.hiddenRowBatch=null,this.shownRowBatch=null},r.prototype.rowShown=function(e){return this.shownRowBatch?this.shownRowBatch.push(e):this.rowsShown([e])},r.prototype.rowHidden=function(e){return this.hiddenRowBatch?this.hiddenRowBatch.push(e):this.rowsHidden([e])},r.prototype.rowsShown=function(e){return this.syncRowHeights(e),this.updateWidth()},r.prototype.rowsHidden=function(e){return this.updateWidth()},r.prototype.syncRowHeights=function(e,t){var r,o,s,n,i,l,u,h,c,a;for(null==t&&(t=!1),null==e&&(e=this.getVisibleRows()),l=0,h=e.length;l<h;l++)a=e[l],a.setTrInnerHeight("");for(i=function(){var o,s,n;for(n=[],o=0,s=e.length;o<s;o++)a=e[o],r=a.getMaxTrInnerHeight(),t&&(r+=r%2),n.push(r);return n}(),n=u=0,c=e.length;u<c;n=++u)a=e[n],a.setTrInnerHeight(i[n]);if(!t&&(o=this.resourceGrid.tbodyEl.height(),s=this.timeGrid.tbodyEl.height(),Math.abs(o-s)>1))return this.syncRowHeights(e,!0)},r.prototype.getVisibleRows=function(){var e,t,r,o,s;for(r=this.rowHierarchy.getRows(),o=[],e=0,t=r.length;e<t;e++)s=r[e],s.isShown&&o.push(s);return o},r.prototype.getEventRows=function(){var e,t,r,o,s;for(r=this.rowHierarchy.getRows(),o=[],e=0,t=r.length;e<t;e++)s=r[e],s instanceof p&&o.push(s);return o},r.prototype.getResourceRow=function(e){return this.resourceRowHash[e]},r.prototype.queryResourceScroll=function(){var e,t,r,o,s,n,i,l;for(i={},l=this.timeGrid.bodyScroller.scrollEl.offset().top,s=this.getVisibleRows(),r=0,o=s.length;r<o;r++)if(n=s[r],n.resource&&(e=n.getTr("event"),t=e.offset().top+e.outerHeight(),t>l)){i.resourceId=n.resource.id,i.bottom=t-l;break}return i},r.prototype.applyResourceScroll=function(e){var t,r,o,s,n;if(e.resourceId&&(s=this.getResourceRow(e.resourceId),s&&(t=s.getTr("event"))))return o=this.timeGrid.bodyScroller.canvas.el.offset().top,r=t.offset().top+t.outerHeight(),n=r-e.bottom-o,this.timeGrid.bodyScroller.setScrollTop(n),this.resourceGrid.bodyScroller.setScrollTop(n)},r.prototype.scrollToResource=function(e){var t,r,o,s;if(o=this.getResourceRow(e.id),o&&(t=o.getTr("event")))return r=this.timeGrid.bodyScroller.canvas.el.offset().top,s=t.offset().top-r,this.timeGrid.bodyScroller.setScrollTop(s),this.resourceGrid.bodyScroller.setScrollTop(s)},r}(Y),W=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.mixin(D),r.prototype.eventRows=null,r.prototype.shownEventRows=null,r.prototype.tbodyEl=null,r.prototype.rowCoordCache=null,r.prototype.spanToSegs=function(e){var t,o,s,n,i,l;if(l=r.__super__.spanToSegs.apply(this,arguments),t=this.view.calendar,n=e.resourceId)for(o=0,s=l.length;o<s;o++)i=l[o],i.resource=t.getResourceById(n),i.resourceId=n;return l},r.prototype.prepareHits=function(){var e,t;return r.__super__.prepareHits.apply(this,arguments),this.eventRows=this.view.getEventRows(),this.shownEventRows=function(){var t,r,o,s;for(o=this.eventRows,s=[],t=0,r=o.length;t<r;t++)e=o[t],e.isShown&&s.push(e);return s}.call(this),t=function(){var t,r,o,s;for(o=this.shownEventRows,s=[],t=0,r=o.length;t<r;t++)e=o[t],s.push(e.getTr("event")[0]);return s}.call(this),this.rowCoordCache=new l({els:t,isVertical:!0}),this.rowCoordCache.build()},r.prototype.releaseHits=function(){return r.__super__.releaseHits.apply(this,arguments),this.eventRows=null,this.shownEventRows=null,this.rowCoordCache.clear()},r.prototype.queryHit=function(e,t){var o,s;if(s=r.__super__.queryHit.apply(this,arguments),s&&(o=this.rowCoordCache.getVerticalIndex(t),null!=o))return{resourceId:this.shownEventRows[o].resource.id,snap:s.snap,component:this,left:s.left,right:s.right,top:this.rowCoordCache.getTopOffset(o),bottom:this.rowCoordCache.getBottomOffset(o)}},r.prototype.getHitSpan=function(e){var t;return t=this.getSnapRange(e.snap),t.resourceId=e.resourceId,t},r.prototype.getHitEl=function(e){return this.getSnapEl(e.snap)},r.prototype.renderSkeleton=function(){var t;return r.__super__.renderSkeleton.apply(this,arguments),this.segContainerEl.remove(),this.segContainerEl=null,t=e('<div class="fc-rows"><table><tbody/></table></div>').appendTo(this.bodyScroller.canvas.contentEl),this.tbodyEl=t.find("tbody")},r.prototype.renderFgSegs=function(e){var t,r,o,s,n,i,l;for(e=this.renderFgSegEls(e),i=this.view.pairSegsWithRows(e),l=[],o=0,s=i.length;o<s;o++)n=i[o],t=n[0],r=n[1],t.fgSegs=r,t.isShown&&(t.isSegsRendered=!0,l.push(n));return this.renderFgSegsInContainers(l),this.updateSegFollowers(e),e},r.prototype.unrenderFgSegs=function(){var e,t,r,o;for(this.clearSegFollowers(),t=this.view.getEventRows(),r=0,o=t.length;r<o;r++)e=t[r],e.fgSegs=null,e.isSegsRendered=!1;return this.unrenderFgContainers(t)},r.prototype.unrenderBgSegs=function(){var e,t,o,s,n;for(r.__super__.unrenderBgSegs.apply(this,arguments),t=this.view.getEventRows(),n=[],o=0,s=t.length;o<s;o++)e=t[o],n.push(e.bgSegs=null);return n},r.prototype.rowCntWithCustomBusinessHours=0,r.prototype.renderBusinessHours=function(){return this.rowCntWithCustomBusinessHours?this.ensureIndividualBusinessHours():r.__super__.renderBusinessHours.apply(this,arguments)},r.prototype.unrenderBusinessHours=function(){return this.rowCntWithCustomBusinessHours?this.clearIndividualBusinessHours():r.__super__.unrenderBusinessHours.apply(this,arguments)},r.prototype.ensureIndividualBusinessHours=function(){var e,t,r,o,s;for(r=this.view.getEventRows(),o=[],e=0,t=r.length;e<t;e++)s=r[e],this.view.has("dateProfile")&&!s.businessHourSegs&&this.populateRowBusinessHoursSegs(s),s.isShown?o.push(s.ensureBusinessHourSegsRendered()):o.push(void 0);return o},r.prototype.clearIndividualBusinessHours=function(){var e,t,r,o,s;for(r=this.view.getEventRows(),o=[],e=0,t=r.length;e<t;e++)s=r[e],o.push(s.clearBusinessHourSegs());return o},r.prototype.assignRowBusinessHourSegs=function(e){if(e.resource.businessHours&&(this.rowCntWithCustomBusinessHours||(j.prototype.unrenderBusinessHours.call(this),this.ensureIndividualBusinessHours()),this.rowCntWithCustomBusinessHours+=1),this.view.has("dateProfile")&&this.rowCntWithCustomBusinessHours)return this.populateRowBusinessHoursSegs(e)},r.prototype.destroyRowBusinessHourSegs=function(e){if(e.clearBusinessHourSegs(),e.resource.businessHours&&(this.rowCntWithCustomBusinessHours-=1,!this.rowCntWithCustomBusinessHours))return this.clearIndividualBusinessHours(),j.prototype.renderBusinessHours.call(this)},r.prototype.populateRowBusinessHoursSegs=function(e){var t,r,o;r=e.resource.businessHours||this.view.opt("businessHours"),o=this.view.calendar.computeBusinessHourEvents(!this.isTimeScale,r),t=this.eventsToSegs(o),t=this.renderFillSegEls("businessHours",t),e.businessHourSegs=t},r.prototype.renderFill=function(e,t,r){var o,s,n,i,l,u,h,c,a,p,d,f;for(t=this.renderFillSegEls(e,t),c=[],l=[],o=0,n=t.length;o<n;o++)d=t[o],d.resourceId?c.push(d):l.push(d);for(h=this.view.pairSegsWithRows(c),f=[],s=0,i=h.length;s<i;s++)u=h[s],a=u[0],p=u[1],"bgEvent"===e&&(a.bgSegs=p),a.isShown&&f.push(u);return l.length&&f.unshift([this,l]),this.renderFillInContainers(e,f,r),t},r.prototype.renderHelper=function(e,t){var r,o;return o=this.eventToSegs(e),o=this.renderFgSegEls(o),r=this.view.pairSegsWithRows(o),this.renderHelperSegsInContainers(r,t)},r}(j),r=30,V=function(){function t(e){var t;this.view=e,this.isRTL=this.view.opt("isRTL"),this.givenColWidths=this.colWidths=function(){var e,r,o,s;for(o=this.view.colSpecs,s=[],e=0,r=o.length;e<r;e++)t=o[e],s.push(t.width);return s}.call(this)}return t.prototype.view=null,t.prototype.headEl=null,t.prototype.el=null,t.prototype.tbodyEl=null,t.prototype.headScroller=null,t.prototype.bodyScroller=null,t.prototype.joiner=null,t.prototype.colGroupHtml="",t.prototype.headTable=null,t.prototype.headColEls=null,t.prototype.headCellEls=null,t.prototype.bodyColEls=null,t.prototype.bodyTable=null,t.prototype.renderSkeleton=function(){return this.headScroller=new i({overflowX:"clipped-scroll",overflowY:"hidden"}),this.headScroller.canvas=new N,this.headScroller.render(),this.headScroller.canvas.contentEl.html(this.renderHeadHtml()),this.headEl.append(this.headScroller.el),this.bodyScroller=new i({overflowY:"clipped-scroll"}),this.bodyScroller.canvas=new N,this.bodyScroller.render(),this.bodyScroller.canvas.contentEl.html('<div class="fc-rows"><table>'+this.colGroupHtml+"<tbody/></table></div>"),this.tbodyEl=this.bodyScroller.canvas.contentEl.find("tbody"),this.el.append(this.bodyScroller.el),this.joiner=new q("horizontal",[this.headScroller,this.bodyScroller]),this.headTable=this.headEl.find("table"),this.headColEls=this.headEl.find("col"),this.headCellEls=this.headScroller.canvas.contentEl.find("tr:last-child th"),this.bodyColEls=this.el.find("col"),this.bodyTable=this.el.find("table"),this.colMinWidths=this.computeColMinWidths(),this.applyColWidths(),this.initColResizing()},t.prototype.renderHeadHtml=function(){var e,t,r,o,s,n,i,l,u,h,c;for(t=this.view.colSpecs,r="<table>",e="<colgroup>",i=0,u=t.length;i<u;i++)c=t[i],e+=c.isMain?'<col class="fc-main-col"/>':"<col/>";for(e+="</colgroup>",this.colGroupHtml=e,r+=e,r+="<tbody>",this.view.superHeaderText&&(r+='<tr class="fc-super"><th class="'+this.view.widgetHeaderClass+'" colspan="'+t.length+'"><div class="fc-cell-content"><span class="fc-cell-text">'+Se(this.view.superHeaderText)+"</span></div></th></tr>"),r+="<tr>",n=!0,o=l=0,h=t.length;l<h;o=++l)c=t[o],s=o===t.length-1,r+='<th class="'+this.view.widgetHeaderClass+'"><div><div class="fc-cell-content">'+(c.isMain?'<span class="fc-expander-space"><span class="fc-icon"></span></span>':"")+'<span class="fc-cell-text">'+Se(c.labelText||"")+"</span></div>"+(s?"":'<div class="fc-col-resizer"></div>')+"</div></th>";return r+="</tr>",r+="</tbody></table>"},t.prototype.givenColWidths=null,t.prototype.colWidths=null,t.prototype.colMinWidths=null,t.prototype.tableWidth=null,t.prototype.tableMinWidth=null,t.prototype.initColResizing=function(){return this.headEl.find("th .fc-col-resizer").each(function(t){return function(r,o){return o=e(o),o.on("mousedown",function(e){return t.colResizeMousedown(r,e,o)})}}(this))},t.prototype.colResizeMousedown=function(e,t,o){var s,n,i,l;return s=this.colWidths=this.queryColWidths(),s.pop(),s.push(null),l=s[e],i=Math.min(this.colMinWidths[e],r),n=new h({dragStart:function(e){return function(){return o.addClass("fc-active")}}(this),drag:function(t){return function(r,o){var n;return n=l+(t.isRTL?-r:r),n=Math.max(n,i),s[e]=n,t.applyColWidths()}}(this),dragEnd:function(e){return function(){return o.removeClass("fc-active")}}(this)}),n.startInteraction(t)},t.prototype.applyColWidths=function(){var e,t,r,o,s,n,i,l,u,h,c,a,p,d,f,g,y;for(r=this.colMinWidths,s=this.colWidths,e=!0,t=!1,y=0,h=0,p=s.length;h<p;h++)o=s[h],"number"==typeof o?y+=o:(e=!1,o&&(t=!0));for(l=t&&!this.view.isHGrouping?"auto":"",i=function(){var e,t,r;for(r=[],u=e=0,t=s.length;e<t;u=++e)o=s[u],r.push(null!=o?o:l);return r}(),g=0,u=c=0,d=i.length;c<d;u=++c)n=i[u],g+="number"==typeof n?n:r[u];for(u=a=0,f=i.length;a<f;u=++a)n=i[u],this.headColEls.eq(u).width(n),this.bodyColEls.eq(u).width(n);return this.headScroller.canvas.setMinWidth(g),this.bodyScroller.canvas.setMinWidth(g),this.tableMinWidth=g,this.tableWidth=e?y:void 0},t.prototype.computeColMinWidths=function(){var e,t,o,s,n,i;for(s=this.givenColWidths,n=[],e=t=0,o=s.length;t<o;e=++t)i=s[e],"number"==typeof i?n.push(i):n.push(parseInt(this.headColEls.eq(e).css("min-width"))||r);return n},t.prototype.queryColWidths=function(){var t,r,o,s,n;for(s=this.headCellEls,n=[],t=0,r=s.length;t<r;t++)o=s[t],n.push(e(o).outerWidth());return n},t.prototype.updateWidth=function(){if(this.headScroller.updateSize(),this.bodyScroller.updateSize(),this.joiner.update(),this.follower)return this.follower.update()},t.prototype.headHeight=function(){var e;return e=this.headScroller.canvas.contentEl.find("table"),e.height.apply(e,arguments)},t}(),k=function(){function t(t){this.view=t,this.children=[],this.trHash={},this.trs=e()}return t.prototype.view=null,t.prototype.parent=null,t.prototype.prevSibling=null,t.prototype.children=null,t.prototype.depth=0,t.prototype.hasOwnRow=!1,t.prototype.trHash=null,t.prototype.trs=null,t.prototype.isRendered=!1,t.prototype.isExpanded=!0,t.prototype.isShown=!1,t.prototype.addChild=function(e,t){var r,o,s,n,i;for(e.remove(),r=this.children,null!=t?r.splice(t,0,e):(t=r.length,r.push(e)),e.prevSibling=t>0?r[t-1]:null,t<r.length-1&&(r[t+1].prevSibling=e),e.parent=this,e.depth=this.depth+(this.hasOwnRow?1:0),i=e.getNodes(),o=0,s=i.length;o<s;o++)n=i[o],n.added();if(this.isShown&&this.isExpanded)return e.show()},t.prototype.removeChild=function(e){var t,r,o,s,n,i,l,u,h,c;for(t=this.children,o=!1,r=s=0,i=t.length;s<i;r=++s)if(c=t[r],c===e){o=!0;break}if(o){for(r<t.length-1&&(t[r+1].prevSibling=e.prevSibling),t.splice(r,1),e.recursivelyUnrender(),u=e.getNodes(),n=0,l=u.length;n<l;n++)h=u[n],h.removed();return e.parent=null,e.prevSibling=null,e}return!1},t.prototype.removeChildren=function(){var e,t,r,o,s,n,i;for(n=this.children,t=0,o=n.length;t<o;t++)e=n[t],e.recursivelyUnrender();for(i=this.getDescendants(),r=0,s=i.length;r<s;r++)e=i[r],e.removed();return this.children=[]},t.prototype.remove=function(){if(this.parent)return this.parent.removeChild(this)},t.prototype.getLastChild=function(){var e;return e=this.children,e[e.length-1]},t.prototype.getPrevRow=function(){var e,t;for(t=this;t;){if(t.prevSibling)for(t=t.prevSibling;e=t.getLastChild();)t=e;else t=t.parent;if(t&&t.hasOwnRow&&t.isShown)return t}return null},t.prototype.getLeadingRow=function(){return this.hasOwnRow?this:this.isExpanded&&this.children.length?this.children[0].getLeadingRow():void 0},t.prototype.getRows=function(e){var t,r,o,s;for(null==e&&(e=[]),this.hasOwnRow&&e.push(this),s=this.children,r=0,o=s.length;r<o;r++)t=s[r],t.getRows(e);return e},t.prototype.getNodes=function(e){var t,r,o,s;for(null==e&&(e=[]),e.push(this),s=this.children,r=0,o=s.length;r<o;r++)t=s[r],t.getNodes(e);return e},t.prototype.getDescendants=function(){var e,t,r,o,s;for(e=[],s=this.children,r=0,o=s.length;r<o;r++)t=s[r],t.getNodes(e);return e},t.prototype.render=function(){var t,r,o,s,n,i,l;if(this.trHash={},i=[],this.hasOwnRow){t=this.getPrevRow(),r=this.view.tbodyHash;for(l in r)s=r[l],n=e("<tr/>"),this.trHash[l]=n,i.push(n[0]),o="render"+ee(l)+"Content",this[o]&&this[o](n),t?t.trHash[l].after(n):s.prepend(n)}return this.trs=e(i).on("click",".fc-expander",ke(this,"toggleExpanded")),this.isRendered=!0},t.prototype.unrender=function(){var t,r,o,s;if(this.isRendered){t=this.trHash;for(o in t)r=t[o],s="unrender"+ee(o)+"Content",this[s]&&this[s](r);return this.trHash={},this.trs.remove(),this.trs=e(),this.isRendered=!1,this.isShown=!1,this.hidden()}},t.prototype.recursivelyUnrender=function(){var e,t,r,o,s;for(this.unrender(),o=this.children,s=[],t=0,r=o.length;t<r;t++)e=o[t],s.push(e.recursivelyUnrender());return s},t.prototype.getTr=function(e){return this.trHash[e]},t.prototype.show=function(){var e,t,r,o,s;if(!this.isShown&&(this.isRendered?this.trs.css("display",""):this.render(),this.ensureSegsRendered&&this.ensureSegsRendered(),this.isExpanded?this.indicateExpanded():this.indicateCollapsed(),this.isShown=!0,this.shown(),this.isExpanded)){for(o=this.children,s=[],t=0,r=o.length;t<r;t++)e=o[t],s.push(e.show());return s}},t.prototype.hide=function(){var e,t,r,o,s;if(this.isShown&&(this.isRendered&&this.trs.hide(),this.isShown=!1,this.hidden(),this.isExpanded)){for(o=this.children,s=[],t=0,r=o.length;t<r;t++)e=o[t],s.push(e.hide());return s}},t.prototype.expand=function(){var e,t,r,o;if(!this.isExpanded){for(this.isExpanded=!0,this.indicateExpanded(),this.view.batchRows(),o=this.children,t=0,r=o.length;t<r;t++)e=o[t],e.show();return this.view.unbatchRows(),this.animateExpand()}},t.prototype.collapse=function(){var e,t,r,o;if(this.isExpanded){for(this.isExpanded=!1,this.indicateCollapsed(),this.view.batchRows(),o=this.children,t=0,r=o.length;t<r;t++)e=o[t],e.hide();return this.view.unbatchRows()}},t.prototype.toggleExpanded=function(){return this.isExpanded?this.collapse():this.expand()},t.prototype.indicateExpanded=function(){return this.trs.find(".fc-expander .fc-icon").removeClass(this.getCollapsedIcon()).addClass(this.getExpandedIcon())},t.prototype.indicateCollapsed=function(){return this.trs.find(".fc-expander .fc-icon").removeClass(this.getExpandedIcon()).addClass(this.getCollapsedIcon())},t.prototype.enableExpanding=function(){return this.trs.find(".fc-expander-space").addClass("fc-expander")},t.prototype.disableExpanding=function(){return this.trs.find(".fc-expander-space").removeClass("fc-expander").find(".fc-icon").removeClass(this.getExpandedIcon()).removeClass(this.getCollapsedIcon())},t.prototype.getExpandedIcon=function(){return"fc-icon-down-triangle"},t.prototype.getCollapsedIcon=function(){var e;return e=this.view.isRTL?"left":"right","fc-icon-"+e+"-triangle"},t.prototype.animateExpand=function(){var e,t,r;if(r=null!=(e=this.children[0])&&null!=(t=e.getLeadingRow())?t.trs:void 0)return r.addClass("fc-collapsed"),setTimeout(function(){return r.addClass("fc-transitioning"),r.removeClass("fc-collapsed")}),r.one("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){return r.removeClass("fc-transitioning")})},t.prototype.getMaxTrInnerHeight=function(){var t;return t=0,e.each(this.trHash,function(e){return function(e,r){var o;return o=ye(r).find("> div:not(.fc-cell-content):first"),t=Math.max(o.height(),t)}}(this)),t},t.prototype.setTrInnerHeight=function(t){return e.each(this.trHash,function(e){return function(e,r){return ye(r).find("> div:not(.fc-cell-content):first").height(t)}}(this))},t.prototype.shown=function(){if(this.hasOwnRow)return this.rowShown(this)},t.prototype.hidden=function(){if(this.hasOwnRow)return this.rowHidden(this)},t.prototype.rowShown=function(e){return(this.parent||this.view).rowShown(e)},t.prototype.rowHidden=function(e){return(this.parent||this.view).rowHidden(e)},t.prototype.added=function(){if(this.hasOwnRow)return this.rowAdded(this)},t.prototype.removed=function(){if(this.hasOwnRow)return this.rowRemoved(this)},t.prototype.rowAdded=function(e){return(this.parent||this.view).rowAdded(e)},t.prototype.rowRemoved=function(e){return(this.parent||this.view).rowRemoved(e)},t}(),z=function(t){function r(e,t,o){this.groupSpec=t,this.groupValue=o,r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.prototype.groupSpec=null,r.prototype.groupValue=null,r.prototype.rowRemoved=function(e){if(r.__super__.rowRemoved.apply(this,arguments),e!==this&&!this.children.length)return this.remove()},r.prototype.renderGroupContentEl=function(){var t,r;return t=e('<div class="fc-cell-content" />').append(this.renderGroupTextEl()),r=this.groupSpec.render,"function"==typeof r&&(t=r(t,this.groupValue)||t),t},r.prototype.renderGroupTextEl=function(){var t,r;return r=this.groupValue||"",
t=this.groupSpec.text,"function"==typeof t&&(r=t(r)||r),e('<span class="fc-cell-text" />').text(r)},r}(k),g=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.prototype.hasOwnRow=!0,r.prototype.renderSpreadsheetContent=function(t){var r;return r=this.renderGroupContentEl(),r.prepend('<span class="fc-expander"><span class="fc-icon"></span></span>'),e('<td class="fc-divider" />').attr("colspan",this.view.colSpecs.length).append(e("<div/>").append(r)).appendTo(t)},r.prototype.renderEventContent=function(e){return e.append('<td class="fc-divider"> <div/> </td>')},r}(z),X=function(t){function r(){return r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.prototype.rowspan=0,r.prototype.leadingTr=null,r.prototype.groupTd=null,r.prototype.rowShown=function(e){return this.rowspan+=1,this.renderRowspan(),r.__super__.rowShown.apply(this,arguments)},r.prototype.rowHidden=function(e){return this.rowspan-=1,this.renderRowspan(),r.__super__.rowHidden.apply(this,arguments)},r.prototype.renderRowspan=function(){var t;return this.rowspan?(this.groupTd||(this.groupTd=e('<td class="'+this.view.widgetContentClass+'"/>').append(this.renderGroupContentEl())),this.groupTd.attr("rowspan",this.rowspan),t=this.getLeadingRow().getTr("spreadsheet"),t!==this.leadingTr?(t&&t.prepend(this.groupTd),this.leadingTr=t):void 0):(this.groupTd&&(this.groupTd.remove(),this.groupTd=null),this.leadingTr=null)},r}(z),p=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return Ue(t,e),t.prototype.hasOwnRow=!0,t.prototype.segContainerEl=null,t.prototype.segContainerHeight=null,t.prototype.innerEl=null,t.prototype.bgSegContainerEl=null,t.prototype.isSegsRendered=!1,t.prototype.isBusinessHourSegsRendered=!1,t.prototype.businessHourSegs=null,t.prototype.bgSegs=null,t.prototype.fgSegs=null,t.prototype.renderEventContent=function(e){return e.html('<td class="'+this.view.widgetContentClass+'"> <div> <div class="fc-event-container" /> </div> </td>'),this.segContainerEl=e.find(".fc-event-container"),this.innerEl=this.bgSegContainerEl=e.find("td > div"),this.ensureSegsRendered()},t.prototype.ensureSegsRendered=function(){if(!this.isSegsRendered)return this.ensureBusinessHourSegsRendered(),this.bgSegs&&this.view.timeGrid.renderFillInContainer("bgEvent",this,this.bgSegs),this.fgSegs&&this.view.timeGrid.renderFgSegsInContainers([[this,this.fgSegs]]),this.isSegsRendered=!0},t.prototype.ensureBusinessHourSegsRendered=function(){if(this.businessHourSegs&&!this.isBusinessHourSegsRendered)return this.view.timeGrid.renderFillInContainer("businessHours",this,this.businessHourSegs,"bgevent"),this.isBusinessHourSegsRendered=!0},t.prototype.unrenderEventContent=function(){return this.clearBusinessHourSegs(),this.bgSegs=null,this.fgSegs=null,this.isSegsRendered=!1},t.prototype.clearBusinessHourSegs=function(){var e,t,r,o;if(this.businessHourSegs){for(r=this.businessHourSegs,e=0,t=r.length;e<t;e++)o=r[e],o.el&&o.el.remove();this.businessHourSegs=null}return this.isBusinessHourSegsRendered=!1},t}(k),B=function(t){function r(e,t){this.resource=t,r.__super__.constructor.apply(this,arguments)}return Ue(r,t),r.prototype.resource=null,r.prototype.rowAdded=function(e){if(r.__super__.rowAdded.apply(this,arguments),e!==this&&this.isRendered&&1===this.children.length)return this.enableExpanding(),this.isExpanded?this.indicateExpanded():this.indicateCollapsed()},r.prototype.rowRemoved=function(e){if(r.__super__.rowRemoved.apply(this,arguments),e!==this&&this.isRendered&&!this.children.length)return this.disableExpanding()},r.prototype.render=function(){return r.__super__.render.apply(this,arguments),this.children.length>0?this.enableExpanding():this.disableExpanding(),this.view.publiclyTrigger("resourceRender",this.resource,this.resource,this.getTr("spreadsheet").find("> td"),this.getTr("event").find("> td"))},r.prototype.renderEventContent=function(e){return r.__super__.renderEventContent.apply(this,arguments),e.attr("data-resource-id",this.resource.id)},r.prototype.renderSpreadsheetContent=function(t){var r,o,s,n,i,l,u,h,c;for(u=this.resource,l=this.view.colSpecs,n=0,i=l.length;n<i;n++)r=l[n],r.group||(s=r.field?u[r.field]||null:u,c="function"==typeof r.text?r.text(u,s):s,o=e('<div class="fc-cell-content">'+(r.isMain?this.renderGutterHtml():"")+'<span class="fc-cell-text">'+(c?Se(c):"&nbsp;")+"</span></div>"),"function"==typeof r.render&&(o=r.render(u,o,s)||o),h=e('<td class="'+this.view.widgetContentClass+'"/>').append(o),r.isMain&&h.wrapInner("<div/>"),t.append(h));return t.attr("data-resource-id",u.id)},r.prototype.renderGutterHtml=function(){var e,t,r,o;for(e="",t=r=0,o=this.depth;r<o;t=r+=1)e+='<span class="fc-icon"/>';return e+='<span class="fc-expander-space"><span class="fc-icon"></span></span>'},r}(p),d.views.timeline.resourceClass=M,H=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return Ue(t,e),t.mixin($),t.prototype.timeGridClass=F,t.prototype.dayGridClass=_,t.prototype.renderHead=function(){return t.__super__.renderHead.apply(this,arguments),this.timeGrid.processHeadResourceEls(this.headContainerEl)},t.prototype.setResourcesOnGrids=function(e){if(this.timeGrid.setResources(e),this.dayGrid)return this.dayGrid.setResources(e)},t.prototype.unsetResourcesOnGrids=function(){if(this.timeGrid.unsetResources(),this.dayGrid)return this.dayGrid.unsetResources()},t}(d.AgendaView),d.views.agenda.queryResourceClass=function(e){var t;if(null!=(t=e.options.groupByResource||e.options.groupByDateAndResource)?t:e.duration&&1===e.duration.as("days"))return H},T=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return Ue(t,e),t.mixin($),t.prototype.dayGridClass=_,t.prototype.renderHead=function(){return t.__super__.renderHead.apply(this,arguments),this.dayGrid.processHeadResourceEls(this.headContainerEl)},t.prototype.setResourcesOnGrids=function(e){return this.dayGrid.setResources(e)},t.prototype.unsetResourcesOnGrids=function(){return this.dayGrid.unsetResources()},t}(d.BasicView),G=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return Ue(t,e),t.mixin($),t.prototype.dayGridClass=_,t.prototype.renderHead=function(){return t.__super__.renderHead.apply(this,arguments),this.dayGrid.processHeadResourceEls(this.headContainerEl)},t.prototype.setResourcesOnGrids=function(e){return this.dayGrid.setResources(e)},t.prototype.unsetResourcesOnGrids=function(){return this.dayGrid.unsetResources()},t}(d.MonthView),d.views.basic.queryResourceClass=function(e){var t;if(null!=(t=e.options.groupByResource||e.options.groupByDateAndResource)?t:e.duration&&1===e.duration.as("days"))return T},d.views.month.queryResourceClass=function(e){if(e.options.groupByResource||e.options.groupByDateAndResource)return G},E="2017-04-27",Q={years:1,weeks:1},y="http://fullcalendar.io/scheduler/license/",b=["GPL-My-Project-Is-Open-Source","CC-Attribution-NonCommercial-NoDerivatives"],ze=function(e,t){if(!Ee(window.location.href)&&!Te(e)&&!he(t))return Pe('Please use a valid license key. <a href="'+y+'">More Info</a>',t)},Te=function(r){var o,s,n,i;return e.inArray(r,b)!==-1||(s=(r||"").match(/^(\d+)\-fcs\-(\d+)$/),!!(s&&10===s[1].length&&(n=t.utc(1e3*parseInt(s[2])),i=t.utc(d.mockSchedulerReleaseDate||E),i.isValid()&&(o=i.clone().subtract(Q),n.isAfter(o)))))},Ee=function(e){return Boolean(e.match(/\w+\:\/\/fullcalendar\.io\/|\/demos\/[\w-]+\.html$/))},Pe=function(t,r){return r.append(e('<div class="fc-license-message" />').html(t))},void(he=function(e){return e.find(".fc-license-message").length>=1}))});