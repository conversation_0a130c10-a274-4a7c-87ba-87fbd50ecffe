(function ($, document) {
    window.Layout.setModeWindow();
    window.Layout.setTitle('Metrics');
    var loadChart;
    var ReportTypes = {
        COMPANY: 1,
        EMPLOYEE_PROJECT_LEADS: 2,
        EMPLOYEE_CUSTOMER_LEADS: 3,
        PRODUCT_LEGACY: 4,
        PRODUCT: 8,
        LEAD_CONVERSION: 5,
        EMPLOYEE_LEAD_CONVERSION: 6,
        EMPLOYEE_SALES_PERFORMANCE: 7
    };
    var SALES_REPORT_COMPANY = 1;
    var SALES_REPORT_EMPLOYEE_PROJECT_LEADS = 2;
    var SALES_REPORT_EMPLOYEE_CUSTOMER_LEADS = 3;
    var SALES_REPORT_PRODUCT = 4;
    var LEAD_CONVERSION_REPORT = 5;
    var EMPLOYEE_LEAD_CONVERSION_REPORT = 6;
    var EMPLOYEE_SALES_PERFORMANCE_REPORT = 7;
    $(document).ready(function () {
        var pageHeight = $( window ).height() * .85;
        $('#metricsHelpModal').css('height',pageHeight);
        $(document).tooltip();
        loadChart = new ChartSet();
        $('input[required], select[required]').change(function () {
            if ($(this).val() == '') {
                $(this).parent().addClass('is-invalid-label');
                $(this).addClass('is-invalid-input');
                $(this).parent().find('.form-error').addClass('is-visible');
            } else {
                $(this).parent().removeClass('is-invalid-label');
                $(this).removeClass('is-invalid-input');
                $(this).parent().find('.form-error').removeClass('is-visible');
            }
        });
        $('select[name="reportType"]').change(function () {
            if ($('select[name="reportType"]').val() == 'OpenBidsPerSalesman') {
                $('input[name="dateFrom"]').parent().parent().hide();
                $('input[name="dateTo"]').parent().parent().hide();
            } else {
                $('input[name="dateFrom"]').parent().parent().show();
                $('input[name="dateTo"]').parent().parent().show();
            }
        });
        $(function () {
            $(".datepickerFrom").datepicker({
                changeMonth: true,
                numberOfMonths: 1,
                onClose: function (selectedDate) {
                    $(".datepickerTo").datepicker("option", "minDate", selectedDate);
                }
            });
            $(".datepickerTo").datepicker({
                changeMonth: true,
                numberOfMonths: 1,
                onClose: function (selectedDate) {
                    $(".datepickerFrom").datepicker("option", "maxDate", selectedDate);
                }
            });
        });

        var runReport = function () {
            $('#marketingReport').remove();
            $('#overallSalesReport').remove();
            $('#employeeSalesReport').remove();
            $('#productSalesReport').remove();
            $('#chart').parent().parent().css('display', '');
            var getReport = false;
            if ($('select[name="reportType"]').val() == 'OpenBidsPerSalesman') {
                getReport = true;
            } else {
                if ($('input[name="dateFrom"]').val() == '' || $('input[name="dateTo"]').val() == '') {
                    getReport = false;
                    $('input[required]').each(function () {
                        if ($(this).val() == '') {
                            $(this).parent().addClass('is-invalid-label');
                            $(this).addClass('is-invalid-input');
                            $(this).parent().find('.form-error').addClass('is-visible');
                        } else {
                            $(this).parent().removeClass('is-invalid-label');
                            $(this).removeClass('is-invalid-input');
                            $(this).parent().find('.form-error').removeClass('is-visible');
                        }
                    });
                }
                else {
                    getReport = true;
                }
            }
            if (getReport) {
                var reportType = $('select[name="reportType"]').val();
                if (/^[0-9]+/.test(reportType)) {
                    getSalesReport(parseInt(reportType));
                } else if (reportType === 'Marketing') {
                    getMarketingReport();
                } else {
                    reportType = 'metrics.' + reportType + '.php';

                    var dateFrom = $('input[name="dateFrom"]').val();
                    var dateTo = $('input[name="dateTo"]').val();
                    var chart = 'ColumnChart';

                    loadChart.drawChart(document.getElementById('chart'), reportType, dateFrom, dateTo, chart);
                }
            }
        };

        $('#runReport').on('click', runReport);

        var buildMarketingReportTableRow = function (marketingData) {
            $('#marketingReport').remove();
            $('#overallSalesReport').remove();
            $('#employeeSalesReport').remove();
            $('#productSalesReport').remove();
            $('#chart').parent().parent().css('display', 'none');
            $('#marketingTable').clone().attr('id', 'marketingReport').css('display', 'block').insertBefore('#marketingTable');
            var allMarketing = marketingData.all;
            var unsourced = marketingData.unsourced;
            var allMarketingRow = $('#heading').clone().attr('id', 'allMarketing');
            var unsourcedRow = $('#heading').clone().attr('id', 'unsourced');
            var sources = marketingData.sources;

            for (var key in allMarketing) {
                allMarketingRow.find('[name="' + key + '"]').text(allMarketing[key]);
            }

            $('#marketingReport tbody').append(allMarketingRow);

            for (var key in unsourced) {
                unsourcedRow.find('[name="' + key + '"]').text(unsourced[key]);
            }

            unsourcedRow.addClass('source');
            $('#marketingReport tbody').append(unsourcedRow);

            for (var source in sources) {
                var sourceRow = $('#heading').clone().attr('id', '');
                var subsources = sources[source]['subsources'];
                var sourceName = sources[source].leadSource;
                sourceRow.attr('name', sourceName);
                sourceRow.addClass('source');
                for (var key in sources[source]) {
                    if (key != 'subsources') {
                        sourceRow.find('[name="' + key + '"]').text(sources[source][key]);
                    }
                }
                $('#marketingReport tbody').append(sourceRow);
                var gray = true; //toggle this for each row to alternate the colors under a source between white and gray.
                for (var subsource in subsources) {
                    gray = !gray;
                    var subsourceRow = $('#heading').clone().attr('id', '');
                    subsourceRow.addClass('subsource');
                    subsourceRow.attr('name', subsources[subsource].leadSource);
                    if (gray) {
                        subsourceRow.css('background-color', '#F6F7FB');
                    }
                    for (var key in subsources[subsource]) {
                        subsourceRow.find('[name="' + key + '"]').text(subsources[subsource][key]);
                    }
                    subsourceRow.find('[name="leadSource"]').addClass('subsourceName');
                    if (subsources[subsource].unspecified) {
                        subsourceRow.find('[name="leadSource"]').append(' <img class="tip" src="' + window.fx_url.assets.IMAGE + 'icons/info.png" title="This is an unspecified source - ' + sourceName + ' was selected, but a source was not specified."/>');
                        if (subsources[subsource].leads == "0") {
                            subsourceRow = null; //remove unspecified row if there is no unspecified data.
                        }
                    }
                    $('#marketingReport tbody').append(subsourceRow);
                }
            }
            $('#loading-image').hide();
        };

        var buildSalesReportTable = function (salesData, type) {
            $('#marketingReport').remove();
            $('#overallSalesReport').remove();
            $('#employeeSalesReport').remove();
            $('#productSalesReport').remove();
            $('#chart').parent().parent().css('display', 'none');
            switch (type) {
                case ReportTypes.LEAD_CONVERSION:
                case ReportTypes.COMPANY:
                    var tableDiv = $('<div id="overallSalesReport" class="row" style="display:block;padding-bottom:5rem;">');
                    var table = $('<table width="100%"><tbody></tbody></table>');
                    table.appendTo(tableDiv);
                    tableDiv.insertAfter('#marketingTable');
                    var tbody = table.find('tbody');
                    var baseRow = $('<tr class="heading"><td></td><td data-name="result"></td></tr>');
                    var tooltip = $('<img class="tip" src="' + window.fx_url.assets.IMAGE + 'icons/info.png" title=""/>');
                    for (key in salesData) {
                        var row = baseRow.clone();
                        var description = tooltip.clone();
                        description.attr('title', salesData[key]['description']);
                        row.find('td:eq(0)').text(salesData[key]['name'] + ' ').append(description);
                        row.find('td:eq(1)').text(salesData[key]['value']);
                        tbody.append(row);
                    }
                    break;
                case ReportTypes.EMPLOYEE_PROJECT_LEADS:
                case ReportTypes.EMPLOYEE_CUSTOMER_LEADS:
                case ReportTypes.EMPLOYEE_LEAD_CONVERSION:
                case ReportTypes.EMPLOYEE_SALES_PERFORMANCE:
                case ReportTypes.PRODUCT:
                    var tableDiv = $('<div id="employeeSalesReport" data-type="' + type + '" class="row" style="display:block;padding-bottom:5rem;"></div>');
                    var table = $('<table><tbody></tbody></table>');
                    table.appendTo(tableDiv);
                    tableDiv.insertAfter('#marketingTable');
                    var tbody = table.find('tbody');
                    var headerRow = $('<tr class="heading"></tr>');
                    var tooltip = $('<img class="tip" src="' + window.fx_url.assets.IMAGE + 'icons/info.png" title=""/>');
                    var type_classes = {
                        1: 'text',
                        2: 'percent',
                        3: 'number',
                        4: 'number'
                    };
                    var header_keys = Object.keys(salesData['headers']);
                    for (datum in salesData['data']) {
                        var resultsRow = $('<tr class="results"></tr>');
                        tbody.append(resultsRow);
                        for (header in salesData['headers']) {
                            var td_class = 'text';
                            if (salesData['headers'][header]['type']) {
                                td_class = type_classes[salesData['headers'][header]['type']];
                            }
                            resultsRow.append('<td class="' + td_class + '"></td>');
                        }
                    }
                    for (header in salesData['headers']) {
                        var index = header_keys.indexOf(header);
                        var headerCell = $('<td data-name="' + header + '">' + salesData['headers'][header]['name'] + ' ' + '</td>');
                        if (salesData['headers'][header]['description'] !== '') {
                            var description = tooltip.clone();
                            description.attr('title', salesData['headers'][header]['description']);
                            headerCell.append(description);
                        }
                        headerRow.append(headerCell);
                        for (datumIndex in salesData['data']) {
                            tbody.find('tr:eq(' + datumIndex + ') td:eq(' + index + ')').text(salesData['data'][datumIndex][header]);
                        }
                    }
                    tbody.prepend(headerRow);
                    if (salesData['data'].length === 0){
                        var size = header_keys.length;
                        tbody.append('<td class="noSalesData" colspan='+ size +'>No employee sales data available</td>')
                    }
                    break;
                case ReportTypes.PRODUCT_LEGACY:
                    var tableDiv = $('<div id="productSalesReport" class="row" style="display:block;padding-bottom:5rem;"></div>');
                    var table = $('<table width="100%"><tbody></tbody></table>');
                    table.appendTo(tableDiv);
                    tableDiv.insertAfter('#marketingTable');
                    var tbody = table.find('tbody');
                    var header = $('<tr class="heading"><td>Product</td><td>Sales</td><td>Percent Total Sales</td></tr>');
                    var baseRow = $('<tr class="heading"><td></td><td class="money"></td><td class="money"></td></tr>');
                    var grossSalesRow = '<tr class="heading mainservice"><td>Gross Sales</td><td class="money">' + salesData.grossSales + '</td><td class="money">100%</td></tr>';
                    var bidDiscountRow = '<tr class="heading mainservice"><td>Bid Discounts</td><td class="money">' + salesData.bidDiscount + '</td><td class="money"></td></tr>';
                    var netSalesRow = '<tr class="heading mainservice"><td>Net Sales</td><td class="money">' + salesData.bidTotal + '</td><td class="money"></td></tr>';
                    tbody.append(header);
                    tbody.append(grossSalesRow);
                    tbody.append(bidDiscountRow);
                    tbody.append(netSalesRow);
                    for (key in salesData.services) {
                        var row = baseRow.clone();
                        row.attr('class', 'heading mainservice');
                        row.find('td:eq(0)').text(salesData.services[key]['name']);
                        row.find('td:eq(1)').text(salesData.services[key]['total']);
                        row.find('td:eq(2)').text(salesData.services[key]['percent']);
                        tbody.append(row);
                        if (salesData.services[key]['services'] !== null) {
                            for (i in salesData.services[key]['services']) {
                                var subservice = salesData.services[key]['services'][i];
                                var subRow = baseRow.clone();
                                subRow.find('td:eq(0)').attr('class', 'subservice').text(subservice['name']);
                                subRow.find('td:eq(1)').text(subservice['total']);
                                subRow.find('td:eq(2)').text(subservice['percent']);
                                tbody.append(subRow);
                            }
                        }
                    }
                    break;
            }
        };

        var getMarketingReport = function () {
            $('#loading-image').show();
            var dateFrom = $('input[name="dateFrom"]').val();
            var dateTo = $('input[name="dateTo"]').val();
            $.ajax({
                url: window.fx_url.BASE + 'marketing-report.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    startDate: dateFrom,
                    endDate: dateTo
                },
                success: function (response) {
                    buildMarketingReportTableRow(response);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        };

        var getSalesReport = function (type) {
            $('#loading-image').show();
            var startDate = $('input[name="dateFrom"]').val();
            var endDate = $('input[name="dateTo"]').val();
            $.ajax({
                url: window.fx_url.BASE + 'sales-report.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    type: type,
                    startDate: startDate,
                    endDate: endDate
                },
                success: function (response) {
                    buildSalesReportTable(response, type);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Sales report could not be retrieved. ' +
                        '<NAME_EMAIL> for assistance.');
                }
            }).done($('#loading-image').hide());
        };
        var openMetricsHelpModal = function() {
            $('#metricsHelpModal').foundation('open');
        };

        $('#openMetricsHelpModal').on('click', openMetricsHelpModal);
    });
})(jQuery, document);