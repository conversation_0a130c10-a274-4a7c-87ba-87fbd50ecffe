var MapSet = function MapSet(domObj, defaultLat, defaultLong) { //send the company lat / longitude so when there are no events, it will point to the company lat / long
    var self = this;
    self._do = domObj;
    self._lat = defaultLat, self._lng = defaultLong;
    self.map = null;
    self.infoWindow = new google.maps.InfoWindow({});
    self.geocoder = new google.maps.Geocoder();
    self.address = [],
    self.markers = [],
    self.location = { lat: null, lng: null };
    self.adminArea = { county: null, township: null };
    //self._fblistener = null;

    // console.log("in mapset " + defaultLat, defaultLong);

    self.GetCoordinates = function (address, city, state, zip, callback) {
        self.geocoder.geocode({ 'address': address + ',' + city + ' ' + state + ' ' + zip, 'region': 'us' }, function (results, status) {
            var r = null;
            if (status == google.maps.GeocoderStatus.OK) {
                if (results.length == 0)
                    r = { status: 0, msg: 'Address not found', title: null, lat: null, lng: null };
                else if (results.length > 1) {
                    r = { status: 0, msg: 'More than one location returned', title: null, lat: null, lng: null };
                }
                else {
                    var locationIcon, counter = 0, a;
                    for (var j = 0; j < results[0].types.length; j++) {
                        if (results[0].types[j] == "street_address" || results[0].types[j] == "premise" || results[0].types[j] == "subpremise") {
                            if (results[0].partial_match) {
                                r = { status: 0, msg: 'Partial match', title: null, lat: null, lng: null };
                            }
                            else {
                                r = { status: 1, msg: 'Location mapped succesfully', title: results[0].formatted_address, lat: results[0].geometry.location.lat(), lng: results[0].geometry.location.lng() };
                                break;
                                //self.location.lat = results[0].geometry.location.lat();
                                //self.location.lng = results[0].geometry.location.lng();
                            }
                        }
                        else {
                            r = { status: 0, msg: 'Partial match of incorrect address type', title: null, lat: null, lng: null };
                        }
                    }
                }
            }
            if ($.isFunction(callback)) callback(r, self);
        });
    };

    self.MapAddress = function (address, city, state, zip, useAddressFunction, callback) {
        self.geocoder.geocode({ 'address': address + ',' + city + ' ' + state + ' ' + zip, 'region': 'us' }, function (results, status) {
            if (status == google.maps.GeocoderStatus.OK) {
                if (self.address != null && self.address.length > 0) {
                    for (var i = 0; i < self.address.length; i++) {
                        self.address[i].setMap(null);
                    }
                    self.address = [];
                    //$('#latitude').val('');
                    //$('#longitude').val('');
                    //$('.locationData').hide();
                    self.location.lat = null;
                    self.location.lng = null;
                    self.adminArea.county = null;
                    self.adminArea.township = null;
                }
                var locationIcon, counter = 0, a;

                function getAdminArea(addrComponents) {
                    var county = null;
                    var township = null;

                    for (var i = 0; i < addrComponents.length; i++) {
                        if (addrComponents[i].types[0] == "administrative_area_level_2") {
                            county = addrComponents[i].short_name;
                        }
                        else if (addrComponents[i].types[0] == "administrative_area_level_3") {
                            township = addrComponents[i].short_name;
                        }
                    }
                    return [county, township];
                }

                function getAddressComponents(address_components) {
                    var street_number = '';
                    var street = '';
                    var city = '';
                    var state = '';
                    var zip = '';

                    for (var i = 0; i < address_components.length; i++) {
                        let value_short = address_components[i].short_name;
                        let value_long = address_components[i].long_name;
                        switch (address_components[i].types[0]) {
                            case 'street_number':
                                street_number = value_long;
                                break;
                            case 'route':
                                street = value_short;
                                break;
                            case 'neighborhood':
                            case 'locality':
                            case 'sublocality':
                            case 'sublocality_level_1':
                                city = value_long;
                                break;
                            case 'administrative_area_level_1':
                                state = value_short;
                                break;
                            case 'postal_code':
                                zip = value_short;
                                break;
                        }
                    }
                    return [street_number, street, city, state, zip];
                }

                for (var i = 0; i < results.length; i++) {
                    for (var j = 0; j < results[i].types.length; j++) {
                        if (results[i].types[j] == "street_address" || results[i].types[j] == "premise" || results[i].types[j] == "subpremise") {
                            if (results[i].partial_match || results.length > 1) {
                                locationIcon = {
                                    url: window.fx_url.assets.IMAGE + 'icons/location_question.png',
                                    labelOrigin: new google.maps.Point(15, 15),
                                    scaledSize: new google.maps.Size(30, 30),
                                    anchor: new google.maps.Point(15, 15)
                                };
                            }
                            else {
                                locationIcon = {
                                    url: window.fx_url.assets.IMAGE + 'icons/location_star.png',
                                    labelOrigin: new google.maps.Point(15, 15),
                                    scaledSize: new google.maps.Size(30, 30),
                                    anchor: new google.maps.Point(15, 15)
                                };
                                self.location.lat = results[i].geometry.location.lat();
                                self.location.lng = results[i].geometry.location.lng();

                                var admin_area = getAdminArea(results[i].address_components);
                                self.adminArea.county = admin_area[0];
                                self.adminArea.township = admin_area[1];

                            }
                            a = results[i].formatted_address.split(',');
                            var admin_area = getAdminArea(results[i].address_components);
                            admin_area[0] == null ? '' : admin_area[0];
                            admin_area[1] == null ? '' : admin_area[1];

                            var address_components = getAddressComponents(results[i].address_components);

                            if (address_components[2] == '' && admin_area[1] != '') {
                                address_components[2] = admin_area[1];
                            }

                            if (self.map == null) self.map = new google.maps.Map(self._do, {});
                            self.address[counter] = new google.maps.Marker({
                                position: results[i].geometry.location,
                                map: self.map,
                                title: results[i].formatted_address,
                                icon: locationIcon,
                                iwc: ((results[i].partial_match || results.length > 1) ? '<b>Partial Match:</b><br>' : '') + results[i].formatted_address + (useAddressFunction == null ? '' : '<br><a href="javascript:' + useAddressFunction + '(\'' + a[0].trim().replace("'", "\\'") + '\',\'' + a[1].trim().replace("'", "\\'") + '\',\'' + a[2].trim().split(' ')[0].replace("'", "\\'") + '\',\'' + a[2].trim().split(' ')[1].replace("'", "\\'") + '\',' + results[i].geometry.location.lat() + ',' + results[i].geometry.location.lng() + ',\'' + admin_area[0] + '\',\'' + admin_area[1] + '\')">Use This Address</a>'),
                                address_full: {
                                    address: address_components[0] + ' ' + address_components[1],
                                    city: address_components[2],
                                    state: address_components[3],
                                    zip: address_components[4]
                                }
                            });
                            //address[counter].addListener('click', function () {
                            //    thisInfowindow[i].open(map, address[counter]);
                            //});
                            google.maps.event.addListener(self.address[counter], 'click', (function (mapSet, marker, i) {
                                return function () {
                                    mapSet.infoWindow.setContent(marker.iwc); //(title + '<br>' + location.address + '<br>' + location.city + ' ' + location.state + ' ' + location.zip);
                                    mapSet.infoWindow.open(mapSet.map, marker);
                                }
                            })(self, self.address[counter], i));
                            //self.address[counter].addListener('click', function (e) {
                            //    
                            //});

                            counter++;
                        }
                    }
                }
            }
            self.FitBounds();
            if ($.isFunction(callback)) callback(self);
        });
    };

    self.MapLocation = function (lat, lng, title) {
        if (self.address != null && self.address.length > 0) { //removed 'x'
            for (var i = 0; i < self.address.length; i++) {
                self.address[i].setMap(null);
            }
            self.address = [];
            self.location.lat = null;
            self.location.lng = null;
        }
        if ($.isNumeric(lat) && $.isNumeric(lng)) {
            self.location.lat = 1 * lat;
            self.location.lng = 1 * lng;
            // Create a map object and specify the DOM element for display.
            // Create a marker and set its position.
            var locationIcon = {
                url: window.fx_url.assets.IMAGE + 'icons/location_star.png',
                labelOrigin: new google.maps.Point(15, 15),
                scaledSize: new google.maps.Size(30, 30),
                anchor: new google.maps.Point(15, 15)
            };
            if (self.map == null) self.map = new google.maps.Map(self._do, {});
            self.address[0] = new google.maps.Marker({
                map: self.map,
                position: self.location,
                title: title,
                icon: locationIcon
            });
            //bounds = new google.maps.LatLngBounds();
            //bounds.extend(marker.position);
            //map.fitBounds(bounds);
            //if (map.getZoom() > 16) map.setZoom(16);
            //var listener = google.maps.event.addListener(map, 'idle', function () {
            //    if (map.getZoom() > 16 && !map.initialZoom) {
            //        map.setZoom(16);
            //        map.initialZoom = true;
            //    }
            //    google.maps.event.removeListener(listener);
            //});
        }
        self.FitBounds();
    }

    self.ShowAddressMarkers = function () {
        if (self.map == null) self.map = new google.maps.Map(self._do, {});
        if (self.address != null && self.address.length > 0) {
            for (var i = 0; i < self.address.length; i++)
                self.address[i].setMap(self.map);
            //map.fitBounds(bounds);
            self.FitBounds();
        }
    };

    self.RemoveAddressMarkers = function () {
        if (self.address != null && self.address.length > 0) {
            for (var i = 0; i < self.address.length; i++)
                self.address[i].setMap(null);
            //map.fitBounds(bounds);
            self.FitBounds();
        }
    };


    self.SetMapLocations = function(locations, start, end) {
        for (var i = 0; i < self.markers.length; i++) {
            self.markers[i].setMap(null);
        }
        self.markers = [];

        if (self.map == null) {
            self.map = new google.maps.Map(self._do, {});
        }

        if (locations != null) {
            var j = 0, r, rid;
            for (var i = 0; i < locations.length; i++) {
                var location = locations[i];
                if (rid != location.resourceId) { r = 1; rid = location.resourceId; }
                var labelCharacter = '' + (r <= 9 ? r : (r < 36 ? String.fromCharCode(55 + r) : ''));
                //if ($('.fc-toolbar').length > 0) { //TODO: Determine what this is
                if (typeof (location.start) == "string") location.start = moment(location.start);
                if (typeof (location.end) == "string") location.end = moment(location.end);

                if (location.start != null && location.end != null) {
                    location.end.local();
                    location.start.local();
                    if (location.eventType == 1 && location.start._d < end && location.end._d >= start) {
                        var latitude = location.latitude;
                        var longitude = location.longitude;
                        image = {
                            path: 'm12 23.7-6.4-6.4c-3.5-3.5-3.5-9.2 0-12.7s9.2-3.5 12.7 0 3.5 9.2 0 12.7L12 23.7zm0-7.3c3.1 0 5.6-2.5 5.6-5.6S15.1 5.2 12 5.2s-5.6 2.5-5.6 5.6 2.5 5.6 5.6 5.6z',
                            fillColor: location.calendarBgColor,
                            fillOpacity: 1,
                            strokeWeight: 0,
                            scale: 1.5,
                            labelOrigin: new google.maps.Point(12, 11),
                            anchor: new google.maps.Point(12, 23)
                        };
                        self.markers[j] = new google.maps.Marker({
                            position: new google.maps.LatLng(latitude, longitude),
                            map: self.map,
                            label: {
                                text: labelCharacter,
                                color: 'black',
                                className: 'map-label'
                            },
                            icon: image,
                            title: location.resourceFirstName + ' ' + location.resourceLastName + ': ' + location.city + ', ' + location.state + ' ' + location.zip + ' (' + (location.scheduleType) + ')',
                            fxl_pincontent: '<b>' + location.resourceFirstName + ' ' + location.resourceLastName + '</b> (' + (location.scheduleType) + ')<br>' + location.title + '<br>' + location.address + '<br>' + location.city + ' ' + location.state + ' ' + location.zip + '<br/><a target="_blank" href="'+location.directions+'">Directions</a>'
                        });

                        google.maps.event.addListener(self.markers[j], 'click', (function (mapset, marker, i) {
                            return function () {
                                mapset.infoWindow.setContent(marker.fxl_pincontent);
                                mapset.infoWindow.open(mapset.map, marker);
                            }
                        })(this, self.markers[j], j));
                        j++;
                        r++;
                    }
                    //} else {
                    //    if (eventType == null || location.eventType == eventType) {
                    //        var latitude = location.latitude;
                    //        var longitude = location.longitude;
                    //        image = {
                    //            url: '/images/markers/' + location.calendarBgColor.replace('#', '') + '.png',
                    //            labelOrigin: new google.maps.Point(15, 12),
                    //            scaledSize: new google.maps.Size(30, 30),
                    //        };
                    //        if (self.map == null) self.map = new google.maps.Map(self._do, {});
                    //        markers[j] = new google.maps.Marker({
                    //            position: new google.maps.LatLng(latitude, longitude),
                    //            map: self.map,
                    //            label: {
                    //                text: labelCharacter,
                    //                color: 'black'
                    //            },
                    //            icon: image,
                    //            title: location.resourceFirstName + ' ' + location.resourceLastName + ': ' + location.title,
                    //            fxl_pincontent: '<b>' + location.resourceFirstName + ' ' + location.resourceLastName + '</b><br>' + location.title + '<br>' + location.address + '<br>' + location.city + ' ' + location.state + ' ' + location.zip
                    //        });

                    //        google.maps.event.addListener(markers[j], 'click', (function (mapset, marker, i) {
                    //            return function () {
                    //                mapset.infoWindow.setContent(marker.fxl_pincontent);
                    //                mapset.infoWindow.open(mapset.map, marker);
                    //            }
                    //        })(this, self.markers[j], j));
                    //        j++;
                    //        r++;
                    //    }
                    //}
                }
            }
        }

        //self.MapAddress();

        self.FitBounds();
    }

    self.FitBounds = function () {
        var bounds = new google.maps.LatLngBounds();
        var isAddressSet = false;
        if (self.markers != null && $.isArray(self.markers) && self.markers.length > 0) {
            for (var i = 0; i < self.markers.length; i++) {
                bounds.extend(self.markers[i].position);
                isAddressSet = true;
            }
        }
        if (self.address != null && $.isArray(self.address) && self.address.length > 0) {
            for (var i = 0; i < self.address.length; i++) {
                bounds.extend(self.address[i].position);
                isAddressSet = true;
            }
        }
        //if (self.map == null) self.map = new google.maps.Map(self._do, {});
        if (self.map == null) return;

        if (isAddressSet) {
            self.map.fitBounds(bounds);
            if (self.map.getZoom() > 16) self.map.setZoom(16);
            google.maps.event.addListenerOnce(self.map, 'idle', self.fbf);
        }
        else {
            // var defaultLocation = new google.maps.LatLng(38.907596, -94.377571);
            //use defaultLat / defaultLong here 
            var defaultLocation = new google.maps.LatLng(self._lat, self._lng);
            //console.log("lat / long" + defaultLat + " " + defaultLong);
            bounds.extend(defaultLocation);
            self.map.fitBounds(bounds);
            self.map.setZoom(10);
            google.maps.event.addListenerOnce(self.map, 'idle', self.fbf);
        }
    };

    self.fbf = function () {
        if (self.map == null) self.map = new google.maps.Map(self._do, {});
        if (self.markers.length == 0 && self.address.length == 0)
            self.map.setZoom(10);
        else if (self.map.getZoom() > 16)
            self.map.setZoom(16);
    };

    self.RemoveMapLocations = function () {
        if (self.markers != null && self.markers.length > 0) {
            for (var i = 0; i < self.markers.length; i++)
                self.markers[i].setMap(null);
        }
        self.markers = [];
    };

    self.Resize = function () {
        //console.log('Resize');
        if (self.map == null) self.map = new google.maps.Map(self._do, {});
        google.maps.event.trigger(self.map, 'resize');
    };
    

}