$(document).ready(function() {

    var pageHeight = $( window ).height() * .85;

    $('input[name="signature"]').on('change.fx', function () {
        var $this = $(this);
        if ($this.val() !== '') {
            $this.parent().removeClass('is-invalid-label');
            $this.removeClass('is-invalid-input');
            $this.parent().find('.form-error').removeClass('is-visible');
        }
    });

    $('.acknowledgement-content').on('change.fx', function () {
        var $this = $(this);
        if ($this.is(':checked')) {
            $this.parent().find('.form-error').removeClass('is-visible');
        }
    });

    $('#view-contract').click(function(){
        $('#contractModal').css('height',pageHeight);
        $('#contractModal').foundation('open');
    });

    $('#accept').on('click.fx', function () {
        $(this).blur();
        $('.sign-contract').show();
        $("html, body").animate({ scrollTop: $(document).height()-$(window).height()}, 700);
    });

    $('#reject').click(function(){
        $('#rejectBidModal').foundation('open');
    });


    $('#signContract').click(function() {
        $('#contractModalAccept').foundation('open');
    });



    $('#signContractAccept').click(function() {
        $(this).blur();
        var evaluationID = $('#evaluationID').text();
        var customEvaluation = $('#customEvaluation').text();
        var bidItemID = $('#bidItemID').text();
        var bidAcceptedName = null;
        if (bid_info.has_contract) {
            bidAcceptedName = bid_info.first_name + ' ' + bid_info.last_name;
        } else {
            var signature = $('input[name="signature"]').val();
            bidAcceptedName = signature === '' ? null : signature;
        }

        var acknowledgement_errors = false;
        var acknowledgement_answers = {};
        var acknowledgements = $('.acknowledgement-content');
        for (var i=0; i < acknowledgements.length; i++) {
            var this_acknowledgement = $(acknowledgements[i]);
            if (this_acknowledgement.attr('required') !== undefined && !this_acknowledgement.is(':checked')) {
                acknowledgement_errors = true;
                this_acknowledgement.parent().find('.form-error').addClass('is-visible');
            }
            acknowledgement_answers[this_acknowledgement.attr('name')] = this_acknowledgement.is(':checked');
        }

        var signature_error = false;
        if (bidAcceptedName === null) {
            signature_error = true;
            var signature_input = $('input[name="signature"]');
            signature_input.parent().addClass('is-invalid-label');
            signature_input.addClass('is-invalid-input');
            signature_input.parent().find('.form-error').addClass('is-visible');
        }

        if (!signature_error && !acknowledgement_errors) {
            $('#loading-image').show();
            $.ajax({
                url: window.fx_url.BASE + 'bid-accept.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    evaluationID: evaluationID,
                    customEvaluation: customEvaluation,
                    bidItemID: bidItemID,
                    bidID: bid_info.id,
                    bidAcceptedName: bidAcceptedName,
                    acknowledgementAnswers: acknowledgement_answers
                },
                success: function(response) {
                    window.location.href = "bid-accept-email.php?id=" + bid_info.id + "&email=send";

                    $('loading-image').hide();


                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    $('loading-image').hide();

                }

            });
        }
    });


    $('#rejectBid').click(function() {
        var evaluationID = $('#evaluationID').text();
        var customEvaluation = $('#customEvaluation').text();
        var bidItemID = $('#bidItemID').text();

        $.ajax({
            url: window.fx_url.BASE + 'bid-reject.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                evaluationID: evaluationID,
                customEvaluation: customEvaluation,
                bidItemID: bidItemID,
                bidID: bid_info.id
            },
            success: function(response) {
                if (response == null) {
                    location.reload();
                } else {

                    window.location.href = "bid-reject-email.php?id=" + bid_info.id;

                }


            }, error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    });

});
