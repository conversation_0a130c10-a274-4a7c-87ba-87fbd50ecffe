var roles = {
    sales: {
        label: 'Sales',
        column: 'sales',
        eventTypes: ['Evaluation']
    },
    installation: {
        label: 'Installation',
        column: 'installation',
        eventTypes: ['Installation']
    }
};
var groups = {
    roles: {
        label: 'Roles',
        class: 'role-list no-bullet',
        id: ''
    },
    users: {
        label: 'Users',
        class: 'user-list no-bullet',
        id: 'sortable'
    }
};

var filterContainer = $('#filterGroup');

function openFilter() {
    var width = '20%';
    if ($(window).width() < 640) {
        width = '50%';
    }
    filterContainer.animate({ width: width }, { duration: 300, easing: "swing" });
    filterContainer.addClass('show');
}

function closeFilter() {
    filterContainer.animate({
        width: '0%'
    }, {
        duration: 300,
        easing: "swing",
        complete: function() {
            $(window).trigger('resize');
        }
    });
    filterContainer.removeClass('show');
}

$(document).ready(function () {
    var showFilter = $('.filter-show, #filter');

    showFilter.click(function () {
        if ($('#filterGroup').hasClass('show')) {
            document.setCookie('view_calendar_filter', 'false');
            closeFilter();
        } else {
            document.setCookie('view_calendar_filter', 'true');
            openFilter();
        }
    });

    if (document.getCookie('view_calendar_filter') == 'true'){
        openFilter();
    }

    var users = {};
    var filterGroup = $('#filterGroup');

    $.ajax({
        url: window.fx_url.API + 'calendar/resources',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            filter: '[]'
        },
        success: function (response) {
            $('#loading-image').hide();
            if (response.status === 1) {
                var resource_filter = filterGroup.find('.list');

                for (var i in groups) {
                    var group = groups[i];
                    parent = $('<ul></ul>').attr('class', 'filter-section no-bullet').appendTo(resource_filter);
                    parent.prepend('<li class="section-title"><span>'+group.label+'</span></li>');
                    group.elem = $('<ul></ul>').attr('label', group.label).attr('class', group.class).attr('id', group.id);
                    parent.find('li span').after(group.elem);
                }
                for (var i in roles) {
                    var role = roles[i];
                    groups['roles'].elem.append('<li class="role-item" data-id="'+role.column+'" data-label="role">'+role.label+'</li>');
                }

                $.each(response.result, function (i, item) {
                    if (item.id > 0) {
                        var user = $('<li class="ui-state-default" data-label="user" data-id="' + item.id + '"><a class="handle"><span class="move"></span></a>' + item.title + '</li>');
                        var dataRole = [];
                        for (var i in roles) {
                            var role = roles[i];
                            dataRole[i] = item[role.column];
                        }
                        user.data('roles',dataRole);

                        groups['users'].elem.append(user);

                        if (item.sales) {
                            $('select[name="projectSalesperson"]').append('<option value="' + item.id + '">' + item.title + '</option>');
                        }
                    }
                });

                $(window).trigger('resize');

                var roleItem = filterGroup.find('li[data-label="role"]');
                var userItem = filterGroup.find('li[data-label="user"]');
                var activeRole = null;

                function uncheckAll() {
                    roleItem.removeClass('active');
                    userItem.removeClass('active');
                }

                filterGroup.on('click.fx', 'li', function () {
                    var $this = $(this);

                    switch($this.data('label')) {
                        case 'role':
                            activeRole = $this;

                            if (activeRole.hasClass('active')) {
                                uncheckAll();
                            } else {
                                uncheckAll();
                                activeRole.addClass('active');
                                var role = activeRole.data('id');
                                userItem.each(function() {
                                    var $this = $(this);
                                    if ($this.data('roles')[role] == true) {
                                        $this.addClass('active');
                                    }
                                });
                            }
                            break;
                        case 'user':
                            if ($this.hasClass('active')) {
                                $this.removeClass('active');
                                if (activeRole) {
                                    activeRole.removeClass('active');
                                }
                            } else {
                                $this.addClass('active');
                                if (activeRole) {
                                    activeRole.removeClass('active');
                                }
                            }
                            break;
                    }
                });

                var calendarFilterCookie = document.getCookie('calendar_filter');
                if (calendarFilterCookie !== '') {
                    try {
                        calendarFilterCookie = JSON.parse(calendarFilterCookie);

                        if (Array.isArray(calendarFilterCookie)){
                            for (i = 0; i < calendarFilterCookie.length; i++) {
                                var cookieID = calendarFilterCookie[i];
                                var newItem = resource_filter.find('li[data-id="'+cookieID+'"]');
                                newItem.addClass('active');
                                if (newItem.hasClass('role-item')) {
                                    activeRole = newItem;
                                }
                            }
                        }

                        if (resource_filter.find('li').hasClass('active')) {
                            showFilter.addClass('active');
                        }
                    }
                    catch(err) {}
                }

                function getFilterSelected(){
                    var filterCalendar = [];
                    resource_filter.find('li.active').each(function(index){
                        filterCalendar[index] = $(this).data('id');
                    });
                    return filterCalendar;
                };

                $('#filter-apply').click(function() {
                    var selectedItems = getFilterSelected();

                    if (selectedItems.length > 0) {
                        showFilter.addClass('active');
                    } else {
                        showFilter.removeClass('active');
                    }

                    document.setCookie('calendar_filter', JSON.stringify(selectedItems));
                    var view = $('#calendar').fullCalendar('getView');
                    var sortableList = $('#sortable');

                    if (sortableList.hasClass('changed')) {
                        $('#loading-image').show();
                        $.ajax({
                            url: window.fx_url.API + 'calendar/resources/order',
                            dataType: "json",
                            type: "POST",
                            contentType: "application/x-www-form-urlencoded",
                            data: {
                                sort: getUserSortOrder()
                            },
                            success: function (response) {
                                if (response.status === 1) {
                                    sortableList.removeClass('changed');
                                    filterCalendar(view.name);
                                }
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                console.log(textStatus, errorThrown);
                                console.log(jqXHR.responseText);
                            }
                        });
                    } else {
                        filterCalendar(view.name);
                    }

                });

                $('#filter-clear').click(function() {
                    uncheckAll();
                    showFilter.removeClass('active');

                    document.setCookie('calendar_filter', JSON.stringify(getFilterSelected()));

                    var view = $('#calendar').fullCalendar('getView');
                    filterCalendar(view.name);
                });

                if ($('.newCustomerDisplay').length > 0) {
                    addNewCustomer();
                } else {
                    var views = {
                        D: {
                            button: '#dailyView',
                            view: 'timelineDay'
                        },
                        M: {
                            button: '#monthlyView',
                            view: 'month'
                        },
                        R: {
                            button: '#resourceWeekView',
                            view: 'resourceWeek'
                        },
                        LD: {
                            button: '#listDay',
                            view: 'listDay'
                        },
                        LW: {
                            button: '#listWeek',
                            view: 'listWeek'
                        },
                        LM: {
                            button: '#listMonth',
                            view: 'listMonth'
                        }
                    };

                    var cookie_view = document.getCookie('calendar_view');
                    if (typeof views[cookie_view] === 'undefined') {
                        cookie_view = $(window).width() < 640 ? 'LD' : 'D';
                    } else if ($(window).width() < 640 && $.inArray(cookie_view, ['LD', 'LW', 'LM']) < 0) {
                        cookie_view = 'LD';
                    }

                    var view = views[cookie_view];
                    for (var i in views) {
                        if (i === cookie_view) {
                            continue;
                        }
                        $(views[i].button).removeClass('active');
                    }
                    $(view.button).addClass('active');
                    filterCalendar(view.view);
                }

                $('#sortable').sortable({
                    delay: 200,
                    handle: '.handle',
                    stop: function( event, ui ) {
                        $(this).addClass('changed');
                    }
                });
                $('#sortable').disableSelection();
            } else {
                $('#calendar').empty();
                $('.dashboard-filter-bar').empty();
                $('#noResourcesModal').foundation('open');
            }

        }, error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });

});

function getFilterUsers(){
    var filterUsers = [];

    var activeUsers = $("#sortable li[data-label='user'].active");

    if (activeUsers.length > 0) {
        userSelection = activeUsers;
    } else {
        userSelection = $("#sortable li[data-label='user']");
    }

    userSelection.each(function(index){
        filterUsers[index] = $(this).data('id');
    });
    return filterUsers;
};

function getUserSortOrder(){
    var allUsers = [];

    $("#sortable li[data-label='user']").each(function(index){
        allUsers[index] = $(this).data('id');
    });
    return allUsers;
};