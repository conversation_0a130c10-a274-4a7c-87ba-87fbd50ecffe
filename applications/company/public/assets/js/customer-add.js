var decodeEntities = function (encodedString) {
    var textArea = document.createElement('textarea');
    textArea.innerHTML = encodedString;
    return textArea.value;
}

var map = null, addressLast = null, cityLast = null, stateLast = null, zipLast = null, loc = null, s, e;
var hiddenMap = null, addressLast = null, cityLast = null, stateLast = null, zipLast = null, loc = null, s, e;

var useAddress = function (a, c, s, z, lat, lng, c2, t) {
    $('#address').val(a);
    $('#city').val(c);
    $('#state').val(s);
    $('#zip').val(z);
    $('#latitude').val(lat);
    $('#longitude').val(lng);
    $('#county').val(c2);
    $('#township').val(t);
    mapAddress();
}

var mapLatLong = function (){
    hiddenMap = new MapSet($('#hiddenMap')[0], $('#companyLatitude').text(), $('#companyLongitude').text());
    addressLast = $('#address').val();
    cityLast = $('#city').val();
    stateLast = $('#state option:selected').val();
    zipLast = $('#zip').val();
    if (addressLast != '' && cityLast != '' && stateLast != '' && zipLast != '') {
        hiddenMap.MapAddress(addressLast, cityLast, stateLast, zipLast, 'useAddress', setLatLng);
    }
}

var mapAddress = function () {
    if($('#calendarMap').length == 0) {
        return;
    }
    if ($('#latitude').val().length > 0 && $('#longitude').val().length > 0) {
        var address = $('#address').val() + ', ' + $('#city').val() + ', ' + $('#state option:selected').val() + ', ' + $('#zip').val();
        map.MapLocation($('#latitude').val(), $('#longitude').val(), address);
    }
    else if ($('#address').val() != '' && $('#city').val() != '' && $('#state option:selected').val() != '' && $('#zip').val() != '') {
        if (addressLast != $('#address').val() || cityLast != $('#city').val() || stateLast != $('#state').val() || zipLast != $('#zip').val()) {
            addressLast = $('#address').val();
            cityLast = $('#city').val();
            stateLast = $('#state option:selected').val();
            zipLast = $('#zip').val();
            map.MapAddress(addressLast, cityLast, stateLast, zipLast, 'useAddress', setLatLng);
        }
        else {
            map.ShowAddressMarkers();
        }
    }
    else {
        map.RemoveAddressMarkers();
        $('#latitude').val('');
        $('#longitude').val('');
    }
};

var initializeMap = function() {
    if ($('.fc-toolbar').length > 0) {
        var locations = $('#calendar').fullCalendar('clientEvents');

        var sD = current_date.split('-');
        var start = new Date(sD[0], sD[1] - 1, sD[2]);
        var end = new Date(start.getTime() + 86400000);

        var newEvents = [];
        $.each(locations, function(i, item) {
            if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                newEvents.push(item);
            }
        });

        setMapLocations(newEvents, start, end);
    }
}

var setMapLocations = function (locations, start, end) {
    if (map === null) {
        map = new MapSet($('#calendarMap')[0], $('#companyLatitude').text(), $('#companyLongitude').text());
        console.log('called map set');
    }
    map.SetMapLocations(locations, start, end);
    mapAddress();
}

var setLatLng = function(mapset){
    var latitude = mapset.location.lat != null ? mapset.location.lat : '';
    var longitude = mapset.location.lng != null ? mapset.location.lng : '';
    $('#latitude').val(latitude);
    $('#longitude').val(longitude);

    var county = mapset.adminArea.county != null ? mapset.adminArea.county : '';
    var township = mapset.adminArea.township != null ? mapset.adminArea.township : '';
    $('#county').val(county);
    $('#township').val(township);
};

var is_idevice = /iPhone|iPad|iPod/i.test(navigator.userAgent);

function validateDates() {
    var elems = {
        start_date: {
            input: $('input[name="scheduledStartDate"]'),
            error: $('#startDateErr')
        },
        start_time: {
            input: $('input[name="scheduledStartTime"]'),
            error: $('#startTimeErr')
        },
        end_date: {
            input: $('input[name="scheduledEndDate"]'),
            error: $('#endDateErr')
        },
        end_time: {
            input: $('input[name="scheduledEndTime"]'),
            error: $('#endTimeErr')
        }
    };
    var start_date = elems.start_date.input.val().trim(),
        start_time = !is_idevice ? elems.start_time.input.wickedpicker('time') : elems.start_time.input.val(),
        start_datetime = null,
        end_date = elems.end_date.input.val().trim(),
        end_time = !is_idevice ? elems.end_time.input.wickedpicker('time') : elems.end_time.input.val(),
        time_format = !is_idevice ? 'h:mmA' : 'HH:mm',
        errors = {};

    if (start_date === '') {
        errors.start_date = 'Start date is required';
        start_date = null;
    }
    if (start_time === '') {
        errors.start_time = 'Start time is required';
        start_datetime = null;
    } else if (start_date !== null) {
        start_datetime = moment(start_date + ' ' + start_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
    }

    if (end_date === '') {
        errors.end_date = 'End date is required';
        end_date = null;
    }
    if (end_time === '') {
        errors.end_time = 'End time is required';
    } else if (start_datetime !== null && end_date !== null) {
        var end_datetime = moment(end_date + ' ' + end_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
        if (end_datetime.isSameOrBefore(start_datetime, 'minute')) {
            errors.end_time = 'End time must be after start time';
        }
    }

    var error_count = 0;
    for (var name in elems) {
        var elem = elems[name];
        if (errors[name]) {
            elem.error.text(errors[name]).show();
            elem.input.addClass('required').addClass('is-invalid-input');
            error_count++;
            continue;
        }
        elem.error.hide();
        elem.input.removeClass('required').removeClass('is-invalid-input');
    }
    return error_count === 0;
}

var populateDropdown = (dropdown_id, response, repeat_value, selected_value, submit_type) => {
    const $dropdown = $(`#${dropdown_id}`);
    $dropdown.empty();
    // Add the default "Select one" option
    $dropdown.append('<option value="">Select One</option>');

    response.forEach(item => {
        if (item.parentMarketingTypeID == null) { // Source
            $dropdown.append(`<optgroup class="source" data-id="${item.marketingTypeID}" label="${item.marketingTypeName}"></optgroup>`);
        } else { // Subsource
            $dropdown.find(`.source[data-id="${item.parentMarketingTypeID}"]`)
                .append(`<option class="subsource" value="${item.marketingTypeID}">${item.marketingTypeName}</option>`);
        }
    });

    // if it's adding a property or project
    if ((submit_type === 'submitNewProperty' || submit_type === 'submitNewProject') ) {
        // and we don't have a repeat value then trigger an error
        if (repeat_value === null) {
            $dropdown.trigger('change');
        // else if we have a repeat value set it
        } else {
            $dropdown.find(`option[value="${repeat_value}"]`).prop('selected', true).trigger('change');
        }
    // else if we are adding a new customer and we have a selected value from the lead then set it
    } else if (submit_type === 'submitNewCustomer' && selected_value !== '') {
        $dropdown.find(`option[value="${selected_value}"]`).prop('selected', true).trigger('change');
    }
};

var getMarketingTypes = () => {
    var submit_type = $('[type="submit"]').attr('name');

    var repeat_business_mkt_type_id = customer_add_info.repeat_business_marketing_type !== null ? customer_add_info.repeat_business_marketing_type : null;
    var secondary_repeat_business_mkt_type_id = customer_add_info.repeat_business_marketing_type !== null ? customer_add_info.repeat_business_marketing_type : null;

    $('#loading-image').show();

    $.ajax({
        url: window.fx_url.BASE + 'getMarketingTypes.php',
        dataType: "json",
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        success: function (response) {
            var existing_primary_marketing_type_id = $('#marketingTypeID').val();

            // Populate primary mkt source dropdown
            populateDropdown('referralDropdown', response, repeat_business_mkt_type_id, existing_primary_marketing_type_id, submit_type);

            // Populate secondary mkt source dropdown (optional)
            if ($('#secondaryReferralDropdown').length) {
                populateDropdown('secondaryReferralDropdown', response, secondary_repeat_business_mkt_type_id, null, null);
            }

            $('#loading-image').hide();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error("AJAX request failed:", textStatus, errorThrown);
            console.error("Response text:", jqXHR.responseText);
        }
    });
};

var current_date = customer_add_info.todays_date_default;
var today = new Date();
today.setHours(0,0,0,0);

var duplicateSearchResult = null;
var emailAddress = null;

$(document).ready(function () {
    $('html').addClass('t-mode-window');
    $('.l-app').addClass('t-fluid-height');

    if ($('#customerID').val() === '') {
        $('[name="sendIntroEmail"]').prop('checked', false);
    } else {
        $('.introEmail').hide();
    }

    $('button[data-calendar-open]').on( "click", function() {
        $('.newCustomerDisplay').hide();
        $('#calendarContainer').show();

        var currentView = $('.dashboard-filter-bar .button-group').find('.button.active').attr('id');

        var views = {
            dailyView: {
                view: 'timelineDay'
            },
            monthlyView: {
                view: 'month'
            },
            resourceWeekView: {
                view: 'resourceWeek'
            }
        };
        var view = views[currentView];
        filterCalendar(view.view);

        window.scrollTo(0, 0);
    });

    $('button[data-calendar-close]').on( "click", function() {
        $('#calendarContainer').hide();
        $('.newCustomerDisplay').show();
    });

    $('select[name="projectSalesperson"]').focus(function(){
        $('select[name="projectSalesperson"]').removeClass('isFloating');
    });

    $('select[name="projectSalesperson"]').change(checkSalesperson);

    $('select[name="projectDescription"]').select2();

    $('.duplicateSearch').change(checkDuplicateCustomers);

    //Confirm Appointment Click Yes on Modal
    $('#confirmAppointmentYes').click(function(){
        var eventModal = $('#addEvent');
        var calendar = $('#calendar');

        if (eventModal.find('select[name="salesperson"]').val() == 0) {
            if (eventModal.find('.callout.small.alert.response').length < 1){
                eventModal.append('<div class="callout small alert"><p>Unable to schedule this event with a company.</p></div>');
            } else {
                eventModal.find('.callout.small.alert p').text('Unable to schedule this event with a company.');
            }
        } else {

            var $this = $(this);
            var fxEventData = $this.data('fx.event');
            var eventModal = $('#addEvent');

            if (validateDates() == true) {

                //Get Temporary ID
                var tempID = eventModal.find('input[name="tempID"]').val();

                //Remove Last Rendered Event
                calendar.fullCalendar('removeEvents', tempID);

                //Get Input Start Day and Time
                var newStartDay = eventModal.find('input[name="scheduledStartDate"]').val();

                if (is_idevice) {
                    var newStartTime = eventModal.find('input[name="scheduledStartTime"]').val();
                    var startHours = newStartTime.split(':')[0];
                    var startMinutes = newStartTime.split(':')[1];
                    var newStartDayFormat = new Date(newStartDay);

                } else {
                    // var newStartTime = $('input[name="scheduledStartTime"]').wickedpicker('time');
                    var newStartTime = eventModal.find('#scheduledStartTime').wickedpicker('time');

                    var startHours = newStartTime.split(':')[0];
                    var startMinutes = newStartTime.split(':')[1];
                    startMinutes = startMinutes.slice(0, -2)
                    var startSuffix = newStartTime.split(' ')[3];

                    startHours = startHours.trim();
                    startMinutes = startMinutes.trim();
                    startSuffix = startSuffix.trim();


                    if (startSuffix == 'pm' || startSuffix == 'PM') {
                        startHours = startHours != '12' ? parseInt(startHours) + parseInt(12) : startHours;
                    }

                    var newStartDayFormat = new Date(newStartDay);
                }

                newStartDayFormat.setHours(startHours, startMinutes);

                //Get Input End Day and Time
                var newEndDay = eventModal.find('input[name="scheduledEndDate"]').val();

                if (is_idevice) {
                    var newEndTime = eventModal.find('input[name="scheduledEndTime"]').val();

                    var endHours = newEndTime.split(':')[0];
                    endHours = endHours.trim();

                    var endMinutes = newEndTime.split(':')[1];
                    endMinutes = endMinutes.trim();

                } else {
                    var newEndTime = eventModal.find('input[name="scheduledEndTime"]').wickedpicker('time');
                    var endHours = newEndTime.split(':')[0];
                    endHours = endHours.trim();

                    var endMinutes = newEndTime.split(':')[1];
                    endMinutes = endMinutes.trim();

                    endMinutes = endMinutes.slice(0, -2)
                    var endSuffix = newEndTime.split(' ')[3];

                    endSuffix = endSuffix.trim();

                    if (endSuffix == 'pm' || endSuffix == 'PM') {
                        endHours = endHours != '12' ? parseInt(endHours) + parseInt(12) : endHours;
                    }

                }

                var endMonth = new Date(eventModal.find('input[name="scheduledEndDate"]').val());
                endMonth = endMonth.getMonth();

                var newEndDayFormat = new Date(newEndDay);
                newEndDayFormat.setHours(endHours, endMinutes);

                //Get Salesperson (Resource) ID
                var salesId = eventModal.find('select[name="salesperson"]').val();
                var newResource = $('#calendar').fullCalendar('getResourceById', salesId);

                var calendarBgColor = newResource.calendarBgColor;

                //Set New Event Data
                eventData = {
                    title: fxEventData.title,
                    firstName: fxEventData.firstName,
                    lastName: fxEventData.lastName,
                    businessName: fxEventData.businessName,
                    start: newStartDayFormat,
                    end: newEndDayFormat,
                    resourceId: salesId,
                    scheduledUserID: salesId,
                    address: fxEventData.address,
                    city: fxEventData.city,
                    state: fxEventData.state,
                    zip: fxEventData.zip,
                    calendarBgColor: calendarBgColor,
                    scheduleType: 'Evaluation'
                };

                calendar.fullCalendar('rerenderEvents');

                calendar.fullCalendar('renderEvent', eventData, true); // stick? = true

                $('#confirmAppointmentYes').data('fx.event', eventData);

                //Get Temporary Event ID
                var clientEvents = calendar.fullCalendar('clientEvents');
                var lastEvent = clientEvents[clientEvents.length - 1];
                var lastId = lastEvent._id;

                eventModal.find('input[name="tempID"]').val(lastId);
                calendar.fullCalendar('unselect');

                if ($('select[name="projectSalesperson"].isFloating').has('option[value="' + salesId + '"]').length > 0) {
                    $('select[name="projectSalesperson"].isFloating > option[value="' + salesId + '"]').prop('selected', true);
                }

                checkSalesperson();

                $('.appointment-summary').show();

                $('.appointment-date').html(moment(newStartDayFormat).format('dddd, MMMM D, YYYY'));
                $('.appointment-time').html(moment(newStartDayFormat).format('h:mm a') + ' - ' + moment(newEndDayFormat).format('h:mm a'));
                $('.appointment-salesperson').html($('select[name="salesperson"] > option[value="' + salesId + '"]').text());

                $('[name="sendIntroEmail"]').prop('checked', false);
                $('.introEmail').hide();

                $('#addEvent').foundation('close');
                $('button[data-calendar-close]').trigger('click');
            }
        }

    });

    scheduleModalShown = false;
    duplicatesModalShown = false;

    //Get all marketing types for the referral dropdown
    getMarketingTypes();

    Object.entries(customer_add_info.priorities).forEach(function([id, name]) {
        if (id === "" || name === "") return; // Skip invalid entries
        let selected = $('#priority').val() === id ? 'selected' : '';
        $('#priorityDropdown').append('<option '+ selected +' value="' + id + '">' + name + '</option>');
    });

    customer_add_info.project_types.forEach(function(item) {
        let selected = $('#projectTypeID').val() === item.projectTypeID ? 'selected' : '';
        $('#typeDropdown').append('<option '+ selected +' value="' + item.projectTypeID + '">' + item.type + '</option>');
    })

    //Enable/Disable Email Field On Click
    $('[name="noEmailRequired"]').change(function (){
        var email = $('[name="email"]');
        var sendIntroEmail = $('[name="sendIntroEmail"]');
        var introEmail = $('.introEmail');

        if ($('input[name="noEmailRequired"]').is(':checked')) {
            email.prop('disabled', true);
            email.val('');
            email.removeAttr('required');
            email.parent().removeClass('is-invalid-label');
            email.removeClass('is-invalid-input');
            email.parent().find('.form-error').removeClass('is-visible');

            sendIntroEmail.prop('checked', false);
            introEmail.hide();
        } else {
            email.prop('disabled', false);
            email.prop('required', true);

            if ($('.appointment-summary').is(':hidden')) {
                sendIntroEmail.prop('checked', false);
                introEmail.show();
            }
        }
    });

    if(is_idevice) {
        //change the input types for time to
        //html 5 "time" so that we don't have
        //interface issues.
        $('#scheduledStartTime').removeClass('timepicker');
        $('#scheduledEndTime').removeClass('timepicker');

        $('#scheduledStartTime').attr('type','time');
        $('#scheduledEndTime').attr('type', 'time');
    }

    $('#cancelAddress').click(function(){

        $('#submitNewCustomer').prop('disabled', false);
    });

    $('#scheduledStartTime, #scheduledEndTime, #scheduledStartDate, #scheduledEndDate, #scheduleSalesperson').focus(function(){
        if(is_idevice){
            //without this, the time controls lost focus and made data entry impossible.
            $(window).scrollTop(0);
        }
    });

    //show warning if scheduling date in past
    $('#scheduledStartDate').on('change', function(){
        $('.callout.small.alert').remove();
        if (new Date($('#scheduledStartDate').val()) < today || new Date($('#scheduledEndDate').val()) < today) {
            if ($('.callout.small.alert').length < 1){
                $('#addEvent').append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
                $('#scheduledEndDate').trigger("change");
            } else {
                $('#addEvent').find('.callout.small.alert p').text('Unable to schedule this event with a company.');
            }
        }
    });

    $('#scheduledEndDate').on('change', function(){
        $('.callout.small.alert').remove();
        if (new Date($('#scheduledStartDate').val()) < today|| new Date($('#scheduledEndDate').val()) < today) {
            if ($('.callout.small.alert').length < 1){
                $('#addEvent').append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
            } else {
                $('#addEvent').find('.callout.small.alert p').text('Unable to schedule this event with a company.');
            }
        }
    });

    //prevent keyboard from popping up on mobile devices.  Prevent keypress event on other devices.
    $('input[name="scheduledEndTime"]').prop('readonly', 'readonly');
    $('input[name="scheduledStartTime"]').prop('readonly', 'readonly');
    $('input[name="scheduledEndDate"]').prop('readonly', 'readonly');
    $('input[name="scheduledStartDate"]').prop('readonly', 'readonly');

    function validateProjectContacts() {
        if ($('#emailTable tbody').children().filter(":visible").length >= 1){
            var runNameValidation = false;
            var nameValidation = true;
            var errorCount = 0;
            $('#emailTable tbody tr:visible').each(function () {
                var peValidation = true;
                $this = $(this);
                var name = $this.find('[name="name"]').val();
                emailInput = $this.find('[name="contactEmail"]');
                var email = emailInput.val();
                var phoneNumber = $this.find('[name="contactPhone"]').val();

                if (email != '') {
                    runNameValidation = true;
                    if (!isEmail(email)) {
                        peValidation = false;
                        errorCount++;
                    }
                }
                if (phoneNumber != '') {
                    runNameValidation = true;
                    if (phoneNumber.length < 14) {
                        peValidation = false;
                        errorCount++;
                    }
                }
                if ((email == '' && phoneNumber == '' && name != '') || (emailInput.parent().find('.validEmail').hasClass('is-visible'))) {
                    peValidation = false;
                    errorCount++;
                }

                if (runNameValidation === true) {
                    if (name == '') {
                        nameValidation = false;
                    }
                }

                if (nameValidation === false) {
                    $('small.contactName').addClass('is-visible');
                } else {
                    $('small.contactName').removeClass('is-visible');
                }

                if (nameValidation === false || peValidation === false) {
                    $this.addClass('is-invalid-input');
                } else if (nameValidation === true && peValidation === true) {
                    $this.removeClass('is-invalid-input');
                }
            });

            if (errorCount > 0) {
                $('small.contactEmail').addClass('is-visible');
            } else {
                $('small.contactEmail').removeClass('is-visible');
            }
        }
    };

    function checkProjectDescriptionSelect(){
        var projectDescriptionSelect = $('select[name="projectDescription"]');

        if (projectDescriptionSelect.val() != null){
            projectDescriptionSelect.parent().parent().removeClass('is-invalid-label');
            projectDescriptionSelect.removeClass('is-invalid-input');
            projectDescriptionSelect.parent().parent().find('.form-error').removeClass('is-visible');
            return true;
        } else {
            projectDescriptionSelect.parent().parent().addClass('is-invalid-label');
            projectDescriptionSelect.addClass('is-invalid-input');
            projectDescriptionSelect.parent().parent().find('.form-error').addClass('is-visible');
            return false;
        }
    }

    function validate() {
        if (duplicateSearchResult !== null && duplicateSearchResult.length > 0 && !duplicatesModalShown) {
            $('#confirmSaveDuplicates').foundation('open');
        } else {
            continueValidate();
        }
    }

    function continueValidate() {
        var addCustomer;
        addCustomer = 1;

        $('input[required], select[required]').each(function() {
			if ($.trim($(this).val()) == '') {
				$(this).parent().addClass('is-invalid-label');
				$(this).addClass('is-invalid-input');
				$(this).parent().find('.form-error').not('.validEmail').addClass('is-visible');
				addCustomer = 0;
                $(window).scrollTop(0);
				return false;
			} else if ($(this).attr('name') == 'email' && $('.validEmail').hasClass('is-visible')) {
				addCustomer = 0;
                $(window).scrollTop(500);
				return false;
			} else if ($(this).attr('name') == 'email' && !isEmail($(this).val())) {
                $(this).parent().addClass('is-invalid-label');
                $(this).addClass('is-invalid-input');
                addCustomer = 0;
                $(window).scrollTop(500);
                return false;
            } else {
                $(this).parent().removeClass('is-invalid-label');
                $(this).removeClass('is-invalid-input');
                addCustomer = 1;
            }
        });

        if (addCustomer === 0) {
            return false;
        }

        validateProjectContacts();

        if ($('#phoneTable tbody tr:visible').length > 0) {
            $('small.onePhone').removeClass('is-visible');

            $('#phoneTable tbody tr:visible').each(function(){
                if ($(this).find('td input.phone').val() == '' || $(this).find('td select').val() == '') {
                    $('small.fullPhone').addClass('is-visible');
                    addCustomer = 0;
                    return false;
                } else {
                    if ($(this).find('td input.phone').val().length < 14) {
                        $('small.fullPhone').addClass('is-visible');
                        addCustomer = 0;
                        return false;
                    } else {
                        $('small.fullPhone').removeClass('is-visible');
                        addCustomer = 1;
                    }
                }
            });

            var projectDescription = $('select[name="projectDescription"]');

            if (addCustomer == 1) {
                if (projectDescription.length >= 1){
                    addCustomer = checkProjectDescriptionSelect();
                }
            }

            if (addCustomer == 1) {
                if ($('#phoneTable .isPrimary:checked').length !== 1) {
                    $('small.primary').addClass('is-visible');
                    addCustomer = 0;
                    return false;
                } else {
                    if ($('#phoneTable .isPrimary:checked').is(':visible')) {
                        $('small.primary').removeClass('is-visible');
                        addCustomer = 1;
                    } else {
                        $('small.primary').addClass('is-visible');
                        addCustomer = 0;
                        return false;
                    }
                }
            }

        } else {
            $('small.onePhone').addClass('is-visible');
            addCustomer = 0;
            return false;
        }

        if ($(".is-visible")[0]){
            addCustomer = 0;
            return false;
        }
        if (addCustomer == 1) {
            if ($('#scheduledStartDate').val() == '' || $('#scheduledEndDate').val() == ''){
                //we want to ensure that you want to add a customer without
                //scheduling an appointment for evaluation
                if (!scheduleModalShown){
                    $('#confirmSaveUnscheduled').foundation('open');
                    // return false;
                    addCustomer = 0;
                    return false;
                }
            } else{
                $('#submitNewCustomer').prop('disabled', true);
            }
        }

        if (addCustomer == 1) {
            checkAddress();
        }
    };

    function checkAddress(){
        var correctAddress = true;
        address = $('input[name="address"]').val();
        city = $('input[name="city"]').val();
        state = $('select[name="state"]').val();
        zip = $('input[name="zip"]').val();
        var geocodeAddress = hiddenMap.address[0] !== undefined ? hiddenMap.address[0] : null;
        var addressFullText = $('#addressFullText');

        $('#addressPartialText').text(address + ', ' + city + ', ' + state + ' ' + zip);

        if ($('#latitude').val() == '' || $('#longitude').val() == ''){
            if (geocodeAddress){
                addressFullText.text(geocodeAddress.title);
                correctAddress = false;
            }
        } else if (geocodeAddress !== null && ((geocodeAddress.address_full.address !== address) ||
            (geocodeAddress.address_full.city !== city) ||
            (geocodeAddress.address_full.state !== state) ||
            (geocodeAddress.address_full.zip !== zip))) {
            addressFullText.text(geocodeAddress.address_full.address + ', ' + geocodeAddress.address_full.city + ', ' + geocodeAddress.address_full.state + ', ' + geocodeAddress.address_full.zip);
            correctAddress = false;
        }

        if (!correctAddress){
            $('#latLongModal').foundation('open');
        } else {
            submitData();
        }
    };

    function submitData(){
        var form = $('form');
        var formdata = new FormData(form[0]);
        var customerPhoneArray = storeCustomerPhoneTableData();
        var emailArray = storeEmailTableData();
        var referralMarketingTypeID = $('#referralDropdown option:selected').val();
        var projectTypeID = $('#typeDropdown option:selected').val();
        var priority = $('#priorityDropdown option:selected').val();
        var valid = true;
        [ 'select[name="projectSalesperson"]',
            'select[name="projectType"]',
            '#referralDropdown'
        ].forEach(function(selector) {
            var el = $(selector);
            if (el.prop('required')) {
                if (!el.val()) {
                    el.parent().parent().addClass('is-invalid-label');
                    el.addClass('is-invalid-input');
                    valid = false;
                } else {
                    el.parent().parent().removeClass('is-invalid-label');
                    el.removeClass('is-invalid-input');
                }
            }
        });

        if (!valid) {
            return;
        }

        if(referralMarketingTypeID == 0){
            referralMarketingTypeID = null;
        }

        if (referralMarketingTypeID) {
            formdata.append('referralMarketingTypeID', referralMarketingTypeID);
        }

        if (projectTypeID) {
            formdata.append('projectType', projectTypeID);
        }

        if (priority) {
            formdata.append('priority', priority);
        }



        var projectDescription = $('select[name="projectDescription"]');
        var projectDescriptionArray = '';

        if (projectDescription.length >= 1){
            if (projectDescription.val() != null){
                projectDescriptionArray = projectDescription.val();
            }
        }

        function storeEmailTableData() {
            var emailTableData = new Array();
            $('#emailTable tbody tr').each(function(row,tr){
                var name = $(tr).find('td:eq(0)').find('[name="name"]').val().trim();
                var phone = $(tr).find('td:eq(1)').find('[name="contactPhone"]').val();
                var email = $(tr).find('td:eq(2)').find('[name="contactEmail"]').val();
                if (name == '') {
                    return;
                }
                if (phone == ''){
                    phone = null;
                }
                if (email == ''){
                    email = null;
                }
                emailTableData[row]={
                    "name": name,
                    "phone":phone,
                    "email": email
                };
            });
            emailTableData.shift();
            var emailTableArray = emailTableData;
            return emailTableArray = JSON.stringify(emailTableArray);
        }

        function storeCustomerPhoneTableData() {
            var phoneTableData = new Array();
            $('#phoneTable tbody tr').each(function(row,tr){
                phoneTableData[row]={
                    "phoneDescription":$(tr).find('td:eq(0)').find('select').val(),
                    "phoneNumber":$(tr).find('td:eq(1)').find('input').val(),
                    "isPrimary":$(tr).find('td:eq(2)').find('input:checked').val()
                };
            });
            phoneTableData.shift();
            var customerPhoneTableArray = phoneTableData;
            return customerPhoneTableArray = JSON.stringify(customerPhoneTableArray);
        }

        var projectNotePin = 0;
        if ($('input[name="isPinnedNote"]').is(':checked')) {
            projectNotePin = 1;
        }

        formdata.append('emailArray', emailArray);

        formdata.append('customerPhoneArray', customerPhoneArray);

        formdata.append('projectDescriptionArray', projectDescriptionArray);

        formdata.append('projectNotePin', projectNotePin);

        if (duplicateSearchResult !== null) {
            formdata.append('isDuplicateMatchShown', duplicateSearchResult.length > 0 ? 1 : 0);
        }

        $('#loading-image').show();

        if ($('input[name="scheduledStartTime"]').val() !='' && $('input[name="scheduledEndTime"]').val() !='' ) {

            if (is_idevice) {

                var userStartTime = $('input[name="scheduledStartTime"]').val().toString();
                var userEndTime = $('input[name="scheduledEndTime"]').val().toString();

                var startHours = userStartTime.split(':')[0];
                var startMinutes = userStartTime.split(':')[1];

                var endHours = userEndTime.split(':')[0];
                var endMinutes = userEndTime.split(':')[1];

            } else {

                var userStartTime = $('input[name="scheduledStartTime"]').wickedpicker('time').toString();
                var userEndTime = $('input[name="scheduledEndTime"]').wickedpicker('time').toString();

                var startHours = userStartTime.split(':')[0];
                var startMinutes = userStartTime.split(':')[1];
                startMinutes = startMinutes.slice(0, -2)
                var startSuffix = userStartTime.split(' ')[3];

                startHours = startHours.trim();
                startMinutes = startMinutes.trim();
                startSuffix = startSuffix.trim();

                if (startSuffix == 'pm' || startSuffix == 'PM') {
                    startHours = startHours != '12' ? parseInt(startHours) + parseInt(12) : startHours;
                }

                var endHours = userEndTime.split(':')[0];
                var endMinutes = userEndTime.split(':')[1];
                endMinutes = endMinutes.slice(0, -2)
                var endSuffix = userEndTime.split(' ')[3];

                endHours = endHours.trim();
                endMinutes = endMinutes.trim();
                endSuffix = endSuffix.trim();

                if (endSuffix == 'pm' || endSuffix == 'PM') {
                    endHours = endHours != '12' ? parseInt(endHours) + parseInt(12) : endHours;
                }
            }

            userStartTime = startHours + ":" + startMinutes + ":" + "00";
            userEndTime = endHours + ":" + endMinutes + ":" +"00";
            $('input[name="scheduledStartTime"]').val(userStartTime);

            $('input[name="scheduledEndTime"]').val(userEndTime);

            formdata.append('scheduledStartTimeFormatted', userStartTime);
            formdata.append('scheduledEndTimeFormatted', userEndTime);


        }

        if ($('[type="submit"]').attr('name') == 'submitNewCustomer') {
            $.ajax({
                url         : window.fx_url.API + 'customers',
                data        : formdata ? formdata : form.serialize(),
                cache       : false,
                contentType : false,
                processData : false,
                type        : 'POST',

                success: function (response) {
                    $('#loading-image').hide();
                    if (response.result == "true"){
                        window.location.href = window.fx_url.BASE + 'projects/' + response.projectID;
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to create customer, please contact support');
                    $('#loading-image').hide();
                }
            });
        } else if ($('[type="submit"]').attr('name') == 'submitNewProperty'){
            $.ajax({
                url         : window.fx_url.API + 'properties',
                data        : formdata ? formdata : form.serialize(),
                cache       : false,
                contentType : false,
                processData : false,
                type        : 'POST',

                success: function (response) {
                    $('#loading-image').hide();
                    window.location.href = window.fx_url.BASE + 'projects/' + response.projectID;
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to create property, please contact support');
                    $('#loading-image').hide();
                }
            });
        } else if ($('[type="submit"]').attr('name') == 'submitNewProject'){

            $.ajax({
                url         : window.fx_url.API + 'projects',
                data        : formdata ? formdata : form.serialize(),
                cache       : false,
                contentType : false,
                processData : false,
                type        : 'POST',

                success: function (response) {
                    $('#loading-image').hide();
                    window.location.href = window.fx_url.BASE + 'projects/' + response.projectID;
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to create project, please contact support');
                    $('#loading-image').hide();
                }
            });
        }
    }

    $('#submitNewCustomer, #submitNewProperty, #submitNewProject').on('click', function () {
        if ($('[type="submit"]').attr('name') == 'submitNewCustomer') {
            validate();

        } else if ($('[type="submit"]').attr('name') == 'submitNewProperty'){
            var addProperty = false;
            $('input[required], select[required]').each(function(){
                if (!$(this).is(':disabled')){
                    if ($.trim( $(this).val() ) == '') {
                        $(this).parent().addClass('is-invalid-label');
                        $(this).addClass('is-invalid-input');
                        $(this).parent().find('.form-error').addClass('is-visible');
                        addProperty = false;
                        return false;
                    } else {
                        $(this).parent().removeClass('is-invalid-label');
                        $(this).removeClass('is-invalid-input');
                        $(this).parent().find('.form-error').removeClass('is-visible');
                        addProperty = true;
                    }
                }
            });

            var projectDescription = $('select[name="projectDescription"]');

            if (addProperty == true) {
                if (projectDescription.length >= 1){
                    addProperty = checkProjectDescriptionSelect();
                } else {
                    addProperty = true;
                }
            }

            if (addProperty){
                submitData();
            }

        }
        else if ($('[type="submit"]').attr('name') == 'submitNewProject'){
            var addProject = false;
            var projectDescriptionSelect = $('select[name="projectDescription"]');
            if (projectDescriptionSelect.length >= 1){
                addProject = checkProjectDescriptionSelect();
            } else {
                if ($.trim( $('[name="projectDescription"]').val() ) == '') {
                    $('[name="projectDescription"]').parent().addClass('is-invalid-label');
                    $('[name="projectDescription"]').addClass('is-invalid-input');
                    $('[name="projectDescription"]').parent().find('.form-error').addClass('is-visible');
                    addProject = false;
                    return false;
                } else {
                    $('[name="projectDescription"]').parent().removeClass('is-invalid-label');
                    $('[name="projectDescription"]').removeClass('is-invalid-input');
                    $('[name="projectDescription"]').parent().find('.form-error').removeClass('is-visible');
                    addProject = true;
                }
            }

            if (addProject) {
                submitData();
            }
        } else {
            console.log('none selected');
        }
        return false;
    });

    $('#useComplete').click(function (){
        var geocodeAddress = hiddenMap.address[0];
        $('input[name="address"]').val(geocodeAddress.address_full.address);
        $('input[name="city"]').val(geocodeAddress.address_full.city);
        $('select[name="state"]').val(geocodeAddress.address_full.state);
        $('input[name="zip"]').val(geocodeAddress.address_full.zip);
        $('#latLongModal').foundation('close');
        submitData();
    });

    $('#usePartial').click(function (){
        $('#latLongModal').foundation('close');
        submitData();
    });

    //handlers for the buttons on the modal #saveDontSchedule
    var saveDontSchedule = function (){
        //For Save without Schedule Modal  FXLRATR-254
        //if the user doesn't want to schedule anything immediately
        //this is the option (s)he will choose
        $('#confirmSaveUnscheduled').foundation('close');
        scheduleModalShown = true;
        checkAddress();
    };

    var dontSaveWithoutSchedule = function (){
        //For Save without Schedule Modal  FXLRATR-254
        //this option is selected when the user wishes to
        //schedule an appointment
        $('#confirmSaveUnscheduled').foundation('close');
        $("input:text:visible:first").focus();
        scheduleModalShown = true;
    };
    // end handlers for buttons on the modal #saveDontSchedule

    var saveWithDuplicates = function (){
        $('#confirmSaveDuplicates').foundation('close');
        duplicatesModalShown = true;
        continueValidate();
    };

    var dontSaveWithDuplicates = function (){
        var offset = $('.duplicateResult').offset();
        window.scroll(0, offset.top / 2);

        $('#confirmSaveDuplicates').foundation('close');
        duplicatesModalShown = true;
    };

    $('#addNew').click(addNewCustomer);
    $('.addPhone').click(addPhoneRow);
    $('#emailTable .addEmail').click(addEmailRow);
    $('.deletePhone').click(deletePhoneRow);
    $('#cancelAddCustomer').click(cancelCustomer);
    $('input.isPrimary').change(isPrimaryCheck);
    $('#address, #city, #state, #zip').change(function () { $('#latitude, #longitude').val('');  mapAddress(); mapLatLong(); });

    $('#saveDontSchedule').click(saveDontSchedule); //handles 'yes' button on the confirm modal
    $('#dontSaveWithoutSchedule').click(dontSaveWithoutSchedule); //handles 'no' button on the confirm modal

    $('#saveWithDuplicates').click(saveWithDuplicates);
    $('#dontSaveWithDuplicates').click(dontSaveWithDuplicates);

    $('#today').click(function () {
        $('#calendar').fullCalendar('today')
    });

    $('#closeNoResourcesModal').click(function () {
        $('#noResourcesModal').foundation('close');
        window.location.href = window.fx_pages.CUSTOMERS;
    });

    $('#dailyView').click(function () {
        $('#resourceWeekView, #monthlyView').removeClass('active');
        $('#dailyView').addClass('active');
        $('#calendar').fullCalendar('changeView', 'timelineDay');
    });

    $('#resourceWeekView').click(function () {
        $('#dailyView, #monthlyView').removeClass('active');
        $('#resourceWeekView').addClass('active');
        $('#calendar').fullCalendar('changeView', 'resourceWeek');
    });

    $('#monthlyView').click(function () {
        $('#dailyView, #resourceWeekView').removeClass('active');
        $('#monthlyView').addClass('active');
        $('#calendar').fullCalendar('changeView', 'month');
    });

    $('#previous').click(function () {
        $('#calendar').fullCalendar('prev')
    });

    $('#next').click(function () {
        $('#calendar').fullCalendar('next')
    });

    $('#addEvent').on('closed.zf.reveal', function () {
        $('#addEvent').parent().appendTo("#calendarShow");
    });

    $(function () {
        $(".datepickerFrom").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerTo").datepicker("option", "minDate", selectedDate);
                $('#scheduledEndDate').trigger('change');
            }
        });
        $(".datepickerTo").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerFrom").datepicker("option", "maxDate", selectedDate);
            }
        });
    });

    $('[name="email"]').on('blur.fx', function() {
        var email = $(this);
        var emailValue = email.val().trim();
        if (emailAddress !== emailValue) {
            emailAddress = emailValue ? emailValue : null;
            if (emailAddress) {
                validateEmailAddress(email);
            }
        }
    });

    $('input[required], select[required]').on('change.fx', function() {
        if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').not('.validEmail').addClass('is-visible');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').not('.validEmail').removeClass('is-visible');
        }
    });

    $('[data-dismiss-email]').on('click.fx', function() {
        var email = $('[name="email"]');
        email.parent().removeClass('is-invalid-label');
        email.removeClass('is-invalid-input');
        email.parent().find('.validEmail').removeClass('is-visible');
    });
});

var checkDuplicateCustomers = function() {
    var businessName = $('input[name="businessName"]').val();
    var firstName = $('input[name="firstName"]').val();
    var lastName = $('input[name="lastName"]').val();
    var address = $('input[name="address"]').val();
    var address2 = $('input[name="address2"]').val();
    var city = $('input[name="city"]').val();
    var state = $('select[name="state"]').val();
    var zip = $('input[name="zip"]').val();
    var email = $('input[name="email"]').val();
    var phone = $('input[name="isPrimary[]"]:checked').parents('tr').find('input[name="phone[]"]').val();

    var duplicateResult = $('.duplicateResult');

    if (firstName !== '' && lastName !== '' && address !== '' && city !== '' && state !== '' && zip !== '' && phone != '' && phone !== undefined) {
        $.ajax({
            url: window.fx_url.API + 'customer/duplicates',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                businessName: businessName,
                firstName: firstName,
                lastName: lastName,
                address: address,
                address2: address2,
                city: city,
                state: state,
                zip: zip,
                email: email,
                phone: phone
            },
            success: function (response) {
                if (response.status === 1) {
                    if (response.result.length > 0) {
                        duplicateSearchResult = response.result;
                        duplicateResult.slideDown("500");

                        $('.viewDuplicateResult').click(showDuplicateResult);
                    } else {
                        duplicateResult.hide();
                    }
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    }
};

var showDuplicateResult = function() {
    if (duplicateSearchResult != null) {
        var results = duplicateSearchResult;

        var modal = $('#duplicateResultModal');
        var table = modal.find('tbody');
        table.empty();
        for (i = 0; i < results.length; ++i) {
            var item = results[i];

            // type
            var type = '';
            var link = '';
            switch (item.type) {
                case 1:
                    type = 'Customer';
                    link = fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', item.customerID);
                    break;
                case 2:
                    type = 'Property';
                    link = fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', item.customerID);
                    break;
                case 3:
                    type = 'Lead';
                    link = fx_pages.LEAD.replace('{lead_id}', item.id);
                    break;
            }
            // business name
            var businessName = '';
            if (item.businessName != null) {
                businessName = item.businessName;
            }

            // address
            var address = '';
            if (item.address != null) {
                address = item.address;
            }

            // address 2
            if (item.address2 != null) {
                address = address + ', ' + item.address2;
            }

            // city
            var city = '';
            if (item.city != null) {
                city = item.city;
            }

            // state
            var state = '';
            if (item.state != null) {
                state = item.state;
            }

            // zip
            var zip = '';
            if (item.zip != null) {
                zip = item.zip;
            }

            // email
            var email = '';
            if (item.email != null) {
                email = item.email;
            }

            // phone
            var phone = '';
            if (item.phoneNumber != null) {
                phone = item.phoneNumber;
            }

            table.append('<tr><td>'+type+'</td><td>'+item.firstName+' '+item.lastName+'</td><td>'+businessName+'</td><td>'+address+'</td><td>'+city+'</td><td>'+state+'</td><td>'+zip+'</td><td>'+email+'</td><td>'+phone+'</td><td><a target="_blank" href="'+link+'" class="button">View</a> </td></tr>')
        }
        modal.foundation('open');
    }
};

var checkSalesperson = function() {

    if ($('select[name="projectSalesperson"]').val() != '' && $('#scheduleSalesperson').val() != '') {

        if ($('select[name="projectSalesperson"]').val() != $('#scheduleSalesperson').val()) {
            $('select[name="projectSalesperson"]').parent().find('.form-warning').show();
        } else {
            $('select[name="projectSalesperson"]').parent().find('.form-warning').hide();
        }
    }
};

var addNewCustomer = function () {

    var userID = $('#userID').text();

    if ($('select[name="projectSalesperson"]').has('option[value="' + userID + '"]').length > 0)
        $('select[name="projectSalesperson"] > option[value="' + userID + '"]').prop('selected', true);

    if ($('#propertyID').val() != '') {
        var propertyID = $('#propertyID').val();
        window.history.replaceState(null, '', 'customer-add.php?pid='+propertyID);
    } else if ($('#customerID').val() != '') {
        var customerID = $('#customerID').val();
        window.history.replaceState(null, '', 'customer-add.php?cid='+customerID);
    } else if ($('#leadUUID').val() != '') {
        var leadUUID = $('#leadUUID').val();
        window.history.replaceState(null, '', 'customer-add.php?lid='+leadUUID);
        if ($('#assignedToUserID').val() != ''){
            $('#projectSalesperson').find('option[value="' + $('#assignedToUserID').val() + '"]').prop('selected', true);
        }
        if ($('#address').val() !== '' && $('#city').val() !== '' && $('#state').val() !== '' && $('#zip').val() !== '') {
            $('#address, #city, #state, #zip').trigger('change');
        }
    } else {
        window.history.replaceState(null, '', 'customer-add.php')
    }

    $('#existing').removeClass('active');
    $('#addNew').addClass('active');

    if (customer_add_info.add_phone_row == '1') {

        oldRow = $('#phoneTable').find('tbody > tr:last-child input.isPrimary').attr('value');
        var newRow = parseFloat(oldRow) + 1;

        $cloneBlankRow = $('#phoneTable').find('tbody tr:first').clone().css('display', '');
        $('#phoneTable').find('tbody').append($cloneBlankRow);
        $('#phoneTable').find('tbody > tr:last-child input.isPrimary').prop('checked', 'true');
        $('#phoneTable').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheck);
        $('#phoneTable').find('tbody > tr:last-child .delete > a').click(deletePhoneRow);
        $('#phoneTable').find('tbody > tr:last-child input.phone').change(checkDuplicateCustomers);
    }

    addEmailRow();
};

var addPhoneRow = function () {

    oldRow = $('#phoneTable').find('tbody > tr:last-child input.isPrimary').attr('value');
    var newRow = parseFloat(oldRow) + 1;

    $cloneBlankRow = $('#phoneTable').find('tbody tr:first').clone().css('display', '');
    $('#phoneTable').find('tbody').append($cloneBlankRow);
    $('#phoneTable').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheck);
    $('#phoneTable').find('tbody > tr:last-child .delete > a').click(deletePhoneRow);

    if (!$('input.isPrimary:checked'))
        $('#phoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
};

var deletePhoneRow = function () {

    $(this).parent().parent().remove();

    if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
        $('#phoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);

    if ($('#phoneTable tbody').children().length == 1)
        $('.addPhone').trigger('click');

};

var isPrimaryCheck = function () {

    $('#phoneTable').find('input.isPrimary').prop('checked', false);
    $(this).prop('checked', true);

};

var addEmailRow = function () {
    $cloneBlankRow = $('#emailTable').find('tbody tr:first').clone().css('display', '');
    $cloneBlankRow.find('a.deleteEmail').click(deleteEmailRow);
    $cloneBlankRow.find('input[name="contactEmail"]').on('blur.fx', function() {
        var email = $(this);
        var emailValue = email.val().trim();
        if (emailValue) {
            validateEmailAddress(email);
        }
    });

    $cloneBlankRow.find('[data-dismiss-contact-email]').on('click.fx', function() {
        var $this = $(this);
        var invalidEmailInput = $this.parent().parent().find('[name="contactEmail"]');
        var contactRow = $('#emailTable').find('.validEmail.is-visible').parent().parent();
        contactRow.removeClass('is-invalid-input');
        invalidEmailInput.removeClass('is-invalid-input');
        invalidEmailInput.parent().removeClass('is-invalid-label');
        $('#emailTable').parent().find('.contactEmail').removeClass('is-visible');
        $this.parent().removeClass('is-visible');
    });

    $('#emailTable').find('tbody').append($cloneBlankRow);
};

var deleteEmailRow = function () {
    $(this).parent().parent().remove();

    if ($('#emailTable tbody').children().length == 1){
        $('small.contactEmail').removeClass('is-visible');
        $('small.contactName').removeClass('is-visible');
    }
};

var isEmail = function(email) {
	var re = /^[A-Za-z0-9!#$%&'*+\-/=?^_`{|}~.]+@(?:[A-Za-z0-9\-]+\.[A-Za-z0-9\-]+)+$/;
	return re.test(email);
};

var cancelCustomer = function () {

    $('#cancelCustomerModal').foundation('open');

    $('#cancelCustomerNo').click(function () {
        $('#cancelCustomerModal').foundation('close');
    });

    $('#cancelCustomerYes').click(function () {
        if ($('#propertyID').val() != '' ||  $('#customerID').val() != '' ) {
            var customerID = $('#customerID').val();
            window.location.href = "customer-management.php?cid="+customerID+"";
        }else {
            window.location.href = window.fx_pages.CUSTOMERS;

        }
    });

};

var users = {};

function moveEvent(event, delta, revertFunc, jsEvent, ui, view) {
    var eventModal = $('#addEvent');
    var calendar = $('#calendar');

    if (delta){
        eventModal.find('.button.drag').show();
        eventModal.find('.button.select').hide();

        $('#confirmAppointmentNoDrag').click(function(){
            eventModal.foundation('close');
            event.calendarBgColor = originalResource.calendarBgColor;
            revertFunc();
        });
    }

    $('#confirmAppointmentYes').data('fx.event', event);

    originalResource = $('#calendar').fullCalendar('getResourceById', event.scheduledUserID);

    var newResource = $('#calendar').fullCalendar('getResourceById', event.resourceId);

    event.calendarBgColor = newResource.calendarBgColor;

    var selectSalesperson = eventModal.find($('select[name="salesperson"]')).empty();

    for (var i in users) {
        var user = users[i];

        for (var i2 in roles) {
            var role = roles[i2];

            if (user[i2] == 1 && $.inArray(event.scheduleType, role.eventTypes) !== -1){
                selectSalesperson.append('<option value="' + user.id + '">' + user.title + '</option>');
            }
        }
    }

    eventModal.find('#modalTitle').text('Schedule ' + event.scheduleType);

    var nameDisplay;

    if (event.businessName != '') {
        nameDisplay = event.businessName;
    } else {
        nameDisplay = event.firstName + ' ' + event.lastName;
    }

    if (nameDisplay != ''){
        name = 'Appointment For ' + nameDisplay;
        eventModal.find('p.name').text(name);
    } else {
        eventModal.find('p.name').text('Add Appointment');
    }

    eventModal.find('.scheduledTitle').text('Salesperson');


    //set Start Date & Time
    var startDate = moment(event.start).format("MM/DD/YYYY");
    var startTime = moment(event.start).format("HH:mm");
    var startTimeAMPM = moment(event.start).format("h : mm A");
    var startTimeString = startTime.toString();

    var StartTimeOptions ={
        now: startTimeString,
        twentyFour: false,  //Display 24 hour format, defaults to false
        upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
        downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
        close: 'wickedpicker__close', //The close class selector to use, for custom CSS
        hoverState: 'hover-state', //The hover state class to use, for custom CSS
        title: "Select a time", //The Wickedpicker's title,
        showSeconds: false, //Whether or not to show seconds,
        secondsInterval: 1, //Change interval for seconds, defaults to 1,
        minutesInterval: 5, //Change interval for minutes, defaults to 1
        beforeShow: null, //A function to be called before the Wickedpicker is shown
        show: function(){
            var modal = $('input[name="scheduledStartTime"]').closest('#addEvent');
            var datePicker = $('body').find('.wickedpicker');
            if(!modal.length) {
                $(datePicker).css('z-index', 'auto');
                return;
            }
            var zIndexModal = $(modal).css('z-index');
            $(datePicker).css('z-index', zIndexModal + 1);
        }, //A function to be called when the Wickedpicker is shown
        clearable: false //Make the picker's input clearable (has clickable "x")
    };

    //set End Date & Time
    var endDate = moment(event.end).format("MM/DD/YYYY");
    var endTime = moment(event.end).format("HH:mm");
    var endTimeAMPM = moment(event.end).format("h : mm A");
    var endTimeString = endTime.toString();
    var EndTimeOptions ={
        now: endTimeString,
        twentyFour: false,  //Display 24 hour format, defaults to false
        upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
        downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
        close: 'wickedpicker__close', //The close class selector to use, for custom CSS
        hoverState: 'hover-state', //The hover state class to use, for custom CSS
        showSeconds: false, //Whether or not to show seconds,
        secondsInterval: 1, //Change interval for seconds, defaults to 1,
        minutesInterval: 5, //Change interval for minutes, defaults to 1
        beforeShow: null, //A function to be called before the Wickedpicker is shown
        show: function(){
            var modal = $('input[name="scheduledEndTime"]').closest('#addEvent');
            var datePicker = $('body').find('.wickedpicker');
            if(!modal.length) {
                $(datePicker).css('z-index', 'auto');
                return;
            }
            var zIndexModal = $(modal).css('z-index');
            $(datePicker).css('z-index', zIndexModal + 1);
        }, //A function to be called when the Wickedpicker is shown
        clearable: false, //Make the picker's input clearable (has clickable "x")
    };

    if (is_idevice) {
        $('input[name="scheduledStartTime"]').val(startTime);
        $('input[name="scheduledEndTime"]').val(endTime);
    } else {

        $('#scheduledStartTime').wickedpicker(StartTimeOptions);
        $('#scheduledStartTime').val(startTimeAMPM);
        $('#scheduledEndTime').wickedpicker(EndTimeOptions);
        $('input[name="scheduledEndTime"]').val(endTimeAMPM);
    }

    $('input[name="scheduledStartDate"]').val(startDate);
    $('input[name="scheduledEndDate"]').val(endDate);

    //salesperson info
    selectSalesperson.find("option[value='" + event.resourceId + "']").prop('selected', true);

    if (new Date($('input[name="scheduledStartDate"]').val()) < today) {
        if (eventModal.find('.callout.small.alert').length < 1){
            eventModal.append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
        } else {
            eventModal.find('.callout.small.alert p').text('Please note that one of the selected dates is in the past.');
        }
    } else {
        eventModal.find('.callout.small.alert').remove();
    }

    $('#confirmAppointmentNoSelect').click(function(){
        var tempID = eventModal.find('input[name="tempID"]').val();
        calendar.fullCalendar('removeEvents', tempID);
        calendar.fullCalendar('unselect');

        $('.appointment-date').empty();
        $('.appointment-time').empty();
        $('.appointment-salesperson').empty();

        eventModal.find('input[name="tempID"]').val('');

        eventModal.find('select[name="salesperson"]').val('');
        eventModal.find('input[name="scheduledStartDate"]').val('').removeClass("required").removeClass("is-invalid-input");
        eventModal.find('input[name="scheduledStartTime"]').val('').removeClass("required").removeClass("is-invalid-input");
        eventModal.find('input[name="scheduledEndDate"]').val('').removeClass("required").removeClass("is-invalid-input");
        eventModal.find('input[name="scheduledEndTime"]').val('').removeClass("required").removeClass("is-invalid-input");

        eventModal.find('#startDateErr').hide();
        eventModal.find('#startTimeErr').hide();
        eventModal.find('#endDateErr').hide();
        eventModal.find('#endTimeErr').hide();

        $('[name="sendIntroEmail"]').prop('checked', false);
        $('.introEmail').show();

        eventModal.foundation('close');
    });

    eventModal.foundation('open');

}

var filterCalendar = function (defaultView) {

    var calendar = $('#calendar');
    var calendarDelay = null;

    calendar.fullCalendar('destroy');
    calendar.fullCalendar('render');

    $('.dashboard-filter-bar').show();

    calendar.fullCalendar({
        header: {
            left: '',
            center: '',
            right: ''
        },
        views: {
            resourceWeek: {
                type: 'timelineDay',
                duration: { weeks: 1 },
                slotDuration: '24:00:00',
                slotLabelFormat: 'ddd M/D'
            },
            timelineDay: {
                slotDuration: '00:15:00',
                slotLabelFormat: 'hA'
            },
            month: {
                eventLimit: 3
            }
        },
        loading: function(bool) {
            if (!bool) {
                if ($('#confirmAppointmentYes').data('fx.event') != null) {
                    var newEvent = $('#confirmAppointmentYes').data('fx.event');

                    eventData = {
                        title: newEvent.title,
                        firstName: newEvent.firstName,
                        lastName: newEvent.lastName,
                        businessName: newEvent.businessName,
                        start: newEvent.start,
                        end: newEvent.end,
                        resourceId: newEvent.resourceId,
                        scheduledUserID: newEvent.resourceId,
                        address: newEvent.address,
                        city: newEvent.city,
                        state: newEvent.state,
                        zip: newEvent.zip,
                        calendarBgColor: newEvent.calendarBgColor,
                        scheduleType: newEvent.scheduleType
                    };

                    calendar.fullCalendar('renderEvent', eventData, true);

                    var clientEvents = calendar.fullCalendar('clientEvents');
                    var lastEvent = clientEvents[clientEvents.length - 1];
                    $('input[name="tempID"]').val(lastEvent._id);
                }
            }
        },
        slotDuration: '00:15:00',
        slotLabelFormat: 'hA',
        scrollTime: '08:00:00',
        defaultView: defaultView,
        aspectRatio: 1.8,
        defaultDate: current_date,
        fixedWeekCount: false,
        selectable: true,
        selectAllow: function(selectInfo) {
            if (selectInfo.resourceId) {

                if (selectInfo.resourceId == 0) {
                    return false;
                } else {
                    var newResource = $('#calendar').fullCalendar('getResourceById', selectInfo.resourceId);
                    var allowDrop = false;

                    for (var i in roles) {
                        var role = roles[i];

                        if (newResource[i] == 1 && $.inArray('Evaluation', role.eventTypes) !== -1) {
                            allowDrop = true;
                            break;
                        }
                    }

                    return allowDrop;
                }
            }
        },
        eventAllow: function(dropLocation, draggedEvent) {
            if (dropLocation.resourceId == null){
                dropLocation.resourceId = draggedEvent.resourceId;
            }
            if (dropLocation.resourceId == 0){
                return false;
            } else {
                var newResource = $('#calendar').fullCalendar('getResourceById', dropLocation.resourceId);
                var allowDrop = false;

                for (var i in roles) {
                    var role = roles[i];

                    if (newResource[i] == 1 && $.inArray('Evaluation', role.eventTypes) !== -1){
                        allowDrop = true;
                        break;
                    }
                }

                return allowDrop;
            }
        },
        eventDrop: moveEvent,
        eventResize: moveEvent,
        selectHelper: true,
        unselectAuto: false,
        editable: true,
        eventLimit: true,
        eventOverlap: true,
        eventColor: '#f6f7fb',
        businessHours: customer_add_info.business_hours,
        resourceLabelText: ' ',
        resources: function (callback) {
            $.ajax({
                url: window.fx_url.API + 'calendar/resources',
                dataType: 'json',
                data: {
                    filter: getFilterUsers()
                },
                success: function (response) {
                    callback(response.result);
                    if ($('#scheduleModalDD select > option').length <= 1) {
                        $('#scheduleModalDD select').empty();
                        $.each(response.result, function (i, item) {
                            if (item.id != '0') {
                                users[item.id] = item;
                            }
                        });
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        },
        dayClick: function (date, jsEvent, view) {
            var date = date.format();
            var coordinates = jsEvent.pageX + ',' + jsEvent.pageY;
            var view = view.name;

            if (view != 'timelineDay') {
                $('#resourceWeekView, #monthlyView').removeClass('active');
                $('#dailyView').addClass('active');
                calendar.fullCalendar('changeView', 'timelineDay');
                calendar.fullCalendar('gotoDate', date);
            }
        },
        select: function (start, end, jsEvent, view, resource) {
            var view = view.name;
            var eventModal = $('#addEvent');

            if (view == 'timelineDay') {

                if (eventModal.find('input[name="tempID"]').val() != '') {

                    //Get Temporary ID
                    var tempID = eventModal.find('input[name="tempID"]').val();

                    //Remove Last Rendered Event
                    calendar.fullCalendar('removeEvents', tempID);
                    eventModal.find('input[name="tempID"]').val('');
                }

                var address, city, state, zip;

                address = $('input[name="address"]').val();
                city = $('input[name="city"]').val();
                state = $('select[name="state"]').val();
                zip = $('input[name="zip"]').val();

                //Render Event
                var eventData;
                var firstName = $('input[name="firstName"]').val();
                var lastName = $('input[name="lastName"]').val();
                var businessName = $('input[name="businessName"]').val();
                var titleDisplay = firstName + ' ' + lastName + ' Appointment';
                if (businessName != ''){
                    titleDisplay = businessName + ' Appointment';
                }

                var newResource = $('#calendar').fullCalendar('getResourceById', resource.id);

                var calendarBgColor = newResource.calendarBgColor;

                eventData = {
                    title: titleDisplay,
                    firstName: firstName,
                    lastName: lastName,
                    businessName: businessName,
                    start: start,
                    end: end,
                    resourceId: resource.id,
                    scheduledUserID: resource.id,
                    address: address,
                    city: city,
                    state: state,
                    zip: zip,
                    calendarBgColor: calendarBgColor,
                    scheduleType: 'Evaluation'
                };

                calendar.fullCalendar('renderEvent', eventData, true);

                //Get Temporary Event ID
                var clientEvents = calendar.fullCalendar('clientEvents');
                var lastEvent = clientEvents[clientEvents.length - 1];
                var lastId = lastEvent._id;
                eventModal.find('input[name="tempID"]').val(lastId);

                eventModal.find('.button.drag').hide();
                eventModal.find('.button.select').show();

                moveEvent(lastEvent);

            }
        },
        events: function (start, end, timezone, callback) {
            var start = moment(start).format("YYYY/MM/DD");
            var end = moment(end).format("YYYY/MM/DD");

            if (calendarDelay !== null) {
                clearTimeout(calendarDelay);
            }

            calendarDelay = setTimeout(function(){
                calendarDelay = null;
                getEvents(start, end, timezone, callback);
            }, 800);
        },
        eventAfterAllRender: function (view) {
            if ($('.fc-toolbar').length > 0 && view.name === 'timelineDay'){

                if ($('#calendarMap').length === 0) {
                    $('#scheduleCalendar .row.expanded').before('<div id="calendarMap"></div>');
                    console.log('added calendar div');
                } else {
                    $('#calendarMap').show();
                    console.log('show calendar div');
                }
                initializeMap();
            } else {
                $('#calendarMap').hide();
            }

            var containerHeight = $(window).height() - $('.footer').height() - $('.top-bar').height();
            var dashboardHeight = $('#calendarMap').outerHeight() + $('#customerButtons').height() + $('#calendarShow .medium-12.columns:first-child').outerHeight();

            var calendarGroupHeight = $('#calendar').outerHeight() + $('.dashboard-filter-bar').outerHeight();
            var filterGroup = $('#filterGroup');
            var filterGroupHeader = $('#filterGroup .header');
            var filterGroupList = $('#filterGroup .list');

            if ((containerHeight - dashboardHeight) < calendarGroupHeight){
                var height = calendarGroupHeight - 3;
                filterGroup.height(height);
                filterGroupList.height(height - filterGroupHeader.outerHeight());
            } else {
                var height = containerHeight - dashboardHeight - 3;
                filterGroup.height(height);
                filterGroupList.height(height - filterGroupHeader.outerHeight());
            }

        },
        eventRender: function (event, element) {

            if (event.id != null){
                event.editable = false;
            }

            if (event.eventType == 3) {
                $('select#filterResources').find('option[value="0"]').show();
                $('tr[data-resource-id="0"] .fc-widget-content').show();
            }

            var fullUserColor = $('#fullUserColor').val();

            //Decode customer name on calendar
            var name = (element.find('.fc-title').text());
            if (name != '') { element.find('.fc-title').text(decodeEntities(name)); }


            if (event.rendering == 'background') {
                element.append(event.title);
            }

            if (event.start == null || event.end == null) return;
            event.end.local();
            event.start.local();

            var view = calendar.fullCalendar('getView');
            var eventAddressDisplay2 = '';
            if (event.address2 != null && event.address2 != ""){
                eventAddressDisplay2 = ' ' + event.address2;
            }

            if (view.name === 'timelineDay') {
                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 4.2rem;float: left;margin: .2rem 0 0 3px;"></div>');
                }

                if (event.id == null) {
                    element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                    element.find('.fc-title, span.fc-time').css('color', '#000000');
                }

                if (event.eventType == 1) {
                    if (parseFloat(event.latitude) === 0 && parseFloat(event.longitude) === 0){
                        if (event.scheduleType == 'Installation'){
                            element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation (Location Not Mapped): ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>');
                        }
                        if (event.scheduleType == 'Evaluation'){
                            element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation (Location Not Mapped)</span>');
                        }
                        element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                    else{
                        if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation: ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>'); }
                        if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation</span>'); }

                        element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                } else if (event.eventType == 2 || event.eventType == 3) {
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            }

            else if (view.name === 'month') {
                element.find('span.fc-time').prependTo(element.find('.fc-content .fc-title'));

                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem .3rem 0 3px;"></div>');
                }

                if (event.id == null) {
                    element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                    element.find('.fc-title, span.fc-time').css('color', '#000000');
                }

                if (event.eventType == 1) {
                    if (event.allDay == '1') {
                        element.find('.fc-title').append('<span class="fc-title-address"> ' + event.address + ' ' + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                    if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">I:' + ' ' + event.projectDescription +'</span>'); }
                    if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">E:' + ' ' + event.projectDescription +'</span>'); }

                } else if (event.eventType == 2 || event.eventType == 3) {
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            }

            else if (view.name === 'resourceWeek') {

                element.find('.fc-title').empty();

                if (element.find('.fc-content span.fc-time').length != 1){
                    element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                } else {
                    element.find('.fc-time').remove();
                    element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                }

                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem 0 0 3px;"></div>');
                }
                if (event.id == null) {
                    element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                    element.find('.fc-title, span.fc-time').css('color', '#000000');
                }
                element.find('.fc-content').parent().addClass('resource-week');

                if (event.eventType == 1) {
                    element.find('.fc-title').append('<span class="fc-title-address">' + event.city + ", " + event.state + '</span>');

                    if (event.scheduleType == 'Installation') {

                        element.find('.fc-title').append('<br/><span class="fc-title-type">I: '+ event.projectDescription +'</span>');

                        if (moment(event.start._d).format('h:mm') == '12:00' && (moment(event.end._d).format('h:mm') == '12:00' || moment(event.end._d).format('h:mm') == '11:59')) {
                            element.find('span.fc-time').html('All Day');
                        } else {
                            element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                        }
                    } else if  (event.scheduleType == 'Evaluation') {
                        element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                        element.find('.fc-title').append('<br/><span class="fc-title-type">E: '+ event.projectDescription +'</span>');
                    }

                } else if (event.eventType == 2 || event.eventType == 3) {
                    element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            }

        },
        viewRender: function (view, element) {
            var moment = calendar.fullCalendar('getDate');
            var momentFormat = moment.format();

                current_date = momentFormat;

            if (view.name === 'timelineDay') {
                var momentFormat = moment.format('dddd MMMM D, YYYY');
                $('#calendarTitle').text(momentFormat);

                $('.fc-expander-space').remove();

                calendar.fullCalendar('option', 'contentHeight', 'auto');


            } else if (view.name === 'agendaWeek' || view.name === 'resourceWeek') {
                var viewStart = view.intervalStart.format('MMMM D');
                var viewEnd = view.intervalEnd.format('MMMM D YYYY');

                var newdate = new Date(viewEnd);
                newdate.setDate(newdate.getDate() - 1); // minus the datez
                var viewEndNew = new Date(newdate);
                var viewEndNewFormat = $.datepicker.formatDate('MM d', viewEndNew);

                $('#calendarTitle').text(viewStart + ' - ' + viewEndNewFormat);

                calendar.fullCalendar('option', 'contentHeight', '');
                $('#calendarMap').hide();
            } else if (view.name === 'month') {
                var momentFormat = moment.format('MMMM YYYY');
                $('#calendarTitle').text(momentFormat);

                calendar.fullCalendar('option', 'contentHeight', '');
                $('#calendarMap').hide();
            }
        },
        resourceRender: function (resourceObj, labelTds, bodyTds) {
            var role = resourceObj.installation == '1' ? 'Installation' : '';
            if (resourceObj.sales == '1') {
                if (role.length > 0) role += ', ';
                role += 'Sales';
            }
            labelTds.find('.fc-cell-content').append($('<span class="fc-cell-role"></span>').text(role));

            calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content').before('<div style="width: .5rem;background-color:' + resourceObj.calendarBgColor + ';height: 4.2rem;float: left;margin-left: 3px;margin-top: 0.4rem;"></div>');

            if (resourceObj.id == 0) {
                labelTds.find('.fc-cell-content .fc-cell-role').text('Company Calendar');
                //labelTds.hide();
                //bodyTds.hide();
            }
        }
    });

};

var getEvents = function (start, end, timezone, callback) {
    $('#loading-image').show();
    $.ajax({
        url: 'getEvents.php',
        dataType: 'json',
        data: {
            filter: getFilterUsers(),
            start: start,
            end: end
        },
        success: function (doc) {
            $('#loading-image').hide();
            if ($('.fc-toolbar').length > 0) {
                var events = eval(doc);
                callback(events);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var submitButton = $('#submitNewCustomer'),
    validatingEmailMessage = $('.validating-email-message'),
    abortValidation = $('.abort-validation'),
    validationRequest = [],
    activeRequests = 0,
    abortTimer = null;
abortValidation.on('click.fx', function() {
    for (var idx in validationRequest) {
        if (validationRequest[idx] === null) {
            continue;
        }
        validationRequest[idx].abort();
    }
});
function validateEmailAddress(email) {
    if (activeRequests === 0) {
        submitButton.prop('disabled', true);
        validatingEmailMessage.show();
        abortValidation.hide();
    }

    if (abortTimer !== null) {
        clearTimeout(abortTimer);
    }
    abortTimer = setTimeout(function () {
        abortValidation.show();
        abortTimer = null;
    }, 10000);

    var requestIdx = validationRequest.length;
    activeRequests++;
    validationRequest[requestIdx] = $.ajax({
        url:  window.fx_url.API + 'service/email-validation',
        dataType: 'json',
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            email: email.val()
        }
    })
        .done(function (response) {
            switch(response.status) {
                // valid
                case 1:
                    email.parent().removeClass('is-invalid-label');
                    email.removeClass('is-invalid-input');
                    email.parent().find('.validEmail').removeClass('is-visible');
                    break;
                // invalid
                case 2:
                // unknown
                case 3:
                    email.parent().addClass('is-invalid-label');
                    email.addClass('is-invalid-input');
                    email.parent().find('.validEmail').addClass('is-visible');
                    break;
            }
        })
        .fail(function (jqXHR, textStatus, errorThrown) {
            email.parent().removeClass('is-invalid-label');
            email.removeClass('is-invalid-input');
            email.parent().find('.validEmail').removeClass('is-visible');
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        })
        .always(function () {
            validationRequest[requestIdx] = null;
            activeRequests--;
            if (activeRequests === 0) {
                submitButton.prop('disabled', false);
                validatingEmailMessage.hide();
                abortValidation.hide();
                // if all requests are done, then we stop the abort timer if it's still active
                if (abortTimer !== null) {
                    clearTimeout(abortTimer);
                    abortTimer = null;
                }
            }
        });
}

$(function () {
    $('.phone').mask('(*************');
});
autosize(document.querySelectorAll('textarea'));