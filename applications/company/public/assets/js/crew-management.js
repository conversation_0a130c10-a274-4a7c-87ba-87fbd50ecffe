window.Layout.setTitle('Crew Management');

var decodeEntities = function (encodedString) {
    var textArea = document.createElement('textarea');
    textArea.innerHTML = encodedString;
    return textArea.value;
};

var downloadReport = function() {
    var weekStart = $('#calendar').fullCalendar('getView').dayGrid.dayDates[0].format('YYYY-MM-DD');
    window.open('time-report.php?weekStart=' + weekStart, '_blank');
};

var approveAll = function() {
    var timecardDates = [];
    var days = $('#calendar').fullCalendar('getView').dayGrid.dayDates;
    for (i = 0; i < days.length; i++){
        timecardDates.push(days[i].format('YYYY-MM-DD'));
    }
    $('.clockIconWeek').each(function(){
        $(this).addClass('clicked')
        var crewmanID = $(this).parent().parent().find('.calendarName').attr('crewman');
        approveTimecardWeek(timecardDates, crewmanID, 1);
        $(this).parent().parent().find('.calendarTime').addClass('approved');
    });
};

var unapproveAll = function() {
    var timecardDates = [];
    var days = $('#calendar').fullCalendar('getView').dayGrid.dayDates;
    for (i = 0; i < days.length; i++){
        timecardDates.push(days[i].format('YYYY-MM-DD'));
    }
    $('.clockIconWeek').each(function(){
        $(this).removeClass('clicked')
        var crewmanID = $(this).parent().parent().find('.calendarName').attr('crewman');
        approveTimecardWeek(timecardDates, crewmanID, 0);
        $(this).parent().parent().find('.calendarTime').removeClass('approved');
    });
};

var getCrewmen = function(sort) {
    var viewAll = false;
    var active = $('#active').val();
    $(".calendarTable").find("tr:gt(0)").remove();
    $('.calendarDay').removeClass('today');
    $('.calendarTableDetail tr').removeClass('today');
    $(".calendarTableDetailNames").empty();

    var day1 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[0].format('YYYY-MM-DD');
    var day2 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[1].format('YYYY-MM-DD');
    var day3 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[2].format('YYYY-MM-DD');
    var day4 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[3].format('YYYY-MM-DD');
    var day5 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[4].format('YYYY-MM-DD');
    var day6 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[5].format('YYYY-MM-DD');
    var day7 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[6].format('YYYY-MM-DD');

    var days = [day1, day2, day3, day4, day5, day6, day7];

    $('loading-image').show();

    //Get Crewman Timecard Info
    $.ajax({
        url: 'getAllCrewmen.php',
        dataType: "json",
        contentType: 'application/json',
        type: "POST",
        data: {
            weekStart: day1,
            weekEnd: day7,
            sort: sort,
            active: active
        },
        contentType: "application/x-www-form-urlencoded",
        success: function(response) {
            if (response != null){
                index = 1;
                $.each(response, function (i, item) {
                    var sum = item.sum;
                    var approved = '';
                    var times = ['', '', '', '', '', '', ''];
                    var approvedDays = [];
                    var invalid = '';

                    if (item.approved != null){
                        approved = ' approved';
                    }

                    if (sum == null){
                        sum = '';
                    }
                    else{
                        sum = item.sum.substr(0, 5);
                    }

                    if (Object.keys(item.days).length <= 1){
                        invalid = ' invalid';
                    }

                    $.each(item.days, function (day, time) {
                        if (time != null) {
                            if (days.indexOf(day) != -1){
                                times[days.indexOf(day)] = time.substr(0, 5);
                            }
                        }
                    });

                    $(".calendarTable").append('<tr><td class="calendarName" crewman="' + item.crewmanID + '"><span>' + item.crewmanName + '</span><div crewman="' + item.crewmanID + '" class="personIcon"></div></td><td class="calendarTime">' + times[0] + '</td><td class="calendarTime" >' + times[1] + '</td><td class="calendarTime">' + times[2] + '</td><td class="calendarTime">' + times[3] + '</td><td class="calendarTime">' + times[4] + '</td><td class="calendarTime">' + times[5] + '</td><td class="calendarTime">' + times[6] + '</td><td class="calendarTime sum">' + sum + '</td><td class="calendarCheckbox"><div class="clockIconWeek' + invalid + '" value="0"></div></td></tr>');
                    $(".calendarTableDetailNames").append('<tr><td class="calendarName" crewman="' + item.crewmanID + '"><span>' + item.crewmanName + '</span><div crewman="' + item.crewmanID + '" class="personIcon"></div></td></tr>');

                    $.each(item.approved, function (day, time) {
                        if (time != null) {
                            approvedDays.push(day);
                            if (days.indexOf(day) != -1){
                                var row = $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().index() + 1;
                                var column = days.indexOf(day) + 2;
                                $('.calendarTable tr:nth-child(' + row + ') td:nth-child(' + column + ')').addClass('approved');
                            }
                        }
                    });
                    if (approvedDays.length == Object.keys(item.days).length - 1 && approvedDays.length != 0){ //if all days in week that have time are approved, check approved button
                        $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().find('.clockIconWeek').addClass('clicked')
                        $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().find('.clockIconWeek').val(1);
                    }
                    index += 1;
                    $('loading-image').hide();
                    if (item.invalidTime){
                        $.each(item.invalidTime, function (day, i) {
                            if (days.indexOf(day) != -1){
                                var row = $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().index() + 1;
                                var column = days.indexOf(day) + 2;
                                $('.calendarTable tr:nth-child(' + row + ') td:nth-child(' + column + ')').addClass('invalid-time');
                                $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().find('.clockIconWeek').addClass('invalid');
                                $('.calendarTable .calendarName').find('[crewman="' + item.crewmanID + '"]').parent().parent().find('.clockIconWeek').removeClass('clicked');
                            }
                        });
                    }
                });
            }
            else{
                //no crewmen
                $('.headerName').empty()
                $(".calendarTableDetailNames").append('<tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlank"></div></td></tr><tr><td class="calendarNameBlankBottom"></div></td></tr>');
            }
        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    }).done(highlightToday, removeFutureTimes);
};

var getCrewmanTimeDetail = function(crewmanID) {
    $('#currentCrewman').val(crewmanID);
    $('.calendarDetailHeader').attr('crewman', crewmanID);
    $('.clockIconDetail').removeClass('clicked');
    $('.clockIconDetail').val(0);
    $('.calendarDetailTotalHours').empty();
    $('.calendarDetailHours').empty();
    $('.calendarDetailTime').empty();
    $('.calendarDetailNotes').empty();
    $('.calendarDetailBottom .calendarName').empty();
    $('.calendarDetailBottom .calendarName').eq(0).append('<span></span>');
    $('.calendarDetailBottom .calendarName').eq(0).attr('class', 'calendarDetailBlankBottom');
    $('.calendarDetailBottom tr .calendarName').remove();
    $('.calendarDetailTime').removeClass('invalid-time');
    $('.clockIconDetail').addClass('invalid');

    var day1 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[0].format('YYYY-MM-DD');
    var day2 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[1].format('YYYY-MM-DD');
    var day3 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[2].format('YYYY-MM-DD');
    var day4 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[3].format('YYYY-MM-DD');
    var day5 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[4].format('YYYY-MM-DD');
    var day6 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[5].format('YYYY-MM-DD');
    var day7 = $('#calendar').fullCalendar('getView').dayGrid.dayDates[6].format('YYYY-MM-DD');

    var days = [day1, day2, day3, day4, day5, day6, day7];
    var index = 1;
    var times = ['00:00', '00:00', '00:00', '00:00', '00:00', '00:00', '00:00'];

    $('loading-image').show();

    //Get Crewman Timecard Info
    $.ajax({
        url: 'getCrewmanTimeDetail.php',
        dataType: "json",
        contentType: 'application/json',
        type: "POST",
        data: {
            crewmanID: crewmanID,
            weekStart: day1,
            weekEnd: day7
        },
        contentType: "application/x-www-form-urlencoded",
        success: function(response) {
            $.each(response, function (i, item) {
                if (i != 'total' && i != 'crewmanName'){
                    if (days.indexOf(i) != -1){
                        var indexIncremented = days.indexOf(i) + 1;
                        $('#day' + indexIncremented).parent().find('.calendarDetailHours').text(item.total.substr(0, 5));
                        if (item.approved != null){
                            $('#day' + indexIncremented).parent().find('.clockIconDetail').addClass('clicked')
                            $('#day' + indexIncremented).parent().find('.clockIconDetail').val(1);
                        }
                        $.each(item.punchTimes, function (day, time) {
                            var inTime = moment(time.inTime, "HH:mm:ss").format("hh:mm a");
                            var outTime = moment(time.outTime, "HH:mm:ss").format("hh:mm a");
                            var notes = time.notes + '<br/>';
                            if (time.notes == null){
                                notes = '';
                            }
                            if (time.outTime != null){
                                $('#day' + indexIncremented).parent().find('.calendarDetailTime').append('<span>' + inTime + ' - ' + outTime + '</span><br/>');
                                $('#day' + indexIncremented).parent().find('.clockIconDetail').removeClass('invalid');
                            }
                            else{
                                $('#day' + indexIncremented).parent().find('.calendarDetailTime').append('<span class="invalid-punchIn">' +inTime + '</span><br/>');
                                $('#day' + indexIncremented).parent().find('.calendarDetailTime').addClass('invalid-time');
                                // $('#day' + indexIncremented).parent().find('.clockIconDetail').addClass('invalid');

                            }
                            $('#day' + indexIncremented).parent().find('.calendarDetailNotes').append(notes);
                            if (findOverlappingTimes(item.punchTimes)){
                                $('#day' + indexIncremented).parent().find('.calendarDetailTime').addClass('invalid-time');
                                $('#day' + indexIncremented).parent().find('.clockIconDetail').removeClass('clicked')
                                // $('#day' + indexIncremented).parent().find('.clockIconDetail').addClass('invalid');
                            }
                        });
                    }
                    $('#day' + indexIncremented).parent().find('.invalid-time span').not('.invalid-punchIn').append(' <img src="' + window.fx_url.assets.IMAGE + 'icons/info.png" title="There are overlapping times. Please correct these before approving."/>');
                    index += 1;
                }
                else if (i == 'crewmanName'){
                    $('.headerName').text(item);
                }
                else{
                    $('.calendarDetailTotalHours').append(item.substr(0, 5));
                }
            });
            $('.invalid-punchIn').append(' <img src="' + window.fx_url.assets.IMAGE + 'icons/info.png" title="Crew member is still punched in. Please correct this before approving."/>');
        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var editCrewmanInfo = function (crewmanID) {
    var editCrewmanInfoModal = $('#editCrewmanInfoModal');

    $('#crewmanID').val(crewmanID);
    $('#phoneTable tbody tr').not(':first').remove();

    $('small.fullPhone').removeClass('is-visible');
    $('small.primary').removeClass('is-visible');
    $('small.onePhone').removeClass('is-visible');

    //Get Crewman Info
    $.ajax({
        url: 'getCrewman.php',
        dataType: "json",
        contentType: 'application/json',
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            crewmanID: crewmanID
        },
        success: function(response) {
            editCrewmanInfoModal.find('input[name="firstName"]').val(decodeEntities(response.firstName));
            editCrewmanInfoModal.find('input[name="lastName"]').val(decodeEntities(response.lastName));
            editCrewmanInfoModal.find('input[name="address"]').val(decodeEntities(response.address));
            editCrewmanInfoModal.find('input[name="address2"]').val(decodeEntities(response.address2));
            editCrewmanInfoModal.find('input[name="city"]').val(decodeEntities(response.city));

            if (editCrewmanInfoModal.find('select[name="state"]').has('option[value="' + response.state + '"]').length > 0) {
                editCrewmanInfoModal.find('select[name="state"] option[value="' + response.state + '"]').prop('selected', true);
            }

            editCrewmanInfoModal.find('input[name="zip"]').val(response.zip);
            editCrewmanInfoModal.find('input[name="email"]').val(response.email);

            if (response.canViewHourly === 1) {
                editCrewmanInfoModal.find('input[name="hourlyPayRate"]').val(response.hourlyPayRate);
            } else {
                editCrewmanInfoModal.find('input[name="hourlyPayRate"]').parents('.row').hide();
            }

            if (response.crewmanActive == 0){
                $('#userActive').prop('checked', false);
            }
            else{
                $('#userActive').prop('checked', true);
            }

            //On Success Get Crewman Phone
            $.ajax({
                url: 'getCrewmanPhone.php',
                dataType: "json",
                contentType: 'application/json',
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    crewmanID: crewmanID
                },
                success: function(response) {
                    if (response != null){
                        $.each(response, function (i, item) {

                            $cloneRow = $('#editCrewmanInfoModal .phoneTable tbody tr:first').clone().css('display', '');

                            $cloneRow.addClass('dbRow');

                            if ($cloneRow.find('select.Description').has('option[value="' + item.phoneDescription + '"]').length > 0)
                                $cloneRow.find('select.Description > option[value="' + item.phoneDescription + '"]').prop('selected', true);

                            $cloneRow.find('input.Phone').val(item.phoneNumber);

                            if (item.isPrimary == '1') {
                                $cloneRow.find('input.isPrimary').prop('checked', true);
                            } else {
                                $cloneRow.find('input.isPrimary').prop('checked', false);
                            }

                            $cloneRow.find('select.Description').attr('sort', item.crewmanPhoneID);
                            $cloneRow.find('input.Phone').attr('sort', item.crewmanPhoneID);
                            $cloneRow.find('input.isPrimary').attr('sort', item.crewmanPhoneID);

                            $('#editCrewmanInfoModal .phoneTable tbody').append($cloneRow);
                            $('#editCrewmanInfoModal .phoneTable tbody > tr:last-child input.isPrimary ').click(isPrimaryCheck);
                            $('#editCrewmanInfoModal .phoneTable tbody > tr:last-child a ').click(deletePhoneRow);
                        });
                    }

                    //On Success Display Modal
                    $('#editCrewmanInfoModal').foundation('open');

                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var saveEditCrewmanInfo = function() {
    var editCrewmanInfoModal = $('#editCrewmanInfoModal');

    editCrewmanInfoModal.find('input.crewmanRequired, #editCrewmanInfoModal select.crewmanRequired').each(function () {

        if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').addClass('is-visible');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }
    });

    if ($('#phoneTable tbody tr:visible').length > 0) {
        $('small.onePhone').removeClass('is-visible');

        if ($('#phoneTable .isPrimary:checked').length < 1) {
            $('small.primary').addClass('is-visible');

        } else {
            if ($('#phoneTable .isPrimary:checked').is(':visible')) {
                $('small.primary').removeClass('is-visible');
            } else {
                $('small.primary').addClass('is-visible');
            }
        }

        $('#phoneTable tbody tr:visible').each(function(){
            if ($(this).find('td input.Phone').val() == '' || $(this).find('td select.Description').val() == '') {
                $('small.fullPhone').addClass('is-visible');
            } else {
                if ($(this).find('td input.Phone').val().length < 14) {
                    $('small.fullPhone').addClass('is-visible');
                } else {
                    $('small.fullPhone').removeClass('is-visible');
                }
            }
        });
    } else {
        $('small.fullPhone').removeClass('is-visible');
        $('small.primary').removeClass('is-visible');
        $('small.onePhone').removeClass('is-visible');
    }

    if (!editCrewmanInfoModal.find('input[name="firstName"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="lastName"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="address"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="address2"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="city"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('select[name="state"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="zip"]').parent().hasClass('is-invalid-label') &&
        !editCrewmanInfoModal.find('input[name="email"]').parent().hasClass('is-invalid-label')
        &&
        !$('small.onePhone').hasClass('is-visible') &&
        !$('small.fullPhone').hasClass('is-visible') &&
        !$('small.primary').hasClass('is-visible')
    ) {

        var firstName = editCrewmanInfoModal.find('input[name="firstName"]').val();
        var lastName = editCrewmanInfoModal.find('input[name="lastName"]').val();
        var address = editCrewmanInfoModal.find('input[name="address"]').val();
        var address2 = editCrewmanInfoModal.find('input[name="address2"]').val();
        var city = editCrewmanInfoModal.find('input[name="city"]').val();
        var state = editCrewmanInfoModal.find('select[name="state"]').val();
        var zip = editCrewmanInfoModal.find('input[name="zip"]').val();
        var email = editCrewmanInfoModal.find('input[name="email"]').val();
        // var notes = $('#editCrewmanInfoModal textarea[name="notes"]').val();
        var crewmanID = $('#crewmanID').val();
        var active = $('#userActive').prop('checked') ? 1 : 0;
        var hourlyPayRate = editCrewmanInfoModal.find('input[name="hourlyPayRate"]').val();

        storePhoneTableData();

        function storePhoneTableData() {
            var phoneTableData = new Array();
            $('#phoneTable tbody tr').each(function(row,tr){
                phoneTableData[row]={
                    "phoneDelete":$(tr).find('td:eq(0)').find('select').attr('delete'),
                    "crewmanPhoneID":$(tr).find('td:eq(0)').find('select').attr('sort'),
                    "phoneDescription":$(tr).find('td:eq(0)').find('select').val(),
                    "phoneNumber":$(tr).find('td:eq(1)').find('input').val(),
                    "isPrimary":$(tr).find('td:eq(2)').find('input:checked').val()
                };
                if (phoneTableData[row]['isPrimary'] == undefined){
                    phoneTableData[row]['isPrimary'] = 0;
                }
            });

            phoneTableData.shift();
            var phoneTableArray = phoneTableData;

            $('loading-image').show();
            $.ajax({
                url: 'editCrewman.php',
                dataType: "json",
                contentType: 'application/json',
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    crewmanID: crewmanID,
                    firstName: firstName,
                    lastName: lastName,
                    address: address,
                    address2: address2,
                    city: city,
                    state: state,
                    zip: zip,
                    email: email,
                    active: active,
                    phoneArray: phoneTableArray,
                    hourlyPayRate: hourlyPayRate
                },
                success: function(response) {
                    if (response == 'true') {
                        $('#phoneTable tbody tr').not(':first').remove();

                        $('#editCrewmanInfoModal input[name="firstName"]').val('');
                        $('#editCrewmanInfoModal input[name="lastName"]').val('');
                        $('#editCrewmanInfoModal input[name="address"]').val('');
                        $('#editCrewmanInfoModal input[name="address2"]').val('');
                        $('#editCrewmanInfoModal input[name="city"]').val('');
                        $('#editCrewmanInfoModal select[name="state"]').val('');
                        $('#editCrewmanInfoModal input[name="zip"]').val('');
                        $('#editCrewmanInfoModal input[name="email"]').val('');
                        // $('#editCrewmanInfoModal input[name="notes"]').val('');
                        $('#userActive').prop('checked', true);

                        $('#crewmanID').val('');

                        $('#editCrewmanInfoModal').foundation('close');
                        $('#editCrewmanInfoSuccessModal').foundation('open');

                        updateCrewmanInfo(crewmanID);
                        getCrewmen();
                        $('loading-image').hide();
                    }
                    else {
                        $('#editCrewmanInfoErrorModal p.error-text').text(response);
                        $('#editCrewmanInfoModal').foundation('open');
                    }
                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    }
};

var saveAddCrewman = function() {

    $('#addCrewmanModal input.crewmanRequired, #addCrewmanModal select.crewmanRequired').each(function () {

        if ($(this).val() == '') {
            $(this).parent().addClass('is-invalid-label');
            $(this).addClass('is-invalid-input');
            $(this).parent().find('.form-error').addClass('is-visible');
        } else {
            $(this).parent().removeClass('is-invalid-label');
            $(this).removeClass('is-invalid-input');
            $(this).parent().find('.form-error').removeClass('is-visible');
        }
    });

    if ($('#phoneTableAdd tbody tr:visible').length > 0) {
        $('small.onePhone').removeClass('is-visible');

        if ($('#phoneTableAdd .isPrimary:checked').length < 1) {
            $('small.primary').addClass('is-visible');

        } else {
            if ($('#phoneTableAdd .isPrimary:checked').is(':visible')) {
                $('small.primary').removeClass('is-visible');
            } else {
                $('small.primary').addClass('is-visible');
            }
        }

        $('#phoneTableAdd tbody tr:visible').each(function(){
            if ($(this).find('td input.Phone').val() == '' || $(this).find('td select.Description').val() == '') {
                $('small.fullPhone').addClass('is-visible');
            } else {
                if ($(this).find('td input.Phone').val().length < 14) {
                    $('small.fullPhone').addClass('is-visible');
                } else {
                    $('small.fullPhone').removeClass('is-visible');
                }
            }
        });
    } else {
        $('small.fullPhone').removeClass('is-visible');
        $('small.primary').removeClass('is-visible');
        $('small.onePhone').removeClass('is-visible');
    }

    if (!$('#addCrewmanModal input[name="firstName"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="lastName"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="address"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="address2"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="city"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal select[name="state"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="zip"]').parent().hasClass('is-invalid-label') &&
        !$('#addCrewmanModal input[name="email"]').parent().hasClass('is-invalid-label')
        &&
        !$('small.onePhone').hasClass('is-visible') &&
        !$('small.fullPhone').hasClass('is-visible') &&
        !$('small.primary').hasClass('is-visible')
    ) {

        var firstName = $('#addCrewmanModal input[name="firstName"]').val();
        var lastName = $('#addCrewmanModal input[name="lastName"]').val();
        var address = $('#addCrewmanModal input[name="address"]').val();
        var address2 = $('#addCrewmanModal input[name="address2"]').val();
        var city = $('#addCrewmanModal input[name="city"]').val();
        var state = $('#addCrewmanModal select[name="state"]').val();
        var zip = $('#addCrewmanModal input[name="zip"]').val();
        var email = $('#addCrewmanModal input[name="email"]').val();
        // var notes = $('#addCrewmanModal textarea[name="notes"]').val();
        var crewmanID = $('#crewmanID').val();

        storePhoneTableData();

        function storePhoneTableData() {
            var phoneTableData = new Array();
            $('#phoneTableAdd tbody tr').each(function(row,tr){
                phoneTableData[row]={
                    "phoneDelete":$(tr).find('td:eq(0)').find('select').attr('delete'),
                    "crewmanPhoneID":$(tr).find('td:eq(0)').find('select').attr('sort'),
                    "phoneDescription":$(tr).find('td:eq(0)').find('select').val(),
                    "phoneNumber":$(tr).find('td:eq(1)').find('input').val(),
                    "isPrimary":$(tr).find('td:eq(2)').find('input:checked').val()
                };
                if (phoneTableData[row]['isPrimary'] == undefined){
                    phoneTableData[row]['isPrimary'] = 0;
                }
            });

            phoneTableData.shift();
            var phoneTableArray = phoneTableData;

            $('loading-image').show();
            $.ajax({
                url: 'addCrewman.php',
                dataType: "json",
                contentType: 'application/json',
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    crewmanID: crewmanID,
                    firstName: firstName,
                    lastName: lastName,
                    address: address,
                    address2: address2,
                    city: city,
                    state: state,
                    zip: zip,
                    email: email,
                    phoneArray: phoneTableArray,
                    // notes: notes,
                },
                success: function(response) {
                    if (response == 'true') {
                        $('#phoneTableAdd tbody tr').not(':first').remove();

                        $('#addCrewmanModal input[name="firstName"]').val('');
                        $('#addCrewmanModal input[name="lastName"]').val('');
                        $('#addCrewmanModal input[name="address"]').val('');
                        $('#addCrewmanModal input[name="address2"]').val('');
                        $('#addCrewmanModal input[name="city"]').val('');
                        $('#addCrewmanModal select[name="state"]').val('');
                        $('#addCrewmanModal input[name="zip"]').val('');
                        $('#addCrewmanModal input[name="email"]').val('');
                        // $('#addCrewmanModal input[name="notes"]').val('');
                        $('#crewmanID').val('');

                        $('#addCrewmanModal').foundation('close');
                        $('#addCrewmanSuccessModal').foundation('open');

                        getCrewmen();
                        $('loading-image').hide();
                    }
                    else {
                        $('#editCrewmanInfoErrorModal p.error-text').text(response);
                        $('#addCrewmanModal').foundation('open');
                    }
                }, error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    }
};

var updateCrewmanInfo = function(crewmanID) {
    $.ajax({
        url: 'getCrewman.php',
        dataType: "json",
        contentType: 'application/json',
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        data: {
            crewmanID: crewmanID
        },
        success: function(response) {
            var fullName = response.firstName + ' ' + response.lastName;
            $('.calendarName[crewman="' + crewmanID + '"] span').text(fullName);

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var addPhoneRow = function () {
    $cloneBlankRow = $('#phoneTable').find('tbody tr:first').clone().css('display', '');
    $('#phoneTable').find('tbody').append($cloneBlankRow);
    $('#phoneTable').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheck);
    $('#phoneTable').find('tbody > tr:last-child .delete > a').click(deletePhoneRow);

    $('#phoneTable').find('tbody > tr:last-child input.Phone').mask('(*************');

    if ($('#phoneTable input.isPrimary:checked').length <= 0) {
        $('#phoneTable tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
    }
};

var addNewCrewmanPhoneRow = function () {
    $cloneBlankRow = $('#phoneTableAdd').find('tbody tr:first').clone().css('display', '');
    $('#phoneTableAdd').find('tbody').append($cloneBlankRow);
    $('#phoneTableAdd').find('tbody > tr:last-child input.isPrimary').change(isPrimaryCheckNewCrewman);
    $('#phoneTableAdd').find('tbody > tr:last-child .delete > a').click(deleteNewCrewmanPhoneRow);

    $('#phoneTableAdd').find('tbody > tr:last-child input.Phone').mask('(*************');

    if ($('#phoneTableAdd input.isPrimary:checked').length <= 0) {
        $('#phoneTableAdd tbody tr:nth-child(2) td.primary input.isPrimary').prop('checked', true);
    }
};

var isPrimaryCheckNewCrewman = function () {
    $('#phoneTableAdd').find('input.isPrimary').prop('checked', false);
    $(this).prop('checked', true);
};

var isPrimaryCheck = function () {
    $('#phoneTable').find('input.isPrimary').prop('checked', false);
    $(this).prop('checked', true);
};

var deleteNewCrewmanPhoneRow = function () {

    if ($(this).parent().parent().hasClass('dbRow')) {
        $(this).parent().parent().find('select').attr('delete','delete');
        $(this).parent().parent().css('display','none');

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTableAdd tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        // if ($('#phoneTableAdd tbody').children().length == 1)
        //     addPhoneRow();
    } else {
        $(this).parent().parent().remove();

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTableAdd tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        // if ($('#phoneTableAdd tbody').children().length == 1)
        //    addNewCrewmanPhoneRow();
        $('small.fullPhone').removeClass('is-visible');
        $('small.primary').removeClass('is-visible');
        $('small.onePhone').removeClass('is-visible');
    }
};

var deletePhoneRow = function () {

    if ($(this).parent().parent().hasClass('dbRow')) {
        $(this).parent().parent().find('select').attr('delete','delete');
        $(this).parent().parent().css('display','none');

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTable tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        // if ($('#phoneTable tbody').children().length == 1)
        //     addPhoneRow();
    } else {
        $(this).parent().parent().remove();

        if ($(this).parent().parent().find('input.isPrimary').is(':checked'))
            $('#phoneTable tbody tr:visible').eq(0).find('td.primary input.isPrimary').prop('checked', true);

        $('small.fullPhone').removeClass('is-visible');
        $('small.primary').removeClass('is-visible');
        $('small.onePhone').removeClass('is-visible');
    }
};

var getDaysOfWeek = function () {
    $('#allDay1').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[0].format('MM/DD'));
    $('#allDay2').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[1].format('MM/DD'));
    $('#allDay3').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[2].format('MM/DD'));
    $('#allDay4').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[3].format('MM/DD'));
    $('#allDay5').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[4].format('MM/DD'));
    $('#allDay6').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[5].format('MM/DD'));
    $('#allDay7').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[6].format('MM/DD'));

    $('#day1').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[0].format('MM/DD'));
    $('#day2').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[1].format('MM/DD'));
    $('#day3').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[2].format('MM/DD'));
    $('#day4').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[3].format('MM/DD'));
    $('#day5').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[4].format('MM/DD'));
    $('#day6').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[5].format('MM/DD'));
    $('#day7').text($('#calendar').fullCalendar('getView').dayGrid.dayDates[6].format('MM/DD'));
};

var timecard_modal = $('#editTimecardModal'),
    timecard_modal_entries = timecard_modal.find('[data-entries]'),
    timecard_date = timecard_modal.find('#date'),
    timecard_date_title = timecard_modal.find('#timecardDateTitle'),
    time_entry_template = timecard_modal_entries.find('[data-template]');
    time_entry_template.remove();
var newTimeEntry = function (data) {
    var entry = time_entry_template.clone(),
        time = entry.find('[time]'),
        clock_in = entry.find('.clockIn'),
        clock_out = entry.find('.clockOut'),
        notes = entry.find('textarea[name="notes"]'),
        project_dropdown = entry.find('select[name="project"]');
    timecard_modal_entries.append(entry);

    time.attr('time', data.punchTimeID);
    notes.val(data.notes);
    if (data.project) {
        var option = $('<option/>')
            .attr('value', data.project.projectID)
            .text(data.project.customerName + ' - ' + data.project.description)
            .prop('selected', true);
        project_dropdown.append(option);
    }

    // configure elements
    var start = null, end = null;
    if (data.start === undefined) {
        var prev_entry = entry.prev();
        if (prev_entry.length > 0) {
            var prev_end = prev_entry.find('.clockOut').val();
            if (prev_end !== '') {
                start = moment(prev_end, 'hh:mma');
            }
        }
    }
    if (start === null) {
        start = moment(data.start === undefined ? '08:00:00' : data.start, 'HH:mm:ss');
    }
    if (data.end === undefined) {
        end = moment(start).add(15, 'minutes');
    }
    if (end === null) {
        end = moment(data.end, 'HH:mm:ss');
    }
    clock_in.timepicker({
        scrollDefault: 'now',
        step: 15,
        disableTouchKeyboard: true
    });
    clock_out.timepicker({
        step: 15,
        showDuration: true,
        disableTouchKeyboard: true,
        minTime: moment(start).add(15, 'minutes').format('hh:mma'),
        durationTime: start.format('hh:mma')
    });
    clock_in.on('changeTime', function () {
        var new_time = $(this).val();
        var next_time = moment(new_time, 'hh:mma').add(15, 'minutes').format('hh:mma');
        clock_out.timepicker('setTime', next_time);
        clock_out.timepicker('option', {minTime: next_time, maxTime: '12:00am', durationTime: new_time});
    });

    clock_in.timepicker('setTime', start.format('hh:mma'));
    clock_out.timepicker('setTime', moment(end, 'HH:mm:ss').format('hh:mma'));

    project_dropdown.select2({
        width: '100%',
        placeholder: 'Choose project',
        ajax: {
            transport: function (params, success, failure) {
                var ajax = {
                    url: window.fx_url.API_V1 + 'projects',
                    method: 'GET',
                    headers: {
                        'Accept': 'application/vnd.adg.fx.list-v1+json'
                    },
                    data: {
                        _page: params.data.page || 1,
                        _per_page: 15
                    },
                    timeout: 15000
                };
                if (params.data.term) {
                    ajax.data._search = params.data.term;
                }
                return $.ajax(ajax).then(success, failure);
            },
            delay: 250,
            dataType: 'json',
            processResults: function (data) {
                let results = [];
                data.collection.forEach(function (project) {
                    results.push({
                        id: project.id,
                        text: project.customer_name + ' - ' + project.description
                    });
                });
                return {
                    results: results,
                    pagination: {
                        more: data._meta && data._meta.pagination && data._meta.pagination.next_page !== null
                    }
                };
            }
        }
    });
};

var editTimecard = function(hourTotal, date, crewmanID) {
    var formattedDate = moment(date).format('MMMM DD, YYYY');
    var formattedTime = '';
    if (hourTotal != ''){
        var minutes = moment(hourTotal, "hh:mm").minute();
        var hours = moment(hourTotal, "hh:mm").hour();
        switch (minutes){
            case 0:
                formattedMinutes = '';
                break;
            case 1:
                formattedMinutes = moment(hourTotal, "hh:mm:ss").format(' m [minute]');
                break;
            default:
                formattedMinutes = moment(hourTotal, "hh:mm:ss").format(' m [minutes]');
                break;
        }
        switch (hours){
            case 0:
                formattedHours = ' - ';
                break;
            case 1:
                formattedHours = ' - ' +  moment(hourTotal, "hh:mm:ss").format('H [hour]');
                break;
            default:
                formattedHours = ' - ' +  moment(hourTotal, "hh:mm:ss").format('H [hours]');
                break;
        }
        if (hours != 0 || minutes != 0){
            formattedTime = formattedHours + formattedMinutes;
        }
        if (formattedHours.includes('Invalid') || formattedMinutes.includes('Invalid')) {
            formattedTime = '';
        }
    }

    timecard_modal_entries.empty();
    timecard_date.val(date);
    timecard_date_title.text(formattedDate + formattedTime);

    $.ajax({
        url: 'getTimecardEntries.php',
        dataType: "json",
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            crewmanID: crewmanID,
            timecardDate: date
        },
        success: function(response) {
            if (response != null){
                $.each(response, function (i, item) {
                    var entry = {
                        punchTimeID: item.punchTimeID,
                        start: typeof item.inTime === 'string' ? item.inTime : '08:00:00',
                        end: typeof item.outTime === 'string' ? item.outTime : '08:15:00',
                        notes: typeof item.notes === 'string' ? item.notes : ''
                    };
                    if (item.projectID !== '0') {
                        entry.project = {
                            projectID: item.projectID,
                            customerName: item.customerName,
                            description: item.projectDescription
                        };
                    }
                    newTimeEntry(entry);
                });
            }
            else{
                newTimeEntry({
                    punchTimeID: 0,
                    notes: ''
                });
            }
            timecard_modal.foundation('open');
        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};

var saveEditTimecard = function () {
    var crewmanID = $('.calendarDetailHeader').attr('crewman');
    var calls = [];
    var addTimes = [];
    var editTimes = [];
    $('.timeEntry').each(function(){
        var inTime = moment($(this).find('.clockIn').val(), 'hh:mma').format('HH:mm:ss');
        var outTime = moment($(this).find('.clockOut').val(), 'hh:mma').format('HH:mm:ss');
        var notes = $(this).find('textarea[name="notes"]').val();
        var punchTimeID = $(this).find('.is-hidden').attr('time');
        var timecardDate = timecard_date.val();
        var project = $(this).find('select[name="project"]').find(':selected');
        projectID = '0';
        if (project.length > 0) {
            projectID = project.eq(0).attr('value');
        }

        if (punchTimeID == 0){
            addTimes.push({
                "inTime": inTime,
                "outTime": outTime,
                "notes": notes,
                "timecardDate": timecardDate,
                "crewmanID": crewmanID,
                "projectID": projectID
            });
        }
        else{
            editTimes.push({
                "inTime": inTime,
                "outTime": outTime,
                "notes": notes,
                "punchTimeID": punchTimeID,
                "projectID": projectID
            });
        }
    });
    if (addTimes.length > 0){
        calls.push($.ajax({
            url: 'addPunchTime.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                punchTimes: addTimes
            },
            success: function(response) {
            }, error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        }));
    }
    if (editTimes.length > 0){
        calls.push($.ajax({
            url: 'editPunchTime.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                punchTimes: editTimes
            },
            success: function(response) {
            }, error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        }));
    }
    $.when.apply($, calls).then(function() {
        //update table when all ajax calls have finished
        $('.calendarDetailHeader').attr('crewman', crewmanID);
        $('.clockIconDetail').removeClass('clicked')
        $('.clockIconDetail').val(0);
        $('.calendarDetailTotalHours').empty();
        $('.calendarDetailHours').empty();
        $('.calendarDetailTime').empty();
        $('.calendarDetailNotes').empty();
        $('#editTimecardModal').foundation('close');
        $('#editTimecardSuccessModal').foundation('open');
        getCrewmanTimeDetail(crewmanID);
        getCrewmen();
    });
};

var deleteTimeEntry = function () {
    var punchTimeID = $('#deleteNumber').text();
    $.ajax({
        url: 'deletePunchTime.php',
        dataType: "json",
        contentType: 'application/json',
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            punchTimeID: punchTimeID
        },
        success: function(response) {
            if (response == 'true') {
                $('#editTimecardModal').foundation('open');
                $('[time="' + punchTimeID + '"]').parent().parent().remove();
            }
            else {
                $('#editTimecardErrorModal p.error-text').text(response);
                $('#editTimecardModal').foundation('open');
            }
        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    }).done(function () {
        getCrewmanTimeDetail($('#currentCrewman').val());
        getCrewmen();
    });
};

var highlightToday = function () {
    var todaysNumber = new Date().getDay();
    var todaysNumberIncremented = todaysNumber + 1;
    var currentDisplayDateForDayNumber = $('#calendar').fullCalendar('getView').dayGrid.dayDates[todaysNumber-1].format('YYYY-MM-DD');
    var todaysDate = moment(new Date()).format('YYYY-MM-DD');
    //checks today's date against the day number on the calendar. This is to
    //prevent highlighting a day on previous or future weeks, and only highlights today
    if (todaysDate == currentDisplayDateForDayNumber){
        $('.calendarTable tr td:nth-child(' + todaysNumberIncremented + ')').addClass('today');
        $('.calendarTableDetail tr:nth-child(' + todaysNumber + ')').addClass('today');
    }
};

var removeFutureTimes = function () {
    var todaysNumber = new Date().getDay();
    var todaysNumberIncremented = todaysNumber + 1;
    var currentDisplayDateForDayNumber = $('#calendar').fullCalendar('getView').dayGrid.dayDates[todaysNumber-1].format('YYYY-MM-DD');
    var todaysDate = moment(new Date()).format('YYYY-MM-DD');
    if (todaysDate == currentDisplayDateForDayNumber){
        for (i = todaysNumberIncremented + 1; i < 9; i++){
            //if for some strange reason there is a time entry in the future, show it. this removes only the blank times.
            $('.calendarTable tr td:nth-child(' + i + '):contains("00:00")').not('.calendarDay').text('');
            $('.calendarTableDetail tr:nth-child(' + i + ')').find('.clockIconDetail').addClass('invalid');
        }
    }
};

var approveTimecard = function (timecardDate, crewmanID, approved) {
    $.ajax({
        url: 'approveTimecard.php',
        dataType: "json",
        contentType: 'application/json',
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        data: {
            timecardDate: timecardDate,
            crewmanID: crewmanID,
            approved: approved
        },
        success: function(response) {

        }, error: function(jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    }).done(getCrewmen());
};

var approveTimecardWeek = function (timecardDates, crewmanID, approved) {
    for (i = 0; i < timecardDates.length; i++){
        timecardDate = timecardDates[i];
        $.ajax({
            url: 'approveTimecard.php',
            dataType: "json",
            contentType: 'application/json',
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                timecardDate: timecardDate,
                crewmanID: crewmanID,
                approved: approved
            },
            success: function(response) {

            }, error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    }
};

var addCrewman = function () {
    $('#phoneTableAdd tbody tr').not(':first').remove();
    $('#addCrewmanModal input[name="firstName"]').val('');
    $('#addCrewmanModal input[name="lastName"]').val('');
    $('#addCrewmanModal input[name="address"]').val('');
    $('#addCrewmanModal input[name="address2"]').val('');
    $('#addCrewmanModal input[name="city"]').val('');
    $('#addCrewmanModal select[name="state"]').val('');
    $('#addCrewmanModal input[name="zip"]').val('');
    $('#addCrewmanModal input[name="email"]').val('');

    $('small.fullPhone').removeClass('is-visible');
    $('small.primary').removeClass('is-visible');
    $('small.onePhone').removeClass('is-visible');

    $('#addCrewmanModal .crewmanRequired').each(function(){
        $(this).next().removeClass('is-visible');
        $(this).removeClass("required");
        $(this).removeClass("is-invalid-input");
        $(this).parent().removeClass('is-invalid-label');
    });

    $('#addCrewmanModal').foundation('open');
    // addNewCrewmanPhoneRow();
};

var findOverlappingTimes = function(timecardEntries){
    var overlapFound = false;
    for (i = 0; i < timecardEntries.length - 1; i++){
        var inTime = timecardEntries[i].inTime;
        var inTimeNext = timecardEntries[i+1].inTime;
        var outTime = timecardEntries[i].outTime;
        var outTimeNext = timecardEntries[i+1].outTime;
        var overlap = ((inTime < outTimeNext)  &&  (outTime > inTimeNext));
        if (overlap){
            overlapFound = true;
        }
    }
    return overlapFound;
};

$(document).ready(function () {
    $( document ).tooltip();

    $('#editCrewmanInfoModal .crewmanRequired').focusout(function(){
        if($(this).val()!=''){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        } else{
            $(this).next().addClass('is-visible');
            $(this).addClass("required");
            $(this).addClass("is-invalid-input");
            $(this).parent().addClass('is-invalid-label');
        }
    });

    $('#addCrewmanModal .crewmanRequired').focusout(function(){
        if($(this).val()!=''){
            $(this).next().removeClass('is-visible');
            $(this).removeClass("required");
            $(this).removeClass("is-invalid-input");
            $(this).parent().removeClass('is-invalid-label');

        } else{
            $(this).next().addClass('is-visible');
            $(this).addClass("required");
            $(this).addClass("is-invalid-input");
            $(this).parent().addClass('is-invalid-label');
        }
    });

    $('#calendarTitleAll').click(function() {
        $('#datepicker').datepicker('show');
    });

    $( function() {
        $( "#datepicker" ).datepicker();
    } );

    $('body').on('change', '#filterResources', function() {
        getCrewmen($(this).val());
    });

    //Click Functions
    $('#detailApproveAll').click(function () {
        $('.clockIconDetail').each(function(){
            var dayNumber = $(this).attr('value');
            var date = $('#calendar').fullCalendar('getView').dayGrid.dayDates[dayNumber].format('YYYY-MM-DD');
            var crewmanID = $('.calendarDetailHeader').attr('crewman');

            if ($(this).val() == 0){
                if ($(this).parent().parent().find('.invalid-time').length > 0 || $(this).parent().parent().find('.invalid').length > 0) {
                    // $('#invalidTimeModal').foundation('open');
                }
                else{
                    $(this).addClass('clicked')
                    $(this).val(1);
                    approveTimecard(date, crewmanID, 1);
                }
            }
        });
    });

    $('.addIcon').click(addCrewman);
    $('#downloadReport').click(downloadReport);

    $('body').on('click', '.calendarName', function() {
        var crewmanID = $(this).parent().find('.calendarName').attr("crewman");
        $('#calendarViewAll').hide();
        $('#calendarViewDetail').show();
        $('#currentCrewman').val(crewmanID);
        $('#detailViewAll').removeClass('active');
        $('#detailViewDetail').addClass('active');
        getCrewmanTimeDetail(crewmanID);
    });

    $('body').on('click', '.calendarTime', function() {
        var crewmanID = $(this).parent().find('.calendarName').attr("crewman");
        $('#calendarViewAll').hide();
        $('#calendarViewDetail').show();
        $('#currentCrewman').val(crewmanID);
        $('#detailViewAll').removeClass('active');
        $('#detailViewDetail').addClass('active');
        getCrewmanTimeDetail(crewmanID);
    });

    $('#viewActive').click(function () {
        $('#viewActive').addClass('active');
        $('#viewInactive').removeClass('active');
        $('#active').val(1);
        getCrewmen();
    });

    $('#viewInactive').click(function () {
        $('#viewInactive').addClass('active');
        $('#viewActive').removeClass('active');
        $('#active').val(0);
        getCrewmen();
    });

    $('#viewActiveDetail').click(function () {
        $('#viewActiveDetail').addClass('active');
        $('#viewInactiveDetail').removeClass('active');
        $('#active').val(1);
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#viewInactiveDetail').click(function () {
        $('#viewInactiveDetail').addClass('active');
        $('#viewActiveDetail').removeClass('active');
        $('#active').val(0);
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#allViewDetail').click(function () {
        if ($('#currentCrewman').val() === '0') {
            alert('No crewman selected');
            return false;
        }
        $('#calendarViewAll').hide();
        $('#calendarViewDetail').show();
        $('#allViewDetail').addClass('active');
        $('#detailViewDetail').addClass('active');
        $('#detailViewAll').removeClass('active');
    });

    $('#detailViewAll').click(function () {
        $('#calendarViewDetail').hide();
        $('#calendarViewAll').show();
        $('#detailViewAll').addClass('active');
        $('#allViewAll').addClass('active');
        $('#allViewDetail').removeClass('active');
    });

    $('body').on('click', '.personIcon', function() {
        var crewmanID = $(this).attr("crewman");
        editCrewmanInfo(crewmanID);
        $('#editCrewmanInfoModal').foundation('open');
    });

    //editTimecardModal
    $('body').on('click', '.addTimeEntry', function () {
        newTimeEntry({
            punchTimeID: 0,
            notes: ''
        });
    });
    $('body').on('click', '.deleteTimeEntry', function() {
        var punchTimeID = $(this).parent().parent().find('.is-hidden').attr('time');
        if (punchTimeID != 0){
            $('#deleteNumber').text(punchTimeID);
            $('#confirmDeleteModal').foundation('open');
        }
        else{
            $(this).parents('.timeEntryRow').remove();
        }
    });

    $('#deleteTimeYes').click(deleteTimeEntry);

    $('#deleteTimeCancel').click(function () {
        $('#editTimecardModal').foundation('open');
    });

    $('body').on('click', '#saveEditTimecardModal', function () {

        $('.timeEntry').each(function(){
            var inTime = moment($(this).find('.clockIn').val(), 'hh:mma');
            var outTime = moment($(this).find('.clockOut').val(), 'hh:mma');
            if (!inTime.isValid() || !outTime.isValid() || inTime.isSameOrAfter(outTime, 'minute')) {
                $(this).find('.labelWrapper label').addClass('is-invalid-label');
                $(this).find('.clockIn').addClass('is-invalid-input');
                $(this).find('.form-error').addClass('is-visible');

                $(this).find('.clockOut').addClass('is-invalid-input');
                $(this).find('.form-error').addClass('is-visible');
            }
            else{
                $(this).find('.labelWrapper label').removeClass('is-invalid-label');
                $(this).find('.clockIn').removeClass('is-invalid-input');
                $(this).find('.form-error').removeClass('is-visible');

                $(this).find('.clockOut').removeClass('is-invalid-input');
                $(this).find('.form-error').removeClass('is-visible');
            }
        });
        if (!$(".is-invalid-label").length > 0){
            saveEditTimecard();
        }
    });

    $('#saveEditCrewmanInfoModal').click(saveEditCrewmanInfo);
    $('#saveAddCrewmanModal').click(saveAddCrewman);

    $('#phoneTable .addPhone').click(addPhoneRow);
    $('#phoneTableAdd .addPhone').click(addNewCrewmanPhoneRow);

    $('.clockIcon').click(function(){
        if ($('.clockIcon').val() == 0){
            $('.clockIcon').addClass('clicked')
            $('.clockIcon').val(1);
            approveAll();
        }
        else{
            $('.clockIcon').removeClass('clicked')
            $('.clockIcon').val(0);
            unapproveAll();
        }
    });

    $('body').on('click', '.clockIconWeek', function() {
        if (!$(this).hasClass('invalid')){
            var days = $('#calendar').fullCalendar('getView').dayGrid.dayDates;
            var timecardDates = [];
            for (i = 0; i < days.length; i++){
                timecardDates.push(days[i].format('YYYY-MM-DD'));
            }

            var crewmanID = $(this).parent().parent().find('.calendarName').attr('crewman');

            if ($(this).val() == 0){
                if ($(this).parent().parent().find('.invalid-time').length > 0) {
                    // $('#invalidTimeModal').foundation('open');
                }
                else{
                    $(this).addClass('clicked')
                    $(this).val(1);
                    approveTimecardWeek(timecardDates, crewmanID, 1);
                    $(this).parent().parent().find('.calendarTime').not('.sum').addClass('approved');
                }
            }
            else{
                $(this).removeClass('clicked')
                $(this).val(0);
                approveTimecardWeek(timecardDates, crewmanID, 0);
                $(this).parent().parent().find('.calendarTime').removeClass('approved');
            }
        }
    });

    $('.clockIconDetail').click(function(){
        var dayNumber = $(this).attr('value');
        var date = $('#calendar').fullCalendar('getView').dayGrid.dayDates[dayNumber].format('YYYY-MM-DD');
        var crewmanID = $('.calendarDetailHeader').attr('crewman');
        var row = $('.calendarTable .calendarName').find('[crewman="' + crewmanID + '"]').parent().parent().index() + 3;
        var column = parseInt(dayNumber) + 2;

        if ($(this).val() == 0){
            if ($(this).parent().parent().find('.invalid-time').length > 0 || $(this).parent().parent().find('.invalid').length > 0) {
                // $('#invalidTimeModal').foundation('open');
            }
            else{
                $(this).addClass('clicked')
                $(this).val(1);
                approveTimecard(date, crewmanID, 1);
                $('.calendarTable tr:nth-child(' + row + ') td:nth-child(' + column + ')').addClass('approved');
            }
        }
        else{
            $(this).removeClass('clicked')
            $(this).val(0);
            approveTimecard(date, crewmanID, 0);
            $('.calendarTable tr:nth-child(' + row + ') td:nth-child(' + column + ')').removeClass('approved');
        }
    });

    $('.pencilIcon').click(function(){
        var hourTotal = $(this).parent().parent().find('.calendarDetailHours').text();
        var dayNumber = $(this).attr('value');
        var date = $('#calendar').fullCalendar('getView').dayGrid.dayDates[dayNumber].format('YYYY-MM-DD');
        var crewmanID = $('.calendarDetailHeader').attr('crewman');
        editTimecard(hourTotal, date, crewmanID);
    });

    $('#previous').click(function () {
        $('#calendar').fullCalendar('prev');
        var m = $('#calendar').fullCalendar('getView').title;
        $('#calendarTitleAll').text(m);
        $('#calendarTitleDetail').text(m);
        getDaysOfWeek();
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#next').click(function () {
        $('#calendar').fullCalendar('next');
        var m = $('#calendar').fullCalendar('getView').title;
        $('#calendarTitleAll').text(m);
        $('#calendarTitleDetail').text(m);
        getDaysOfWeek();
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#previousDetail').click(function () {
        $('#calendar').fullCalendar('prev');
        var m = $('#calendar').fullCalendar('getView').title;
        $('#calendarTitleAll').text(m);
        $('#calendarTitleDetail').text(m);
        getDaysOfWeek();
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#nextDetail').click(function () {
        $('#calendar').fullCalendar('next');
        var m = $('#calendar').fullCalendar('getView').title;
        $('#calendarTitleAll').text(m);
        $('#calendarTitleDetail').text(m);
        getDaysOfWeek();
        getCrewmen();
        getCrewmanTimeDetail($('#currentCrewman').val());
    });

    $('#filterSelect').append('<select id="filterResources"><option value="last">Sort By Last Name</option><option value="first">Sort By First Name</option></select>');
    $('#filterSelectDetail').append('<select id="filterResourcesDetail"><option value="last">Sort By Last Name</option><option value="first">Sort By First Name</option></select>');

    $('#calendar').fullCalendar({
        header: {
            left: '',
            center: 'title',
            right: 'today'
        },
        defaultView: 'basicWeek',
        firstDay: 1,
        weekNumbers: true,
        height:0
    });

    var m = $('#calendar').fullCalendar('getView').title;
    $('#calendarTitleAll').text(m);
    $('#calendarTitleDetail').text(m);
    getDaysOfWeek();

    getCrewmen();

    $('#currentCrewman').val(0);

});
$(function () {
    $('.Phone').mask('(*************');
});
