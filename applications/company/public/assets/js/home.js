var eventTypes = {
    project: 1,
    user: 2,
    company: 3
};

window.Layout.setModeWindow();

var resizePanels = function () {
    var topbar = $('.a-a-header').height();
    var menu = $('.menu-bar').height();

    var views = {
        D: {
            button: '#dailyView',
            view: 'timelineDay',
            size: 'large',
            sibling: 'LD'
        },
        M: {
            button: '#monthlyView',
            view: 'month',
            size: 'large',
            sibling: 'LM'
        },
        R: {
            button: '#resourceWeekView',
            view: 'resourceWeek',
            size: 'large',
            sibling: 'LW'
        },
        LD: {
            button: '#listDay',
            view: 'listDay',
            size: 'small',
            sibling: 'D'
        },
        LW: {
            button: '#listWeek',
            view: 'listWeek',
            size: 'small',
            sibling: 'R'
        },
        LM: {
            button: '#listMonth',
            view: 'listMonth',
            size: 'small',
            sibling: 'M'
        }
    };

    var window_width = $(window).width();
    var cookie_view = document.getCookie('calendar_view');
    var set_view = cookie_view;
    var cookie_lookup = views[cookie_view];
    var reload = false;

    if (typeof cookie_lookup === 'undefined') {
        reload = true;
        set_view = window_width < 640 ? 'LD' : 'D';
    } else if (window_width < 640 && cookie_lookup.size === 'large') {
        set_view = cookie_lookup.sibling;
        reload = true;
    } else if (window_width > 640 && cookie_lookup.size === 'small') {
        set_view = cookie_lookup.sibling;
        reload = true;
    }

    if (reload) {
        var view = views[set_view];
        for (var i in views) {
            if (i === set_view) {
                continue;
            }
            $(views[i].button).removeClass('active');
        }
        $(view.button).addClass('active');
        filterCalendar(view.view);
        document.setCookie('calendar_view', set_view);
    }
};

function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
};

var waitToResize = debounce(function() {
    resizePanels();
}, 250);

$(window).on('resize', waitToResize);

var decodeEntities = function (encodedString) {
    var textArea = document.createElement('textarea');
    textArea.innerHTML = encodedString;
    return textArea.value;
}

function htmlEncode(value){
    return $('<div/>').text(value).html();
}

var all_day_flag = $('input[name="scheduledStartTimeAllDay"]'),
    scheduledStart = $('input[name="scheduledStartTime"]'),
    scheduledEnd = $('input[name="scheduledEndTime"]'),
    scheduledStartHidden = $('div#scheduledStartTimeHidden'),
    scheduledEndHidden = $('div#scheduledEndTimeHidden');
function setAllDayFlag(active, set_elem = true) {
    var checked = all_day_flag.is(':checked');
    if (set_elem && checked === active) {
        return;
    }
    if (set_elem) {
        all_day_flag.prop('checked', active);
    }

    var oldStartTime, oldEndTime;

    if (active) {
        scheduledStart.attr('disabled', 'disabled').css('background-color', '#bdbdbd');
        scheduledEnd.attr('disabled', 'disabled').css('background-color' , '#bdbdbd');

        oldStartTime = scheduledStart.val();
        oldEndTime = scheduledEnd.val();

        scheduledStartHidden.text(oldStartTime);
        scheduledEndHidden.text(oldEndTime);

        scheduledStart.val(is_idevice ? '00:00' : '12 : 00 AM');
        scheduledEnd.val(is_idevice ? '23:59' : '11 : 59 PM');
    } else {
        scheduledStart.removeAttr('disabled').css('background-color', '#ffffff');
        scheduledEnd.removeAttr('disabled').css('background-color', '#ffffff');

        oldStartTime = scheduledStartHidden.text();
        oldEndTime = scheduledEndHidden.text();
        if (oldStartTime === '') {
            oldStartTime = is_idevice ? '08:00' : '08 : 00 AM';
        }
        if (oldEndTime === '') {
            oldEndTime = is_idevice ? '10:00' : '10 : 00 AM';
        }

        scheduledStart.val(oldStartTime);
        scheduledEnd.val(oldEndTime);
    }
}

var today = new Date();
today.setHours(0,0,0,0);

var is_idevice = /iPhone|iPad|iPod/i.test(navigator.userAgent);

function validateDates() {
    var elems = {
        start_date: {
            input: $('input[name="scheduledStartDate"]'),
            error: $('#startDateErr')
        },
        start_time: {
            input: $('input[name="scheduledStartTime"]'),
            error: $('#startTimeErr')
        },
        end_date: {
            input: $('input[name="scheduledEndDate"]'),
            error: $('#endDateErr')
        },
        end_time: {
            input: $('input[name="scheduledEndTime"]'),
            error: $('#endTimeErr')
        }
    };
    var start_date = elems.start_date.input.val().trim(),
        start_time = !is_idevice ? elems.start_time.input.wickedpicker('time') : elems.start_time.input.val(),
        start_datetime = null,
        end_date = elems.end_date.input.val().trim(),
        end_time = !is_idevice ? elems.end_time.input.wickedpicker('time') : elems.end_time.input.val(),
        time_format = !is_idevice ? 'h:mmA' : 'HH:mm',
        errors = {};

    if (start_date === '') {
        errors.start_date = 'Start date is required';
        start_date = null;
    }
    if (start_time === '') {
        errors.start_time = 'Start time is required';
        start_datetime = null;
    } else if (start_date !== null) {
        start_datetime = moment(start_date + ' ' + start_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
    }

    if (end_date === '') {
        errors.end_date = 'End date is required';
        end_date = null;
    }
    if (end_time === '') {
        errors.end_time = 'End time is required';
    } else if (start_datetime !== null && end_date !== null) {
        var end_datetime = moment(end_date + ' ' + end_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
        if (end_datetime.isSameOrBefore(start_datetime, 'minute')) {
            errors.end_time = 'End time must be after start time';
        }
    }

    var error_count = 0;
    for (var name in elems) {
        var elem = elems[name];
        if (errors[name]) {
            elem.error.text(errors[name]).show();
            elem.input.addClass('required').addClass('is-invalid-input');
            error_count++;
            continue;
        }
        elem.error.hide();
        elem.input.removeClass('required').removeClass('is-invalid-input');
    }
    return error_count === 0;
}

$(document).ready(function () {

    $(function () {
        $(".datepickerFrom").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerTo").datepicker("option", "minDate", selectedDate);
                $('#scheduledEndDate').trigger('change');
            }
        });
        $(".datepickerTo").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerFrom").datepicker("option", "maxDate", selectedDate);
            }
        });
    });

    if(is_idevice) {
        //change the input types for time to
        //html 5 "time" so that we don't have
        //interface issues.
        $('#scheduledStartTime').removeClass('timepicker');
        $('#scheduledEndTime').removeClass('timepicker');

        $('#scheduledStartTime').attr('type','time');
        $('#scheduledEndTime').attr('type', 'time');
    }

    //Click All Day on Schedule Modal
    all_day_flag.click(function () {
        setAllDayFlag($(this).is(':checked'), false);
    });

    $('#confirmAppointmentYes').click(function(){
        if (!validateDates()) {
            return;
        }

        $('#loading-image').show();

        var $this = $(this);
        var fxEventData = $this.data('fx.event');
        var eventModal = $('#editEvent');
        var calendar = $('#calendar');
        var scheduledStartDate, scheduledEndDate, scheduledStartTime, scheduledEndTime, startTimeAllDay, userStartTime, userEndTime;

        if ($('input[name="scheduledStartTimeAllDay"]').is(':checked')) {
            startTimeAllDay = 1;
        } else {
            startTimeAllDay = 0;
        }

        scheduledStartDate = eventModal.find('input[name="scheduledStartDate"]').val();
        scheduledEndDate = eventModal.find('input[name="scheduledEndDate"]').val();

        scheduledStartTime = eventModal.find('input[name="scheduledStartTime"]');
        scheduledEndTime = eventModal.find('input[name="scheduledEndTime"]');

        if(is_idevice){
            userStartTime = scheduledStartTime.val();
            userEndTime = scheduledEndTime.val();

        } else{
            var startHours, startMinutes, startSuffix, endHours, endMinutes, endSuffix;
            userStartTime = scheduledStartTime.wickedpicker('time').toString();
            userEndTime = scheduledEndTime.wickedpicker('time').toString();

            startHours = userStartTime.split(':')[0];
            startMinutes = userStartTime.split(':')[1];
            startMinutes = startMinutes.slice(0, -2)
            startSuffix = userStartTime.split(' ')[3];

            startHours = startHours.trim();
            startMinutes = startMinutes.trim();
            startSuffix = startSuffix.trim();

            if (startSuffix == 'pm' || startSuffix == 'PM') {
                startHours = startHours != '12' ? parseInt(startHours) + parseInt(12) : startHours;
            }
            userStartTime = startHours + ":" + startMinutes + ":" + "00";

            endHours = userEndTime.split(':')[0];
            endMinutes = userEndTime.split(':')[1];
            endMinutes = endMinutes.slice(0, -2)
            endSuffix = userEndTime.split(' ')[3];

            endHours = endHours.trim();
            endMinutes = endMinutes.trim();
            endSuffix = endSuffix.trim();

            if (endSuffix == 'pm' || endSuffix == 'PM') {
                endHours = endHours != '12' ? parseInt(endHours) + parseInt(12) : endHours;
            }
            userEndTime = endHours + ":" + endMinutes + ":" +"00";
        }

        var notifyCustomer = '';
        if (eventModal.find('input[name="notifyCustomer"]:checked').length >= 1){
            notifyCustomer = 1;
        } else {
            notifyCustomer = 0;
        }

        var newScheduledUserID = eventModal.find('select[name="salesperson"]').val();
        var description =  eventModal.find('textarea[name="scheduleDescription"]').val();

        $.ajax({
            url: window.fx_url.API + 'project/events',
            dataType: "json",
            //contentType: 'application/json',
            contentType: "application/x-www-form-urlencoded",
            type: "PUT",
            data: {
                dragAndDrop: '1',
                projectScheduleID: fxEventData.id,
                startDate: scheduledStartDate,
                startTime: userStartTime,
                startTimeAllDay: startTimeAllDay,
                endDate: scheduledEndDate,
                endTime: userEndTime,
                scheduleType: fxEventData.scheduleType,
                scheduledUserID: newScheduledUserID,
                description: description,
                notifyCustomer: notifyCustomer
            },
            success: function (response) {
                if (response.status === 1) {
                    if (eventModal.find('.callout.small.alert.response').length >= 1){
                        eventModal.find('.callout.small.alert.response').remove();
                    }

                    var updatedEvent = $('#calendar').fullCalendar('clientEvents', fxEventData.id);
                    updatedEvent = updatedEvent[0];

                    if (response.id !== null) {
                        updatedEvent.id = response.id;
                    }
                    updatedEvent.scheduledUserID = newScheduledUserID;
                    updatedEvent.scheduledStart = scheduledStartDate + ' ' + userStartTime;
                    updatedEvent.scheduledEnd = scheduledEndDate + ' ' + userEndTime;
                    if (startTimeAllDay == 1){
                        updatedEvent.allDay = true;
                    } else {
                        updatedEvent.allDay = false;
                    }

                    //if Modal has Changes
                    if (eventModal.find('select.dragAppointment').hasClass('changed') || eventModal.find('input.dragAppointment').hasClass('changed')){

                        startHours = userStartTime.split(':')[0];
                        startMinutes = userStartTime.split(':')[1];
                        var newStartDayFormat = new Date(scheduledStartDate);
                        newStartDayFormat.setHours(startHours, startMinutes);

                        endHours = userEndTime.split(':')[0];
                        endMinutes = userEndTime.split(':')[1];
                        var newEndDayFormat = new Date(scheduledEndDate);
                        newEndDayFormat.setHours(endHours, endMinutes);

                        if (updatedEvent.allDay) {
                            newEndDayFormat.setDate(newEndDayFormat.getDate() + 1);
                            newEndDayFormat.setHours(0, 0);
                        }

                        var newScheduledUser = $('#calendar').fullCalendar('getResourceById', newScheduledUserID);
                        var calendarBgColor = newScheduledUser.calendarBgColor;
                        var calendarTextColor = newScheduledUser.calendarTextColor;

                        var eventData;

                        eventData = {
                            address: updatedEvent.address,
                            address2: updatedEvent.address2,
                            allDay: updatedEvent.allDay,
                            businessName: updatedEvent.businessName,
                            calendarBgColor: calendarBgColor,
                            calendarTextColor: calendarTextColor,
                            city: updatedEvent.city,
                            end: newEndDayFormat,
                            eventType: updatedEvent.eventType,
                            firstName: updatedEvent.firstName,
                            id: updatedEvent.id,
                            isAllDay: startTimeAllDay,
                            lastName: updatedEvent.lastName,
                            latitude: updatedEvent.latitude,
                            longitude: updatedEvent.longitude,
                            projectDescription: updatedEvent.projectDescription,
                            projectID: updatedEvent.projectID,
                            resourceId: newScheduledUserID,
                            scheduleType: updatedEvent.scheduleType,
                            scheduledEnd: updatedEvent.scheduledEnd,
                            scheduledStart: updatedEvent.scheduledStart,
                            scheduledUserID: updatedEvent.scheduledUserID,
                            sendNotifications: notifyCustomer,
                            start: newStartDayFormat,
                            state: updatedEvent.state,
                            status: updatedEvent.status,
                            title: updatedEvent.title,
                            zip: updatedEvent.zip
                        };

                        calendar.fullCalendar('removeEvents', fxEventData.id);
                        calendar.fullCalendar('renderEvent', eventData, true);
                    }

                    $('#loading-image').hide();

                    $('#confirmAppointmentNo').off('click.reschedule_modal');

                    eventModal.foundation('close');
                } else {
                    $('#loading-image').hide();
                    if (eventModal.find('.callout.small.alert.response').length < 1){
                        eventModal.append('<div class="callout small alert response"><p>'+response.error.message+'</p></div>');
                    }
                }

            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
                alert('Unable to reschedule event, please contact support');
                $('#loading-image').hide();
            }
        });

    });


    if (document.getCookie('show_map') != 'N') {
        $('#mapSwitch').prop('checked', true);
    }
    else {
        $('#mapSwitch').prop('checked', false);
    }


    $('.today').click(function () {
        $('#calendar').fullCalendar('today')
    });

    $("#dateFilter").datepicker({ dateFormat: 'DD, MM d, yy' });

    var dailyView = $('#dailyView');
    var resourceWeekView = $('#resourceWeekView');
    var monthlyView = $('#monthlyView');
    var listWeek = $('#listWeek');
    var listMonth = $('#listMonth');
    var listDay = $('#listDay');


    listDay.click(function () {
        document.setCookie('calendar_view', 'LD');
        [dailyView, resourceWeekView, monthlyView, listWeek, listMonth].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'listDay');
    });

    listWeek.click(function () {
        document.setCookie('calendar_view', 'LW');
        [dailyView, resourceWeekView, monthlyView, listDay, listMonth].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'listWeek');
    });

    listMonth.click(function () {
        document.setCookie('calendar_view', 'LM');
        [dailyView, resourceWeekView, monthlyView, listDay, listWeek].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'listMonth');
    });

    dailyView.click(function () {
        document.setCookie('calendar_view', 'D');
        [resourceWeekView, monthlyView, listDay, listWeek, listMonth].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'timelineDay');
    });

    resourceWeekView.click(function () {
        document.setCookie('calendar_view', 'R');
        [dailyView, monthlyView, listDay, listWeek, listMonth].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'resourceWeek');
    });

    monthlyView.click(function () {
        document.setCookie('calendar_view', 'M');
        [dailyView, resourceWeekView, listDay, listWeek, listMonth].forEach(function(element) {
            element.removeClass('active');
        });
        $(this).addClass('active');
        $('#calendar').fullCalendar('changeView', 'month');
    });

    $('#previous').click(function () {
        $('#calendar').fullCalendar('prev');
    });

    $('#next').click(function () {
        $('#calendar').fullCalendar('next');
    });

    $('#mapSwitch').on('change', MapSwitchFlipped);
    //showMap();
});


var savedLocations = [], isMapInit = false;
//var bounds = null, markers = [], image, infowindow;
var map = null;


function initMap() {
    isMapInit = true;
    //map = new MapSet(document.getElementById('calendarMap'));
    map = new MapSet(document.getElementById('calendarMap'), company_info.latitude, company_info.longitude); //FXLRATR-25
    if (savedLocations != null && savedLocations.length > 0) showMap(savedLocations);
}

function clearMap() {
    if (map != null) map.RemoveMapLocations();
}

function showMap(locations) {
    var sD = $('#currentDate').val().split('-');
    var start = new Date(sD[0], sD[1] - 1, sD[2]);
    //var end = new Date(sD[0], sD[1] - 1, sD[2], 23, 59, 59, 999);
    var end = new Date(start.getTime() + 86400000);

    if (locations == null) savedLocations = [];
    else savedLocations = locations;

    if (!isMapInit) {
        return;
    }
    if ($('#mapSwitch').prop('checked')) {
        $('#calendarMap').show();

        if (map == null) {
            map = new MapSet(document.getElementById('calendarMap'), company_info.latitude, company_info.longitude); //FXLRATR-258
        }

        map.SetMapLocations(locations, start, end);
        map.Resize();
    }
    else {
        $('#calendarMap').hide();
    }
}

function MapSwitchFlipped() {
    if ($('#mapSwitch').prop('checked'))
    {
        document.setCookie('show_map', 'Y');

        var view = $('#calendar').fullCalendar('getView');
        if (view !== undefined && view.name === 'timelineDay') {
            showMap(savedLocations);
        }
    }
    else {
        document.setCookie('show_map', 'N');
        $('#calendarMap').hide();
    }
}

var users = {};

function moveEvent(event, delta, revertFunc, jsEvent, ui, view) {

    $('#confirmAppointmentYes').data('fx.event', event);

    originalResource = $('#calendar').fullCalendar('getResourceById', event.scheduledUserID);
    originalStart = moment(event.scheduledStart).format("MM/DD/YYYY h:mm A");
    originalEnd = moment(event.scheduledEnd).format("MM/DD/YYYY h:mm A");

    var newResource = $('#calendar').fullCalendar('getResourceById', event.resourceId);

    event.calendarBgColor = newResource.calendarBgColor;

    var eventModal = $('#editEvent');
    var selectSalesperson = eventModal.find($('select[name="salesperson"]')).empty();

    // clear errors, copied from project-management
    $('#startDateErr').hide();
    $('input[name="scheduledStartDate"]').removeClass("required").removeClass("is-invalid-input");
    $('#startTimeErr').hide();
    $('input[name="scheduledStartTime"]').removeClass("required").removeClass("is-invalid-input");
    $('#endDateErr').hide();
    $('input[name="scheduledEndDate"]').removeClass("required").removeClass("is-invalid-input");
    $('#endTimeErr').hide();
    $('input[name="scheduledEndTime"]').removeClass("required").removeClass("is-invalid-input");

    for (var i in users) {
        var user = users[i];

        for (var i2 in roles) {
            var role = roles[i2];

            if (user[i2] == 1 && $.inArray(event.scheduleType, role.eventTypes) !== -1){
                selectSalesperson.append('<option value="' + user.id + '">' + user.title + '</option>');
            }
        }
    }

    eventModal.find('#modalTitle').text('Reschedule ' + event.scheduleType);

    var nameDisplay;
    if (event.businessName != null) {
        nameDisplay = event.businessName;
    } else {
        nameDisplay = event.firstName + ' ' + event.lastName;
    }

    eventModal.find('textarea[name="scheduleDescription"]').text(event.description);

    var name = 'Update';
    var original = 'Original';
    var proposed = 'Proposed';

    if (event.scheduleType == 'Evaluation') {
        name = name + ' Appointment For ' + nameDisplay;
        eventModal.find('p.name').text(name);
        eventModal.find('p.original').html('<strong>' + original + ' Appointment</strong><br>Salesperson: ' + originalResource.title + '<br>Start Time: ' + originalStart + '<br>End Time: ' + originalEnd);
        eventModal.find('p.proposed').html('<strong>'+proposed+' Appointment</strong>');
        eventModal.find('.scheduledTitle').text('Salesperson');
    } else {
        name = name + ' Installation For ' + nameDisplay;
        eventModal.find('p.name').text(name);
        eventModal.find('p.original').html('<strong>' + original + ' Installation</strong><br>Installer: ' + originalResource.title + '<br>Start Time: ' + originalStart + '<br>End Time: ' + originalEnd);
        eventModal.find('p.proposed').html('<strong>'+proposed+' Installation</strong>');
        eventModal.find('.scheduledTitle').text('Installer');
    }

    //set Start Date & Time
    var startDate = moment(event.start).format("MM/DD/YYYY");
    var startTime = moment(event.start).format("HH:mm");
    var startTimeAMPM = moment(event.start).format("h : mm A");
    var startTimeString = startTime.toString();

    var StartTimeOptions ={
        now: startTimeString,
        twentyFour: false,  //Display 24 hour format, defaults to false
        upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
        downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
        close: 'wickedpicker__close', //The close class selector to use, for custom CSS
        hoverState: 'hover-state', //The hover state class to use, for custom CSS
        title: "Select a time", //The Wickedpicker's title,
        showSeconds: false, //Whether or not to show seconds,
        secondsInterval: 1, //Change interval for seconds, defaults to 1,
        minutesInterval: 5, //Change interval for minutes, defaults to 1
        beforeShow: null, //A function to be called before the Wickedpicker is shown
        show: function(){
            var modal = $('input[name="scheduledStartTime"]').closest('#editEvent');
            var datePicker = $('body').find('.wickedpicker');
            if(!modal.length) {
                $(datePicker).css('z-index', 'auto');
                return;
            }
            var zIndexModal = $(modal).css('z-index');
            $(datePicker).css('z-index', zIndexModal + 1);
        }, //A function to be called when the Wickedpicker is shown
        clearable: false //Make the picker's input clearable (has clickable "x")
    };

    //set End Date & Time
    var event_end = moment(event.end);
    if (event.isAllDay === 1) {
        event_end.subtract(1, 'day').endOf('day');
    }
    var endDate = event_end.format("MM/DD/YYYY");
    var endTime = event_end.format("HH:mm");
    var endTimeAMPM = event_end.format("h : mm A");
    var endTimeString = endTime.toString();
    var EndTimeOptions ={
        now: endTimeString,
        twentyFour: false,  //Display 24 hour format, defaults to false
        upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
        downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
        close: 'wickedpicker__close', //The close class selector to use, for custom CSS
        hoverState: 'hover-state', //The hover state class to use, for custom CSS
        showSeconds: false, //Whether or not to show seconds,
        secondsInterval: 1, //Change interval for seconds, defaults to 1,
        minutesInterval: 5, //Change interval for minutes, defaults to 1
        beforeShow: null, //A function to be called before the Wickedpicker is shown
        show: function(){
            var modal = $('input[name="scheduledEndTime"]').closest('#editEvent');
            var datePicker = $('body').find('.wickedpicker');
            if(!modal.length) {
                $(datePicker).css('z-index', 'auto');
                return;
            }
            var zIndexModal = $(modal).css('z-index');
            $(datePicker).css('z-index', zIndexModal + 1);
        }, //A function to be called when the Wickedpicker is shown
        clearable: false, //Make the picker's input clearable (has clickable "x")
    };


    if(is_idevice){
        $('input[name="scheduledStartTime"]').val(startTime);
        $('input[name="scheduledEndTime"]').val(endTime);
    }else{

        $('#scheduledStartTime').wickedpicker(StartTimeOptions);
        $('#scheduledStartTime').val(startTimeAMPM);

        $('#scheduledEndTime').wickedpicker(EndTimeOptions);
        $('input[name="scheduledEndTime"]').val(endTimeAMPM);
    }

    $('input[name="scheduledStartDate"]').val(startDate);
    $('input[name="scheduledEndDate"]').val(endDate);

    setAllDayFlag(event.isAllDay === 1);

    //salesperson info
    selectSalesperson.find("option[value='" + event.resourceId + "']").prop('selected', true);
    //$('input[name="tempID"]').val(lastId);

    if (new Date($('input[name="scheduledStartDate"]').val()) < today) {
        if (eventModal.find('.callout.small.alert').length < 1){
            eventModal.append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
        } else {
            eventModal.find('.callout.small.alert p').text('Please note that one of the selected dates is in the past.');
        }
    } else {
        eventModal.find('.callout.small.alert').remove();
    }

    var confirm_appt_no = $('#confirmAppointmentNo');
    confirm_appt_no.on('click.reschedule_modal', function(){
        eventModal.foundation('close');
        event.calendarBgColor = originalResource.calendarBgColor;
        revertFunc();
        confirm_appt_no.off('click.reschedule_modal');
    });

    eventModal.find('input[name="notifyCustomer"]').prop('checked', event.sendNotifications === 1);

    eventModal.find('select.dragAppointment, input.dragAppointment').removeClass('changed');

    eventModal.find('select.dragAppointment, input.dragAppointment').change(function() {
        $(this).addClass('changed');
    });

    eventModal.foundation('open');

}

var filterCalendar = function (defaultView) {

    var aspect_ratio = 1.6;
    if ($(window).width() < 640) {
        aspect_ratio = .9;
    }

    var calendar = $('#calendar');
    var date = $('input#currentDate').val();
    var calendarEditable = false;
    var calendarDelay = null;

    if ($('#calendarEditable').val() == 1) {
        calendarEditable = true;
    }

    calendar.fullCalendar('destroy');
    calendar.fullCalendar('render');


    calendar.fullCalendar({
        header: {
            left: '',
            center: '',
            right: ''
        },
        views: {
            resourceWeek: {
                type: 'timelineDay',
                duration: { weeks: 1 },
                slotDuration: '24:00:00',
                slotLabelFormat: 'ddd M/D'
            },
            timelineDay: {
                slotDuration: '00:15:00',
                slotLabelFormat: 'hA'
            }
        },
        scrollTime: '08:00:00',
        defaultView: defaultView,
        aspectRatio: aspect_ratio,
        nowIndicator: true,
        defaultDate: date,
        fixedWeekCount: false,
        selectable: false,
        selectHelper: false,
        unselectAuto: false,
        editable: calendarEditable,
        eventLimit: true,
        eventOverlap: true,
        eventColor: '#f6f7fb',
        resourceAreaWidth: '20%',
        businessHours: home_info.business_hours,
        resourceLabelText: ' ',
        resources: function (callback) {
            $.ajax({
                url: window.fx_url.API + 'calendar/resources',
                dataType: 'json',
                data: {
                    filter: getFilterUsers()
                },
                success: function (response) {
                    callback(response.result);
                    $('#scheduleModalDD select').empty();
                    $.each(response.result, function (i, item) {
                        if (item.id != '0') {
                            users[item.id] = item;
                        }
                    });
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        },
        eventAllow: function(dropLocation, draggedEvent) {
            if (dropLocation.resourceId == null){
                dropLocation.resourceId = draggedEvent.resourceId;
            }
            if (dropLocation.resourceId == 0){
                return false;
            } else {
                var newResource = $('#calendar').fullCalendar('getResourceById', dropLocation.resourceId);
                var allowDrop = false;

                for (var i in roles) {
                    var role = roles[i];

                    if (newResource[i] == 1 && $.inArray(draggedEvent.scheduleType, role.eventTypes) !== -1){
                        allowDrop = true;
                        break;
                    }
                }

                return allowDrop;
            }
        },
        eventDrop: moveEvent,
        eventResize: moveEvent,
        dayClick: function (date, jsEvent, view) {
            var date = date.format();
            var coordinates = jsEvent.pageX + ',' + jsEvent.pageY;
            var view = view.name;

            if (view != 'timelineDay') {
                document.setCookie('calendar_view', 'D');
                $('#resourceWeekView, #monthlyView').removeClass('active');
                $('#dailyView').addClass('active');
                calendar.fullCalendar('changeView', 'timelineDay');
                calendar.fullCalendar('gotoDate', date);
            }
        },
        events: function (start, end, timezone, callback) {
            var start = start.local().format('YYYY/MM/DD');
            var end = end.local().format('YYYY/MM/DD');

            if (calendarDelay !== null) {
                clearTimeout(calendarDelay);
            }

            calendarDelay = setTimeout(function(){
                calendarDelay = null;
                getEvents(start, end, timezone, callback);
            }, 800);
        },
        eventAfterAllRender: function(view){
            if ($('.fc-toolbar').length > 0) {
                if (view.name === 'timelineDay' || view.name === 'listDay'){
                    var newEvents = [];
                    $.each(calendar.fullCalendar('clientEvents'), function(i, item) {
                        if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                            newEvents.push(item);
                        }
                    });
                    $('div.fc-timelineDay-view a.fc-event').each(function () {
                        if ($(this).find('div.fc-content > span.fc-title').width() > $(this).width()) {
                            $(this).on('mouseenter', function () {
                                if(!$(this).attr('event-width'))
                                    $(this).attr('event-width', $(this).width());
                                $(this).css('z-index', 3)
                                $(this).animate({ width: $(this).find('div.fc-content > span.fc-title').width() + 16 }, 200, "swing");
                            }).on('mouseleave', function () {
                                $(this).animate({ width: $(this).attr('event-width') }, 200, "swing", function () {
                                    $(this).find('div.fc-content').css('z-index', '');
                                });
                            });
                        }
                    });
                    showMap(newEvents);
                }
                else $('#calendarMap').hide();
            }

            var containerHeight = $(window).height() - $('.footer').height() - $('.top-bar').height();
            var dashboardHeight = $('#calendarMap').height() + $('.dashboard-title').height() + $('.dashboard-controls').height();
            var calendarGroupHeight = $('#calendar').outerHeight() + $('.dashboard-filter-bar').outerHeight();
            var filterGroup = $('#filterGroup');
            var filterGroupHeader = $('#filterGroup .header');
            var filterGroupList = $('#filterGroup .list');

            if ((containerHeight - dashboardHeight) < calendarGroupHeight){
                var height = calendarGroupHeight - 3;
                filterGroup.height(height);
                filterGroupList.height(height - filterGroupHeader.height());
            } else {
                var height = containerHeight - dashboardHeight;
                filterGroup.height(height);
                filterGroupList.height(height - filterGroupHeader.height());
            }

        },
        eventClick: function (calEvent, jsEvent, view) {

            $('#loading-image').show();
            $.ajax({
                url: window.fx_url.BASE + 'getEventDetail.php',
                dataType: "json",
                contentType: 'application/json',
                type: "GET",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    eventID: calEvent.id,
                    eventType: calEvent.eventType
                },
                success: function (response) {
                    var item = response;
                    var addressDisplay2 = '';
                    var onMapDetail = '';
                    if (item.address2 != null){
                        addressDisplay2 = item.address2;
                    }
                    if (item.latitude == '0.00000000' && item.longitude == '0.00000000') {
                        onMapDetail = '(Location Not Mapped)<br/>';
                    }

                    var description = '';
                    if (item.description != null){
                        description = htmlEncode(item.description);
                        description = 'Description: ' + description + '<br/>';
                    } else {
                        description = '';
                    }

                    if (calEvent.eventType == eventTypes.project) {
                        var nameDisplay = item.firstName + ' ' + item.lastName;
                        if (item.businessName != null){
                            nameDisplay = item.businessName;
                        }

                        $('#viewEvent').append('<h2 id="modalTitle">' + nameDisplay + '</h2><h3>' + item.scheduleType + ':' + ' ' + item.projectDescription + '</h3><p>' + onMapDetail +  description + item.scheduled + '<br/>Start: ' + item.start + '<br/>End: ' + item.end + ' </p><p>' + item.address + ' ' + addressDisplay2 + '<br/>' + item.city + ', ' + item.state + ' ' + item.zip + item.directions+'<br/>' + item.phoneNumber + item.email + '<br/><br/><a class="button no-margin" href="' + window.fx_url.BASE + 'projects/' + item.projectID + '">View Project</a><br/><br/>Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</p><button class="close-button" data-close aria-label="Close reveal" type="button"><span aria-hidden="true">&times;</span></button>');

                    } else if (calEvent.eventType == eventTypes.user) {
                        $('#viewEvent').append('<h3 style="margin-bottom:0;" id="modalTitle">' + item.scheduledFirstName + ' ' + item.scheduledLastName + '</h3><h2 id="modalTitle">' + item.scheduleType + '</h2><p>Start: ' + item.start + '<br/>End: ' + item.end + ' </p><button class="close-button" data-close aria-label="Close reveal" type="button"><span aria-hidden="true">&times;</span></button>');

                    } else if (calEvent.eventType == eventTypes.company) {
                        $('#viewEvent').append('<h3 style="margin-bottom:0;" id="modalTitle">' + item.name + '</h3><h2 id="modalTitle">' + item.scheduleType + '</h2><p>Start: ' + item.start + '<br/>End: ' + item.end + ' </p><button class="close-button" data-close aria-label="Close reveal" type="button"><span aria-hidden="true">&times;</span></button>');

                    }
                    $('#loading-image').hide();
                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }

            });

            $('#viewEvent').foundation('open');
            $('#viewEvent').on('closed.zf.reveal', function () {
                $('#viewEvent').empty();
            });

        },
        eventRender: function (event, element) {
            if (event.eventType == eventTypes.company) {
                $('select#filterResources').find('option[value="0"]').show();
                $('tr[data-resource-id="0"] .fc-widget-content').show();
            }

            var fullUserColor = $('#fullUserColor').val();

            //Decode customer name on calendar
            var name = (element.find('.fc-title').text());
            if (name != '') { element.find('.fc-title').text(decodeEntities(name)); }

            if (event.rendering == 'background') {
                element.append(event.title);
            }

            if (event.start == null || event.end == null) return;
            event.end.local();
            event.start.local();

            var view = calendar.fullCalendar('getView');
            var eventAddressDisplay2 = '';
            if (event.address2 != null && event.address2 != ""){
                eventAddressDisplay2 = ' ' + event.address2;
            }

            if (view.name === 'timelineDay') {
                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 4.2rem;float: left;margin: .2rem 0 0 3px;"></div>');
                }

                if (event.eventType == eventTypes.project) {
                    if (parseFloat(event.latitude) === 0 && parseFloat(event.longitude) === 0){
                        if (event.scheduleType == 'Installation'){
                            element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation (Location Not Mapped): ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>');
                        }
                        //if (event.scheduleType == 'Installation') element.find('.fc-title').append('<br/><span class="fc-title-type">Installation :  ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>');
                        if (event.scheduleType == 'Evaluation'){
                            element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation (Location Not Mapped)</span>');
                        }
                        element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                    else{
                        if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation: ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>'); }
                        if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation</span>'); }

                        element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                } else if (event.eventType == eventTypes.user || event.eventType == eventTypes.company) {
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            }

            else if (view.name === 'month') {
                element.find('span.fc-time').prependTo(element.find('.fc-content .fc-title'));

                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem .3rem 0 3px;"></div>');
                }

                if (event.eventType == eventTypes.project) {
                    if (event.allDay == '1') {
                        element.find('.fc-title').append('<span class="fc-title-address"> ' + event.address + ' ' + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                    }
                    if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">I:' + ' ' + event.projectDescription +'</span>'); }
                    if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">E:' + ' ' + event.projectDescription +'</span>'); }

                } else if (event.eventType == eventTypes.user || event.eventType == eventTypes.company) {
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            }

            else if (view.name === 'resourceWeek') {

                element.find('.fc-title').empty();

                if (element.find('.fc-content span.fc-time').length != 1){
                    element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                } else {
                    element.find('.fc-time').remove();
                    element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                }

                if (fullUserColor == '1') {
                    element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                    element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                } else {
                    element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem 0 0 3px;"></div>');
                }
                element.find('.fc-content').parent().addClass('resource-week');

                if (event.eventType == eventTypes.project) {
                    element.find('.fc-title').append('<span class="fc-title-address">' + event.city + ", " + event.state + '</span>');

                    if (event.scheduleType == 'Installation') {

                        element.find('.fc-title').append('<br/><span class="fc-title-type">I: '+ event.projectDescription +'</span>');

                        if (moment(event.start._d).format('h:mm') == '12:00' && (moment(event.end._d).format('h:mm') == '12:00' || moment(event.end._d).format('h:mm') == '11:59')) {
                            element.find('span.fc-time').html('All Day');
                        } else {
                            element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                        }
                    } else if  (event.scheduleType == 'Evaluation') {
                        element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                        element.find('.fc-title').append('<br/><span class="fc-title-type">E: '+ event.projectDescription +'</span>');
                    }

                } else if (event.eventType == eventTypes.user || event.eventType == eventTypes.company) {
                    element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                    element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                }
            } else if (view.name === 'listDay' || view.name === 'listWeek' || view.name === 'listMonth') {
                element.find('.fc-event-dot').css('background-color', event.calendarBgColor);

                var text = '';
                var resource_name = event.resourceFirstName + ' ' + event.resourceLastName;
                if (event.eventType == eventTypes.project) {
                    text = event.scheduleType + ' - ' +event.title + '<br/>' + event.projectDescription;
                } else if (event.eventType == eventTypes.user || event.eventType == eventTypes.company) {
                    text = event.scheduleType;
                }
                if (event.eventType == eventTypes.company) {
                    resource_name = event.resourceFirstName;
                }
                element.find('.fc-list-item-title.fc-widget-content').html('<strong>' + resource_name + '</strong><br/>'+text);
            }

        },
        viewRender: function (view, element) {

            var m = calendar.fullCalendar('getDate');
            $('#currentDate').val(m.format('YYYY-MM-DD'));

            $('.fc-expander-space').remove();

            if (view.name === 'timelineDay') {
                var momentFormat = m.format('dddd MMMM D, YYYY');
                $('#calendarTitle').text(momentFormat);

                //$(element).height(auto);
                calendar.fullCalendar('option', 'contentHeight', 'auto');

                if ($('.fc-toolbar').length > 0) {
                    var newEvents = [];
                    $.each(calendar.fullCalendar('clientEvents'), function(i, item) {
                        if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                            newEvents.push(item);
                        }
                    });
                    showMap(newEvents);
                }
            } else if (view.name === 'agendaWeek' || view.name === 'resourceWeek') {

                var viewStart = view.intervalStart.format('MMMM D');
                var viewEnd = view.intervalEnd.format('MMMM D YYYY');

                var newdate = new Date(viewEnd);
                newdate.setDate(newdate.getDate() - 1); // minus the date
                var viewEndNew = new Date(newdate);
                var viewEndNewFormat = $.datepicker.formatDate("MM d", viewEndNew);

                $('#calendarTitle').text(viewStart + ' - ' + viewEndNewFormat);

                calendar.fullCalendar('option', 'contentHeight', '');
                $('#calendarMap').hide();
            } else if (view.name === 'listWeek') {

                var viewStart = view.intervalStart.format('MMM D');
                var viewEnd = view.intervalEnd.format('MMM D YYYY');

                var newdate = new Date(viewEnd);
                newdate.setDate(newdate.getDate() - 1); // minus the date
                var viewEndNew = new Date(newdate);
                var viewEndNewFormat = $.datepicker.formatDate("M d", viewEndNew);

                $('#calendarTitle').text(viewStart + ' - ' + viewEndNewFormat);

                calendar.fullCalendar('option', 'contentHeight', '');
                $('#calendarMap').hide();

            } else if (view.name === 'month' || view.name === 'listMonth') {
                var momentFormat = m.format('MMMM YYYY');
                $('#calendarTitle').text(momentFormat);

                calendar.fullCalendar('option', 'contentHeight', '');
                $('#calendarMap').hide();

            } else if (view.name === 'listDay') {
                var momentFormat = m.format('MMM D, YYYY');
                $('#calendarTitle').text(momentFormat);

                if ($('.fc-toolbar').length > 0) {
                    var newEvents = [];
                    $.each(calendar.fullCalendar('clientEvents'), function(i, item) {
                        if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                            newEvents.push(item);
                        }
                    });
                    showMap(newEvents);
                }
            }
        },
        resourceRender: function (resourceObj, labelTds, bodyTds) {
            var role = resourceObj.installation == true ? "Installation" : "";
            if (resourceObj.sales == true) {
                if (role.length > 0) role += ", ";
                role += "Sales";
            }

            labelTds.find('.fc-cell-content').append($('<span class="fc-cell-role"></span>').text(role));

            calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content').before('<div style="width: .5rem;background-color:' + resourceObj.calendarBgColor + ';height: 85%;float: left;margin-left: 3px;margin-top: 0.4rem;"></div>');

            //Decode name on calendar
            var title = calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content .fc-cell-text').text();
            calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content .fc-cell-text').text(decodeEntities(title));

            if (resourceObj.id == 0) {
                labelTds.find('.fc-cell-content .fc-cell-role').text('Company Calendar');
                //labelTds.hide();
                //bodyTds.hide();
            }
        },
        viewDestroy: function (view, element) {
            clearMap();
        }
    });

};

var getEvents = function (start, end, timezone, callback) {
    $('#loading-image').show();
    $.ajax({
        url: window.fx_url.BASE + 'getEvents.php',
        dataType: 'json',
        data: {
            filter: getFilterUsers(),
            start: start, //moment(start).format('YYYY/MM/DD'),
            end: end //moment(end).format('YYYY/MM/DD')
        },
        success: function (response) {
            $('#loading-image').hide();
            if ($('.fc-toolbar').length > 0) {
                var events = eval(response);
                callback(events);
            }
            $(response).each(function (i, item) {
                var date = $('#calendar').fullCalendar('getDate');
                var currentDate = $('input#currentDate').val();
                start = moment(item.start).format('YYYY-MM-DD');
                end = moment(item.end).format('YYYY-MM-DD');
                var addressDisplay2 = '';
                if (item.address2 != null){
                    addressDisplay2 = item.address2;
                }
                var nameDisplay = item.firstName+' '+item.lastName;
                if (item.businessName != null){
                    nameDisplay = item.businessName;
                }
            });
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(textStatus, errorThrown);
            console.log(jqXHR.responseText);
        }
    });
};