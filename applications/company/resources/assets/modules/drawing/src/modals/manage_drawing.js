/**
 * @module Drawing/Modals/Drawing
 */

'use strict';

const FormValidator = require('@ca-submodule/validator');

const Modal = require('@ca-submodule/modal').Base;

const network = require('../network');
const log = require('../log');

const Api = require('../api');
const DrawingEntity = require('../entities/drawing');
const DrawingRepo = require('../repositories/drawing');

const modal_tpl = require('@cam-drawing-tpl/modals/manage_drawing.hbs');

const Actions = {
    ADD: 1,
    DUPLICATE: 2,
    EDIT: 3
};
const Sizes = {
    small: {
        background_size: [50, 50],
        pixels_per_unit: 24,
        label: 'Small'
    },
    standard: {
        background_size: [100, 100],
        pixels_per_unit: 24,
        label: 'Standard'
    },
    medium: {
        background_size: [250, 250],
        pixels_per_unit: 24,
        label: 'Medium'
    },
    large: {
        background_size: [500, 500],
        pixels_per_unit: 24,
        label: 'Large'
    }
};

/**
 * @memberof module:Drawing/Modals
 */
class ManageDrawing extends Modal {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages.Main} module
     */
    constructor(module) {
        super(modal_tpl({
            sizes: Sizes
        }), {
            size: Modal.Size.TINY,
            classes: ['t-manage-drawing']
        });
        Object.assign(this.state, {
            module,
            action: null,
            drawing: null,
            external_close: false,
            projects: new Map
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of ['name', 'project', 'size']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }
        this.elem.size_wrapper = this.elem.content.fxFind('size-wrapper');

        this.state.form = FormValidator.init(this.elem.form)
            .on('form:submit', () => {
                this.save();
                return false;
            });
        let fields = {
            name: {
                required: true,
                maxlength: 100
            },
            project: {
                pattern: '[0-9]+',
                patternMessage: 'Invalid project id',
                classHandler: (field) => {
                    return field.$element.siblings('.select2-container');
                }
            }
        };
        this.state.field = {};
        for (let name in fields) {
            fields[name].requiredMessage = 'Required';
            this.state.field[name] = this.elem.input[name].parsley(fields[name]);
        }

        this.elem.input.project.select2({
            placeholder: 'Choose project',
            allowClear: true,
            templateSelection: (state) => {
                let max_length = 50;
                return state.text.length > max_length ? state.text.substr(0, max_length) + '...' : state.text;
            },
            ajax: {
                transport: (params, success, failure) => {
                    let request = Api.Resources.Projects()
                        .accept('application/vnd.adg.fx.drawing-v2+json')
                        .page(params.data.page || 1).perPage(15);
                    if (params.data.term) {
                        request.search(params.data.term);
                    }
                    return request.all().then(success, failure);
                },
                delay: 250,
                dataType: 'json',
                processResults: ({data: collection}) => {
                    let results = [];
                    collection.entities.forEach((project) => {
                        let id = project.get('id');
                        results.push({
                            id,
                            text: `${project.get('customer_name')} - ${project.get('description')}`
                        });
                        this.state.projects.set(id, project.data);
                    });
                    return {
                        results: results,
                        pagination: {
                            more: collection.response.meta('pagination.next_page') !== null
                        }
                    };
                }
            }
        });

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });
    };

    /**
     * Get available actions
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Action() {
        return Actions;
    };

    /**
     * Select a default project
     *
     * @param {object} project
     */
    selectProject(project) {
        let option = new Option(`${project.customer_name} - ${project.description}`, project.id, true, true);
        this.elem.input.project.append(option).trigger('change');
        this.state.projects.set(project.id, project);
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {object} $0.config
     * @param {Promise} $0.promise
     */
    open({config, promise}) {
        this.state.promise = promise;
        this.state.action = config.action;
        switch (config.action) {
            case Actions.ADD:
                this.setTitle('Create Drawing');
                if (!network.online) {
                    this.elem.input.project.prop('disabled', true);
                } else if (config.project_id !== undefined) {
                    this.startWorking();
                    Api.Resources.Projects()
                        .accept('application/vnd.adg.fx.drawing-v2+json')
                        .retrieve(config.project_id)
                        .done(({data: project}) => {
                            this.selectProject(project.data);
                        })
                        .always(() => this.resetWorking())
                }
                this.elem.size_wrapper.show();
                this.elem.input.size.val('standard');
                break;
            case Actions.EDIT:
            case Actions.DUPLICATE:
                this.elem.size_wrapper.hide();
                if (config.drawing_id !== undefined) {
                    this.startWorking();
                    DrawingRepo.retrieve(config.drawing_id).then((drawing) => {
                        this.state.drawing = drawing;
                        this.elem.input.name.val(config.action === Actions.DUPLICATE ? `${drawing.name} (Copy)` : drawing.name);
                        if (drawing.project !== null) {
                            this.selectProject(drawing.project);
                        }
                        if (!network.online) {
                            this.elem.input.project.prop('disabled', true);
                        }
                        this.resetWorking();
                    }, (error) => {
                        this.showErrorMessage('Unable to fetch drawing info');
                        log.error('Unable to fetch drawing info', {error});
                    });
                }
                this.setTitle(config.action === Actions.DUPLICATE ? 'Duplicate Drawing' : 'Update Drawing');
                break;
        }
        super.open();
    };

    /**
     * Save changes
     */
    save() {
        this.clearMessages();
        this.startWorking();
        let entity;
        if (this.state.action !== Actions.DUPLICATE) {
            entity = this.state.drawing !== null ? this.state.drawing : new DrawingEntity;
        } else {
            entity = this.state.drawing.clone(true);
        }
        entity.name = this.elem.input.name.val();
        let project_id = this.elem.input.project.val();
        if (project_id !== null) {
            project_id = parseInt(project_id);
            entity.project_id = project_id;
            entity.project = this.state.projects.get(project_id);
        } else {
            entity.project_id = null;
            entity.project = null;
        }
        if (this.state.action === Actions.ADD) {
            let size = Sizes[this.elem.input.size.val()];
            entity.background_size = size.background_size;
            entity.pixels_per_unit = size.pixels_per_unit;
            entity.version = DrawingEntity.VERSION;
            entity.created_by_user_id = this.state.module.current_user.id;
        }
        if (entity.isStateChanged()) {
            entity.sync_status = DrawingEntity.SyncStatus.PENDING;
            DrawingRepo.store(entity).then(() => {
                entity.setStateChanged(false);
                this.state.promise.resolve(entity);
                this.state.promise = null;
                this.close();
            })
                .catch(error => {
                    log.error('Unable to store drawing', {error, entity});
                    this.showErrorMessage('Unable to save changes, please contact support');
                });
        } else {
            this.state.promise.resolve(entity);
            this.state.promise = null;
            this.close();
        }
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.state.action = null;
        this.state.drawing = null;
        this.state.form.reset();
        this.state.projects.clear();
        this.elem.form[0].reset();
        this.elem.input.project.prop('disabled', false).empty().val(null).trigger('change');
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = ManageDrawing;
