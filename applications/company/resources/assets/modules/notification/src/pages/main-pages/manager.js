'use strict';

import $ from "jquery";
import moment from "moment-timezone";

import Page from '@ca-package/router/src/page';

import Api from "@cam-project-js/api";
import {Base as Table} from "@cas-table-js";
const Tooltip = require('@cas-tooltip-js');

import {acknowledge} from "@cas-layout-js/notifications/helpers";
import NotificationService from "@ca-submodule/layout/src/notifications/service";

import manager_tpl from '@cam-notification-tpl/pages/main-pages/manager.hbs';

const EventEmitter = require('events');
const item_max_length = 50;

export class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            layout: window.layout,
            events: new EventEmitter,
            table_loaded: false,
            table_scope: {
                sorts: {
                    seen_read_at: Table.Sort.DESC
                },
                filters: {},
            }
        });
    };

    /**
     * Get special icon for association type notifications
     *
     * @param {number} association_type
     * @param {string} metadata
     * @returns {{class_name: string, icon: string}}
     */
    getInfoTypeIcon(association_type, metadata = '{}') {
        let BASE_ICON_MAP = {
            [Api.Constants.AppNotification.AssociationType.PROJECT]: {
                default:               { icon: '--document--clipboard-line',     class_name: 't-yellow' },
                project_assigned:      { icon: '--document--clipboard-line',     class_name: 't-yellow' },
                project_event_created: { icon: '--business--calendar-todo-line', class_name: 't-purple' }
            },
            [Api.Constants.AppNotification.AssociationType.LEAD]: {
                default:          { icon: '--business--inbox-archive-line', class_name: 't-orange' },
                lead_assigned:    { icon: '--business--inbox-archive-line', class_name: 't-orange' }
            },
            [Api.Constants.AppNotification.AssociationType.TASK]: {
                default:          { icon: '--system--checkbox-line', class_name: 't-green' },
                task_assigned:    { icon: '--system--checkbox-line', class_name: 't-green' }
            },
            [Api.Constants.AppNotification.AssociationType.BID]: {
                default:      { icon: '--document--file-list-2-line',   class_name: 't-blue' },
                bid_viewed:   { icon: '--document--file-list-2-line', class_name: 't-blue' },
                bid_accepted: { icon: '--document--file-check-line',  class_name: 't-green' },
                bid_rejected: { icon: '--document--file-close-line',  class_name: 't-red' }
            }
        };

        let FALLBACK_ICON = { icon: '--system--information-line', class_name: 't-blue' };
        if (association_type === null) return FALLBACK_ICON;

        let sub_type;
        try {
            sub_type = JSON.parse(metadata).sub_type;
        } catch (_) {
            sub_type = undefined;
        }

        const group = BASE_ICON_MAP[association_type] || {};
        return group[sub_type] || group.default || FALLBACK_ICON;
    }

    /**
     * Create the Project DataTable and apply settings and defaults
     */
    createTable() {
        try {
            let type_map = new Map([
                    [Api.Constants.AppNotification.Type.INFO, { class_name: "t-blue", icon: '--system--information-line', }],
                    [Api.Constants.AppNotification.Type.MAINTENANCE, { class_name: "t-yellow", icon: '--design--tools-line', }],
                    [Api.Constants.AppNotification.Type.ERROR, { class_name: "t-red", icon: '--system--error-warning-line', }],
                ]);

            this.state.table = new Table(this.elem.table, {
                server_paginate: false,
                timezone: this.state.layout.user.timezone
            }).on('row_click', (data) => {
                let link = NotificationService.getNotificationLink(data.notification)
                link ? window.location.href = link : this.openSingleNotification(data);
            });

            // set header config
            this.state.table.setHeader({
                custom_search: true,
                search: true,
                search_placeholder: 'Search',
            });

            // set columns config
            this.state.table.setColumns({
                type: {
                    label: 'Type',
                    orderable: false,
                    value: data => {
                        let notification_type = data?.notification?.type;
                        let type = type_map.get(data?.notification?.type);
                        if (notification_type === Api.Constants.AppNotification.Type.INFO) {
                            let info_type = this.getInfoTypeIcon(data?.notification?.association_type, data?.notification?.metadata);
                            return `<span class="c-h-type">
                                <svg class="c-row-icon ${info_type.class_name}"><use xlink:href="#remix-icon${info_type.icon}"></use></svg>
                            </span>`;
                        }
                        return `<span class="c-h-type">
                                <svg class="c-row-icon ${type.class_name}"><use xlink:href="#remix-icon${type.icon}"></use></svg>
                            </span>`;
                    }

                },
                title: {
                    label: 'Title',
                    orderable: false,
                    value: (data) => {
                        return data?.notification.title.length > item_max_length
                            ? data.notification.title.substring(0, item_max_length) + '...'
                            : data.notification.title;
                    }
                },
                summary: {
                    label: 'Summary',
                    orderable: false,
                    value: (data) => {
                        let summary = data.notification.summary.replace(/<\/?[^>]+(>|$)/g, "");
                        return summary.length > item_max_length
                            ? summary.substring(0, item_max_length) + '...'
                            : summary;
                    }
                },
                created_at: {
                    label: 'Created',
                    value: (data) => {
                        let date = data.created_at;
                        if (date === null) {
                            return '';
                        }

                        let parsed = date.includes('T')
                            ? moment(date)
                            : moment.utc(date);

                        return parsed.tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                    }
                },
                seen_read_at: {
                    label: 'Read At',
                    value: (data) => {
                        let date = data?.seen_read_at;
                        if (date === null) {
                            return '';
                        }

                        let parsed = date.includes('T')
                            ? moment(date)
                            : moment.utc(date);

                        return parsed.tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                    }
                },
            });

            this.state.table.setRowActions({
                go_to_link: {
                    label: 'Go to Link',
                    visible: (data) => {
                        let link = NotificationService.getNotificationLink(data.notification)
                        return !!link;
                    },
                    action: (data) => {
                        window.location.href = NotificationService.getNotificationLink(data.notification)
                    }
                },
                view_notification: {
                    label: 'Open Notification',
                    action: (data) => {
                        this.openSingleNotification(data);
                    }
                },
                mark_as_unread: {
                    label: 'Mark As Unread',
                    visible: (data) => {
                      return data.status !== Api.Constants.AppNotificationDistribution.Status.COMPLETED;
                    },
                    action: (data) => {
                        acknowledge(data.id, Api.Constants.AppNotificationDistribution.Status.UNREAD).then(() => {
                            $(document).trigger('notification:fetch-all')
                        });
                    }
                }
            });

            this.state.table.setAjax(Api.Resources.AppNotificationDistribution, (request) => {
                request.accept('application/vnd.adg.fx.archive-v1+json');
            });

            // modify table state
            if (this.state.table_scope) {
                this.state.table.setState(this.state.table_scope);
            }

            this.state.table.build();
            this.state.table_loaded = true;

        } catch (error) {
            console.error('Error creating the table:', error);
        }
    };

    openSingleNotification(distribution) {
        if (!distribution || !distribution.notification) {
            console.warn('Invalid distribution object passed to openSingleNotification:', distribution);
            return;
        }

        let notification = distribution.notification;

        let notification_data = {
            ...notification,
            distribution: distribution,
            created_at: distribution.created_at,
        };

        $(window).trigger('notification:clicked', [notification_data]);
    };

    /**
     * Refresh the table
     */
    refresh() {
        this.state.table.draw();
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);

        if (this.state.table_loaded) {
            this.state.table.draw(false);
        } else {
            if (request.query) {
                this.state.table_scope = Object.assign(this.state.table_scope, Table.buildScopeFromQuery(request.query));
            }
            this.state.table.setState(this.state.table_scope);
            this.state.table.build();
            this.state.table_loaded = true;
        }
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.createTable();
        $(window).on('notification:archive-update', () => {
            this.refresh();
        });

        Tooltip.initAll(root);
    };

    /**
     * Render page
     */
    render() {
        return manager_tpl();
    };
}
