/**
 * @module BidCreator/Sidebar/Components/Uploads/Section
 */

'use strict';

const truncate = require('lodash/truncate');

const Accordion = require('../../accordion');
const List = require('../../list');

const title_tpl = require('@cam-bid-creator-tpl/sidebar/components/uploads/title.hbs');

/**
 * @alias module:BidCreator/Sidebar/Components/Uploads/Section
 */
class Section {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Sidebar/Components/Uploads} component
     * @param {module:BidCreator/Section} section
     */
    constructor(component, section) {
        /**
         * @private
         */
        this.state = {
            booted: false,
            component: component,
            section: section,
            files: new Map,
            lightbox_items: null
        };

        this.state.list = new List({
            type: List.Type.EDIT,
            display_type: List.DisplayType.MEDIA,
            no_items_text: 'No files added',
            action_handler: (item) => {
                let image = this.getFile(item.storage.file_id);
                this.edit_modal.open(image);
            },
            preview_handler: (item) => {
                this.showLightbox(item.storage.file_id);
            }
        });

        for (let form of section.forms.values()) {
            for (let file of form.files.values()) {
                this.addFile(file);
            }
        }

        section.on('renamed', () => {
            if (this.isBooted()) {
                this.renderTitle();
            }
        });
    };

    /**
     * Get component
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components/Uploads}
     */
    get component() {
        return this.state.component;
    };

    /**
     * Get accordion item instance
     *
     * @readonly
     *
     * @returns {Accordion.Item}
     */
    get item() {
        return this.state.item;
    };

    /**
     * Get accordion item title
     *
     * @returns {string}
     */
    getTitle() {
        let name = `${this.state.section.name === null ? 'Unsaved Section' : this.state.section.name} (${this.state.files.size})`;
        return title_tpl({
            title: name,
            unsaved: this.state.section.name === null
        });
    };

    /**
     * Set title of accordion item
     */
    renderTitle() {
        this.state.item.setTitle(this.getTitle());
    };

    /**
     * Get file
     *
     * @param {string} id - File uuid
     * @returns {any}
     */
    getFile(id) {
        return this.state.files.get(id);
    };

    /**
     * Add file
     *
     * @param {module:Form/File} file
     */
    addFile(file) {
        file.on('destroy', () => {
            this.destroyFile(file.id);
        });
        file.on('edited', () => {
            this.updateFile(file.id);
        });
        let info = {
            file: file,
            list_item_id: this.state.list.addItem({
                name: truncate(file.name, {length: 25}),
                thumbnail_url: file.thumbnail_url
            }, {
                file_id: file.id
            })
        };
        this.state.files.set(file.id, info);
        if (this.isBooted()) {
            this.renderTitle();
        }
        // remove lightbox item cache, so this file will be added
        this.state.lightbox_items = null;
    };

    /**
     * Show fancybox lightbox with all available files
     *
     * Designed to show the selected file represented with the passed file id in the proper order in the lightbox
     *
     * @param {string} file_id - File uuid
     */
    showLightbox(file_id) {
        if (this.state.lightbox_items === null) {
            let items = [];
            let index = 0;
            this.state.files.forEach((info) => {
                info.lightbox_index = index;
                items.push({
                    src: info.file.url,
                    opts: {
                        caption: info.file.name,
                        thumb: info.file.thumbnail_url,
                        fx_delete_handler: (data) => {
                            data.fb_instance.close();
                            info.file.startDelete();
                        }
                    }
                });
                index++;
            });
            this.state.lightbox_items = items;
        }
        let item = this.state.files.get(file_id);
        $.fancybox.open(this.state.lightbox_items, {
            buttons: [
                'thumbs',
                'delete',
                'close'
            ],
            thumbs: {
                autoStart: true
            }
        }, item.lightbox_index);
    };

    // /**
    //  * Delete file by id
    //  *
    //  * Starts delete process on passed file instance, this will emit a 'destroy' event which we added a listener to
    //  * in the addFile() method which will destroy the file in this class
    //  *
    //  * @param id
    //  */
    // deleteFile(id) {
    //     let info = this.getFile(id);
    //     info.file.startDelete();
    // };

    /**
     * Get edit modal, if it doesn't exist then modal instance will be made and cached statically
     *
     * @readonly
     *
     * @returns {module:Modal.Base}
     */
    get edit_modal() {
        if (this.state.edit_modal === undefined) {
            let modal = require('../../../modals/upload/edit');
            this.state.edit_modal = new modal(this);
        }
        return this.state.edit_modal;
    };

    /**
     * Destroy file by item
     *
     * Deletes file from internal cache, removes it from the files list, and updates accordion item title count
     *
     * @param {string} id - File uuid
     */
    destroyFile(id) {
        let info = this.getFile(id);
        this.state.list.deleteItem(info.list_item_id);
        this.state.files.delete(id);
        if (this.isBooted()) {
            this.renderTitle();
        }
        // remove lightbox item cache, so this file will be removed
        this.state.lightbox_items = null;
    };

    /**
     * Update file by item
     *
     * After file name and/or description has been edited in the modal we need to update the name in the accordian
     *
     * @param {string} id - File uuid
     */
    updateFile(id) {
        let info = this.getFile(id);
        this.state.list.setItemLabel(info.list_item_id, truncate(info.file.state.name, {length: 25}));
    };

    /**
     * Delete item
     */
    delete() {
        this.state.list.delete();
        if (this.state.item !== undefined) {
            this.state.item.delete();
        }
    };

    /**
     * Determines if class is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot item
     *
     * Add accordion item to proper accordion of parent component
     */
    boot() {
        this.state.item = new Accordion.Item(this.getTitle(), this.state.list.render());
        this.state.item.on('booted', (event) => {
            this.state.list.boot(event.item.elem.panel);
        });

        this.component.accordions.section.addItem(this.state.item);

        this.state.booted = true;
    };

    /**
     * Render item
     *
     * @returns {string}
     */
    render() {
        return this.state.list.render();
    };
}

module.exports = Section;
