'use strict';

import 'remixicon/icons/System/close-line.svg';
import 'remixicon/icons/Editor/node-tree.svg';
import 'remixicon/icons/Editor/text.svg';
import 'remixicon/icons/Finance/money-dollar-circle-line.svg';
import 'remixicon/icons/System/menu-line.svg';
import 'remixicon/icons/Document/file-paper-2-line.svg'
import 'remixicon/icons/Design/pencil-ruler-line.svg'
import 'remixicon/icons/Design/square-line.svg'
import 'remixicon/icons/Design/circle-line.svg'
import 'remixicon/icons/Design/ball-pen-line.svg'
import 'remixicon/icons/Media/image-line.svg'
import 'remixicon/icons/Media/image-line.svg'
import 'remixicon/icons/System/information-line.svg'
import 'remixicon/icons/Design/layout-5-line.svg'
import 'remixicon/icons/Arrows/arrow-down-s-line.svg'
import 'remixicon/icons/Arrows/arrow-right-up-line.svg'
import 'remixicon/icons/Arrows/arrow-go-back-line.svg'
import 'remixicon/icons/Arrows/arrow-go-forward-line.svg'
import 'remixicon/icons/System/add-circle-line.svg'
import 'remixicon/icons/Design/edit-2-line.svg'
import 'remixicon/icons/System/delete-bin-2-line.svg'
import 'remixicon/icons/System/delete-bin-line.svg'
import 'remixicon/icons/System/refresh-line.svg'
import 'remixicon/icons/System/eye-line.svg'
import 'remixicon/icons/System/checkbox-blank-circle-line.svg'
import 'remixicon/icons/System/checkbox-circle-line.svg'
import 'remixicon/icons/System/checkbox-circle-fill.svg'
import 'remixicon/icons/System/radio-button-line.svg'
import 'remixicon/icons/Map/map-pin-2-line.svg'
import 'remixicon/icons/User & Faces/user-line.svg'
import 'remixicon/icons/Device/phone-line.svg'
import 'remixicon/icons/Business/mail-line.svg'
import 'remixicon/icons/System/upload-cloud-2-line.svg'
import 'remixicon/icons/System/lock-line.svg'
import 'remixicon/icons/System/more-line.svg'
import 'remixicon/icons/Arrows/arrow-right-line.svg'
import 'remixicon/icons/System/external-link-line.svg'
import 'remixicon/icons/System/share-circle-line.svg'
import 'remixicon/icons/Development/cursor-line.svg'
import 'remixicon/icons/System/close-large-line.svg'

require('@cac-loader/spinning-circle.svg');
require('./resources/sass/main.scss')

const $ = require('jquery');
window.$ = window.jQuery = $;

require('select2');
require('@fancyapps/fancybox');

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

require('@ca-package/dom/src/jquery_plugin');

const {layout} = require('@ca-submodule/layout');

const BidCreator = require('./src/index');

window.BidCreator = new BidCreator(layout);
window.BidCreator.boot(window.bid_info.id);
