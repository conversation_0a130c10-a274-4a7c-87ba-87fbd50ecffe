'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const DeletModal = require('../../../../modals/term-condition/delete');

const terms_conditions_tpl = require('@cam-company-profile-tpl/pages/main-pages/terms-conditions-pages/items-pages/manager.hbs');
const moment = require("moment-timezone");

class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            modals: {},
            table: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC,
                }
            },
        });

        this.state.modals = {
            delete: new DeletModal(this)
        }
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('terms_conditions.items.update', {
                    terms_conditions_id: data.id
                });
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Terms & Conditions'
        });

        this.state.table.setFilterOptions({
            type: {
                label: 'Type',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Contract',
                        value: Api.Constants.BidContent.Type.CONTRACT
                    },
                    2: {
                        label: 'Disclaimer',
                        value: Api.Constants.BidContent.Type.DISCLAIMER
                    },
                    3: {
                        label: 'Waiver',
                        value: Api.Constants.BidContent.Type.WAIVER
                    },
                    4: {
                        label: 'Warranty',
                        value: Api.Constants.BidContent.Type.WARRANTY
                    }
                }
            },
            is_default: {
                label: 'Default',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            is_locked: {
                label: 'Locked',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            is_required: {
                label: 'Required',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            }
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        let checked = '<svg class="t-icon-checked"><use xlink:href="#remix-icon--system--checkbox-circle-line"></use></svg>',
            unchecked = '';

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name',
                responsive: 1,
                width: '50%'
            },
            type_name: {
                label: 'Type',
                responsive: 3,
                width: '45%'
            },
            is_default: {
                label: 'Default',
                orderable: false,
                value: (data) => {
                    return data.is_default ? checked : unchecked;
                }
            },
            is_locked: {
                label: 'Locked',
                orderable: false,
                value: (data) => {
                    return data.is_locked ? checked : unchecked;
                }
            },
            is_required: {
                label: 'Required',
                orderable: false,
                value: (data) => {
                    return data.is_required ? checked : unchecked;
                }
            },
            product_items: {
                label: 'Products Attached',
                orderable: false,
                value: (data) => {
                    if (data.product_items.length === 0) {
                        return;
                    }
                    return data.product_items.length;
                }
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            }
        });

        // set row action config
        this.state.table.setRowActions({
            edit_terms: {
                label: 'Edit',
                action: (data) => {
                    this.router.navigate('terms_conditions.items.update', {
                        terms_conditions_id: data.id
                    });
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: (data) => {
                    this.openModal({
                        bid_content_id: data.id,
                        name: data.name,
                        type: data.type_name
                    })
                        .then(() => {
                            this.state.table.draw() })
                        .finally(() => {
                            this.state.modals.delete.close();
                        });
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            add_unit: {
                label: 'New Item',
                action: () => {
                    this.router.navigate('terms_conditions.items.create');
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.BidContent, (request) => {
            let filters = {
                'status': Api.Constants.BidContent.Status.ACTIVE,
                'type': Api.Filter.make('in', [
                    Api.Constants.BidContent.Type.DISCLAIMER, Api.Constants.BidContent.Type.CONTRACT,
                    Api.Constants.BidContent.Type.WARRANTY,  Api.Constants.BidContent.Type.WAIVER,
                    Api.Constants.BidContent.Type.ACKNOWLEDGEMENT
                ])
            };
            let relations = {
                product_items: {
                    fields: ['id']
                }
            };
            request.fields([
                'id', 'name', 'type_name', 'is_default', 'is_locked', 'is_required', 'created_at', 'updated_at'
            ]).filters(filters).relations(relations);
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }
    };

    /**
     * Redraw table
     */
    drawTable() {
        this.state.table.draw();
    };

    /**
     * Build or draw table
     */
    loadTable() {
        this.createTable();
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Open delete media modal with promise
     *
     * @param {string} bid_content_id - uuid
     * @param {string} name
     * @param {string} type
     * @returns {Promise<undefined>}
     */
    openModal({bid_content_id, name, type}) {
        return new Promise((resolve, reject) => {
            return this.state.modals.delete.open({
                bid_content_id,
                name,
                type,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();
        await super.load(request, next);
        this.loadTable();
        this.elem.loader.hide();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // We have to force the filter to hide if it's still open when they unload
        this.state.table.hideFilterMenu();

        this.state.table.destroy();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return terms_conditions_tpl();
    };
}

module.exports = Manager;