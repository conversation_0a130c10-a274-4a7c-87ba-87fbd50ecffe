"use strict";

const Uppy = require("@uppy/core");
const Dashboard = require("@uppy/dashboard");

const Page = require("@ca-package/router/src/page");

const { mime_types, MAX_UPLOAD_SIZE_10MB } = require("./constants");

const {
    csvColumns,
    csvTo<PERSON>son,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/csv");

const upload_tpl = require("@cam-company-profile-tpl/pages/main-pages/products-pages/import-pages/upload.hbs");

/**
 * Product Import Upload Page
 *
 * Represents the first step in the product import process.
 * Allows the user to upload a CSV file and processes it using Uppy.
 */
class Upload extends Page {
    /**
     * @param {module:Router.Controller} router - The router controller.
     * @param {string} name - The route name of this page.
     * @param {module:Router.Page|null} [parent=null] - Optional parent page.
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            uppy: null,
        });
    };

    /**
     * Configures the Uppy instance for uploading and parsing CSV files.
     * Updates parent state and navigates to preview upon file read.
     */
    configureUppy() {
        /**
         * Utility to read a file as text using FileReader wrapped in a Promise.
         * @param {File} file - The file to read.
         * @returns {Promise<string>} - Promise resolving to the file's text content.
         */
        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = () => reject(reader.error);
                reader.readAsText(file);
            });
        }

        this.state.uppy = Uppy({
            id: "product-import-upload",
            autoProceed: false,
            restrictions: {
                allowedFileTypes: Object.keys(mime_types),
                maxNumberOfFiles: 1,
                maxFileSize: MAX_UPLOAD_SIZE_10MB,
            },
            locale: {
                strings: {
                    youCanOnlyUploadFileTypes: "This file type is not allowed",
                    browse: 'Browse',
                    dropPaste: '%{browse}, Paste, or Drop Completed Template Here'
                },
            },
        }).use(Dashboard, {
            target: ".m-product-uploader",
            inline: true,
            width: "100%",
            height: "365px",
            proudlyDisplayPoweredByUppy: false,
            hideProgressAfterFinish: true,
            closeModalOnClickOutside: false
        });

        this.state.uppy.on("file-added", async (file) => {
            try {
                this.parent.startWorking();
                const csvText = await readFileAsText(file.data);
                this.parent.state.csv_columns = csvColumns(csvText);
                this.parent.state.csv_data = csvToJson(csvText);
                this.router.navigate("products.import.preview");
            } catch (error) {
                console.error("Error reading CSV file:", error);
                this.state.uppy.reset();
            } finally {
                this.parent.resetWorking();
            }
        });
    };

    exportProducts() {
        let button = this.elem.export_products_button;
        button.css('color', 'black');
        setTimeout(() => button.css('color', '#005AD0'), 4000);
        window.location.href = window.fx_url.API + 'export/products';
    };

    /**
     * Loads the page and resets previous upload state.
     *
     * @param {object} request - The route request object.
     * @param {function} next - Callback to continue routing.
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        if (this.state.uppy) {
            this.state.uppy.reset();
        }

        this.parent.state.csv_columns = null;
        this.parent.state.csv_data = null;
        this.parent.state.import_data = null;

        this.parent.state.mapping = {
            id_user_defined: null,
            name: null,
            price: null,
            unit: null,
            description: null,
            pricing_disclaimer: null,
            category: null,
        };

        this.elem.export_products_button.on('click.fx', () => {
            this.exportProducts();
        });
    };

    /**
     * Cleans up page state and Uppy instance.
     *
     * @param {object} request - The route request object.
     * @param {function} next - Callback to continue routing.
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        if (this.state.uppy) {
            this.state.uppy.reset();
        }
        await super.unload(request, next);
    };

    /**
     * Initializes DOM elements and configures Uppy.
     *
     * @param {jQuery} root - The root DOM element.
     */
    boot(root) {
        super.boot(root);
        this.configureUppy();
        this.elem.export_products_button = root.fxFind('export-products');
    };

    /**
     * Renders the page using its Handlebars template.
     *
     * @returns {string} HTML string to render.
     */
    render() {
        return upload_tpl({
            close_route: "products.items.manager",
        });
    };
}

module.exports = Upload;
