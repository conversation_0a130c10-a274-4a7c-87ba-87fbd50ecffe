'use strict';

const accounting = require('accounting');

const {findChild, jsSelector, onEvent} = require("@ca-package/dom");
const FormValidator = require("@cas-validator-js");

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(NumberInput);

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

const edit_tpl = require('@cam-company-profile-tpl/pages/main-pages/additional-costs-pages/edit.hbs');
const additional_costs_tpl = require('@cam-company-profile-tpl/pages/main-pages/additional-costs-pages/items.hbs');

class Items extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            modals: {},
            table: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            },
            table_loaded: false,
        });
    };

    /**
     * Edit additional cost in table row
     *
     * @param {jQuery} content
     * @param {object} row
     */
    editAdditionalCost(content, row) {
        this.showLoader();
        let markup = null,
            markup_val = findChild(content, jsSelector('markup')).val();
        if (markup_val !== '' &&  markup_val !== '0') {
            markup = markup_val;
        }
        let data = {
            'name': findChild(content, jsSelector('name')).val(),
            'unit_id': findChild(content, jsSelector('unit')).val(),
            'cost': findChild(content, jsSelector('cost')).val(),
            'markup': markup,
            'unit_price': findChild(content, jsSelector('unit_price')).val(),
        };
        Api.Resources.AdditionalCosts().partialUpdate(row.data().id, data).then(() => {
            this.hideLoader();
            let message = createSuccessMessage('Additional cost edited successfully');
            this.router.main_route.layout.toasts.addMessage(message);
            this.state.table.toggleChildRow(row, {}, true);
            this.state.child_row_open = false;
            this.state.table.draw();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    console.log(item_errors);
                    this.hideLoader();
                    break;
                default:
                    this.hideLoader();
                    let message = createErrorMessage('Unable to save additional cost, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Calculate the unit price based on cost and markup
     */
    calculateUnitPrice(content) {
        let cost = parseFloat(findChild(content, jsSelector('cost')).val()),
            markup = findChild(content, jsSelector('markup')).val(),
            unit_price_field = findChild(content, jsSelector('unit_price')),
            value = 0;
        if (markup == 0 || markup === '') {
            markup = null;
        }

        if (markup !== null) {
            value = cost * (parseFloat(markup) / 100) + cost;
        } else {
            value = cost;
        }
        unit_price_field.val(value);
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    /**
     * Create the additional cost datatable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, {
            row_edit: true
        })
            .on('scope_change', () => {
                this.state.child_row_open = false;
            })
            .on('row_click', (data) => {
                if (this.state.child_row_open) {
                    return;
                }
                let row = this.state.table.getRowById(data.id);
                let row_data = this.state.table.getRowData(row);
                let units = this.state.units;
                for (let unit of units) {
                    if (row_data.unit !== unit.name) {
                        unit.selected = false;
                        continue;
                    }
                    unit.selected = true;
                }

                let markup = '';
                if (row_data.markup !== '') {
                    markup = parseFloat(row_data.markup).toFixed(2);
                }

                let template = edit_tpl({
                    name: row_data.name,
                    units,
                    cost: parseFloat(row_data.cost).toFixed(2),
                    markup,
                    unit_price: parseFloat(row_data.unit_price).toFixed(2),
                    has_products: row_data.product_items.length > 0,
                    products: row_data.product_items
                });

                this.state.table.toggleChildRow(row, {
                    title: 'Edit Additional Cost',
                    id: row_data.id,
                    content: template,
                    footer: {
                        label: 'Delete Cost',
                        icon: 'system--delete-bin-2-line'
                    }
                });
                this.state.child_row_open = true;
            })
            .on('child_row_rendered', (row, content) => {
                this.elem.form = findChild(content, jsSelector('form'));
                this.state.validator = FormValidator.init(this.elem.form)
                    .on('form:submit', () => {
                        this.editAdditionalCost(content, row);
                        return false;
                    });

                FormInput.init(findChild(content, jsSelector('cost')), {
                    type: NumberInput.Type.CURRENCY,
                    right_align: true,
                    allow_minus: false
                });

                FormInput.init(findChild(content, jsSelector('markup')), {
                    type: NumberInput.Type.PERCENTAGE,
                    right_align: true,
                    allow_minus: false
                });

                FormInput.init(findChild(content, jsSelector('unit_price')), {
                    type: NumberInput.Type.CURRENCY,
                    right_align: true,
                    allow_minus: false
                });

                let that = this;
                onEvent(findChild(content, jsSelector('cost')), 'change', function(e) {
                    e.preventDefault();
                    that.calculateUnitPrice(content);
                    return false;
                });

                onEvent(findChild(content, jsSelector('markup')), 'change', function(e) {
                    that.calculateUnitPrice(content);
                    return false;
                });
            })
            .on('row_cancel', (row) => {
                this.state.table.toggleChildRow(row, {}, true);
                this.state.child_row_open = false;
            })
            .on('row_save', () => {
                this.elem.form.trigger('submit');
            })
            .on('row_delete', (row) => {
                this.router.navigate('additional_costs.delete', {
                    additional_cost_id: this.state.table.getRowData(row).id
                });
            });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Additional Costs'
        });

        this.state.table.setFilterOptions({
            unit_id: {
                label: 'Unit',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: profile_data.filter_options.units
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            }
        });

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name',
                width: '50%',
                value: (data) => {
                    if (!data.name) {
                        return "";
                    }
                    return this.state.table.trimColumn(data.name, 40, true, true);
                },
            },
            markup: {
                label: 'Markup',
                width: '24%',
                value: (data) => {
                    if (data.markup !== null) {
                        return `${parseFloat(data.markup).toFixed(2)}%`;
                    }
                    return null;
                }
            },
            unit_price: {
                label: 'Unit Price',
                width: '24%',
                value: (data) => {
                    return accounting.formatMoney(data.unit_price);
                }
            },
            unit: {
                label: 'Unit',
                width: '24%'
            }
        });

        // set buttons config
        this.state.table.setButtons({
            import: {
                label: 'Import',
                action: () => {
                    this.router.navigate('additional_costs.import.upload')
                },
                type_class: 't-tertiary-icon',
                icon: 'system--upload-line'
            },
            export: {
                label: 'Export',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/additional-costs' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                },
                type_class: 't-tertiary-icon',
                icon: 'system--download-line'
            },
            add_unit: {
                label: 'New Additional Cost',
                action: () => {
                    this.router.navigate('additional_costs.add')
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.AdditionalCosts, (request) => {
            let additional_cost_relations = {
                'product_items': {}
            };
            request
                .fields(['id', 'name', 'unit_price', 'unit', 'markup', 'cost'])
                .filter('status', Api.Constants.AdditionalCosts.Status.ACTIVE)
                .relations(additional_cost_relations);
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Fetch units
     *
     * @returns {Promise}
     */
    async fetchUnits() {
        let {entities: units} = await Api.Resources.Units()
            .filter('status', Api.Constants.Units.Status.ACTIVE)
            .sort('name', 'asc')
            .all();

        this.state.units = units.map(entity => entity.data);
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        this.state.table.draw();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await this.fetchUnits();

        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // We have to force the filter to hide if it's still open when they unload
        this.state.table.hideFilterMenu();

        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return additional_costs_tpl();
    };
}

module.exports = Items;