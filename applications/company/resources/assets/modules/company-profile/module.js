'use strict';

require('./resources/sass/main.scss');

window.e$ = jQuery;

require('@cac-icon/grip.svg');
require('@cac-icon/info.svg');
require('@cac-icon/materials.svg');
require('@cac-icon/quickbooks.svg');
require('@cac-icon/wisetack.svg');

const $ = require('jquery');
window.$ = window.jQuery = $;

require('remixicon/icons/Arrows/arrow-left-line.svg');
require('remixicon/icons/Business/calendar-line.svg');
require('remixicon/icons/Business/mail-line.svg');
require('remixicon/icons/Communication/chat-3-line.svg');
require('remixicon/icons/Design/grid-fill.svg');
require('remixicon/icons/Design/ruler-2-line.svg');
require('remixicon/icons/Document/article-line.svg');
require('remixicon/icons/Document/draft-line.svg');
require('remixicon/icons/Document/file-copy-2-line.svg');
require('remixicon/icons/Document/file-list-line.svg');
require('remixicon/icons/Document/file-list-2-line.svg');
require('remixicon/icons/Document/file-paper-line.svg');
require('remixicon/icons/Document/file-paper-2-line.svg');
require('remixicon/icons/Document/file-user-line.svg');
require('remixicon/icons/Document/folder-open-line.svg');
require('remixicon/icons/Editor/draggable.svg');
require('remixicon/icons/Editor/link-unlink-m.svg');
require('remixicon/icons/Editor/node-tree.svg');
require('remixicon/icons/Finance/p2p-line.svg');
require('remixicon/icons/Media/notification-2-line.svg');
require('remixicon/icons/Logos/google-line.svg');
require('remixicon/icons/System/add-circle-line.svg');
require('remixicon/icons/System/checkbox-blank-circle-line.svg');
require('remixicon/icons/System/checkbox-circle-line.svg');
require('remixicon/icons/System/checkbox-circle-fill.svg');
require('remixicon/icons/System/eye-line.svg');
require('remixicon/icons/System/list-settings-line.svg');
require('remixicon/icons/System/toggle-line.svg');
require('remixicon/icons/System/refresh-line.svg');
require('remixicon/icons/System/settings-3-line.svg');
require('remixicon/icons/System/upload-cloud-2-line.svg');
require('remixicon/icons/System/upload-line.svg');
require('remixicon/icons/System/download-line.svg');
require('remixicon/icons/Weather/mist-line.svg');

require('spectrum-colorpicker');

require('@ca-package/dom/src/jquery_plugin');
require('@ca-submodule/validator');
require('@ca-submodule/validator/src/validators/file');
require('@ca-submodule/validator/src/validators/password');
require('@ca-submodule/validator/src/validators/multiple_emails');
require('@ca-submodule/validator/src/validators/timezones');
require('@ca-submodule/validator/src/validators/value_empty');

const {layout} = require('@ca-submodule/layout');

const CompanyProfile = require('./src/index');
window.CompanyProfile = new CompanyProfile(layout);
