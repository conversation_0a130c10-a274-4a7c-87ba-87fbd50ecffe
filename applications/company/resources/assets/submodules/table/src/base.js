'use strict';

const $ = require('jquery');
const lang = require('lodash/lang');
const isPlainObject = require('lodash/isPlainObject');
const includes = require('lodash/includes');
const size = require('lodash/size');
const qs = require('qs');
const Popper = require('popper.js');
const escape = require('lodash/escape');
const moment = require("moment-timezone");
const Inputmask = require('inputmask');
const Sortable = require('sortablejs');

const Api = require('@ca-package/api');

const debounce = require('@cac-js/utils/debounce');
const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');
require('@ca-package/dom/src/jquery_plugin');

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/checkbox'));
FormInput.use(require('@ca-submodule/form-input/src/date'));
FormInput.use(require('@ca-submodule/form-input/src/date_range'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const Tooltip = require('@ca-submodule/tooltip');

const base_tpl = require('@cas-table-tpl/base.hbs');
const header_tpl = require('@cas-table-tpl/header.hbs');
const toolbar_tpl = require('@cas-table-tpl/toolbar.hbs');
const row_action_tpl = require('@cas-table-tpl/row_action_button.hbs');
const row_edit_tpl = require('@cas-table-tpl/row_edit_button.hbs');
const row_menu_tpl = require('@cas-table-tpl/row_menu.hbs');
const row_menu_action_tpl = require('@cas-table-tpl/row_action.hbs');
const button_tpl = require('@cas-table-tpl/button.hbs');
const header_menu_tpl = require('@cas-table-tpl/header_menu.hbs');
const header_table_settings_button = require('@cas-table-tpl/header_table_settings_button.hbs');
const header_menu_action_tpl = require('@cas-table-tpl/header_action.hbs');
const bulk_action_menu_tpl = require('@cas-table-tpl/bulk_action_menu.hbs');
const bulk_action_tpl = require('@cas-table-tpl/bulk_action.hbs');
const filter_menu_tpl = require('@cas-table-tpl/filter_menu.hbs');
const filter_row_tpl = require('@cas-table-tpl/filter_row.hbs');
const filter_value_date_tpl = require('@cas-table-tpl/filter_value_date.hbs');
const filter_value_date_range_tpl = require('@cas-table-tpl/filter_value_date_range.hbs');
const filter_value_input_tpl = require('@cas-table-tpl/filter_value_input.hbs');
const filter_value_select_tpl = require('@cas-table-tpl/filter_value_select.hbs');
const table_settings_menu_tpl = require('@cas-table-tpl/table_settings_menu.hbs');
const table_settings_menu_row_tpl = require('@cas-table-tpl/table_settings_menu_row.hbs');
const checkbox_header_tpl = require('@cas-table-tpl/checkbox_header.hbs');
const checkbox_tpl = require('@cas-table-tpl/checkbox.hbs');
const edit_tpl = require('@cas-table-tpl/edit.hbs');

/**
 * @typedef {Object} Scope
 * @property {?string} search
 * @property {?Object} sorts
 * @property {?Object} filters
 * @property {?Object} pagination
 * @property {number} pagination.page
 * @property {number} pagination.per_page
 */

/**
 * @memberof module:Table
 */
class Base {
    /**
     * Table constructor
     *
     * @param {jQuery} container
     * @param {Object} config
     */
    constructor(container, config = {}) {
        const header_footer_height = {
            large: 135, // header + table header (subtract 1px for borders) + footer + gap spaces
            small: 135
        };

        this.events = {};
        this.state = {
            class: typeof config.class === 'string' ? config.class : '',
            idx: Base.__index++,
            search_icon_types: {
                SEARCH: 'search',
                CLEAR: 'close-circle'
            },
            table_api: null,
            booted: false,
            height: container.height(),
            width: container.width(),
            mobile_size: false,
            server_side: true,
            server_search: true,
            server_paginate: typeof config.server_paginate === 'boolean' ? config.server_paginate : true,
            // temporary fix for tooltip on older modules like customer and property
            load_tooltip: typeof config.load_tooltip === 'boolean' ? config.load_tooltip : true,
            use_table_settings: typeof config.use_table_settings === 'boolean' ? config.use_table_settings : false,
            table_settings_bulk_actions: typeof config.table_settings_bulk_actions === 'boolean' ? config.table_settings_bulk_actions : false,
            table_settings_map_view: typeof config.table_settings_map_view === 'boolean' ? config.table_settings_map_view : false,
            table_settings_bulk_actions_status: typeof config.table_settings_bulk_actions_status === 'boolean' ? config.table_settings_bulk_actions_status : false,
            table_settings_map_view_status: typeof config.table_settings_map_view_status === 'boolean' ? config.table_settings_map_view_status : false,
            header: {
                name: null,
                custom_search: false,
                search: false,
                search_placeholder: 'Search',
                filter_name: null
            },
            toolbar: {
                filter: false,
                settings: false
            },
            filters: {},
            total_results: null,
            search_timer: null,
            search_bar: null,
            search_icon: 'search',
            table_scope: {
                pagination: {
                    per_page: 25,
                    page: 1
                },
                search: null,
                filters: null,
                sorts: null
            },
            header_menu: null,
            header_menu_popper: null,
            header_menu_open: false,
            row_actions: null,
            bulk_actions: null,
            bulk_actions_menu: null,
            filter_menu: null,
            table_settings_menu: null,
            filter_name_options_used: [],
            checkbox: typeof config.checkbox === 'boolean' ? config.checkbox : false,
            checkboxes: [],
            row_menu: null,
            row_menu_id: null,
            row_menu_popper: null,
            bulk_menu_popper: null,
            filter_menu_popper: null,
            table_settings_menu_popper: null,
            row_menu_open: false,
            bulk_actions_menu_open: false,
            filter_menu_open: false,
            table_settings_menu_open: false,
            header_search_open: false,
            paging_enabled: typeof config.paging_enabled === 'boolean' ? config.paging_enabled : true,
            paging_type: Base.Paging.SIMPLE_NUMBERS,
            page_lengths: Array.isArray(config.page_lengths) ? config.page_lengths : [10, 25, 50, 100],
            page_length_all: typeof config.page_length_all === 'boolean' ? config.page_length_all : false,
            config: config,
            lang: Object.assign({
                no_rows: 'No data available'
            }, config.lang),
            scrolling_enabled: typeof config.scrolling_enabled === 'boolean' ? config.scrolling_enabled : true,
            responsive: typeof config.responsive === 'boolean' ? config.responsive : false,
            fixed_left_columns: false,
            has_fixed_columns: false,
            row_edit: typeof config.row_edit === 'boolean' ? config.row_edit : false,
            timezone: typeof config.timezone === 'string' ? config.timezone : null,
            processing: false,
            original_column_keys: null,
            original_columns: null,
            column_order: Array.isArray(config.column_order) ? config.column_order : [],
            column_visibility: Array.isArray(config.column_visibility) ? config.column_visibility : [],
            no_header: typeof config.no_header === 'boolean' ? config.no_header : false
        };

        if (this.state.width < 520) {
            this.state.mobile_size = true;
            this.state.height = this.state.height - header_footer_height.small;
            this.state.paging_type = Base.Paging.SIMPLE;
        } else {
            this.state.height = this.state.height - header_footer_height.large;
        }
        if (this.state.row_actions === null) {
            this.state.height = this.state.height - 4;
        }
        if (this.state.no_header === true) {
            this.state.height = this.state.height + 32;
        }

        this.elem = {
            container: container
        };

        // Set default paging type and update if config paging type is defined
        this.state.paging_type = PagingTypes[this.state.paging_type];
        if (this.state.config.paging_type !== undefined && !this.state.mobile_size) {
            this.state.paging_type = PagingTypes[this.state.config.paging_type];
        }
    };

    /**
     * Generate url from scope object
     *
     * @param {Object} scope
     * @returns {string}
     */
    static buildUrlFromScope(scope) {
        let url_scope = {};
        if (scope.search !== null) {
            url_scope._search = scope.search;
        }
        if (size(scope.sorts) > 0) {
            let sorts = [];
            for (let name in scope.sorts) {
                let direction = scope.sorts[name];
                sorts.push((direction === 'desc' ? '-' : '') + name);
            }
            url_scope._sorts = sorts.join(',');
        }

        for (let key in scope.filters) {
            let scope_filter = scope.filters[key];
            let filter = null;
            if (Array.isArray(scope_filter)) {
                switch(scope_filter.length) {
                    case 1:
                        filter = scope_filter[0];
                        break;
                    case 2:
                        if (Array.isArray(scope_filter[1])) {
                            filter = `${scope_filter[0]}:${scope_filter[1].join('|')}`;
                        } else if (typeof scope_filter[0] === 'string') {
                            filter = `${scope_filter[0]}:${scope_filter[1]}`;
                        }
                        break;
                    case 3:
                        if (Array.isArray(scope_filter[2])) {
                            filter = `${scope_filter[0]}:${scope_filter[1]}:${scope_filter[2].join('|')}`;
                        } else if (typeof scope_filter[1] === 'string') {
                            filter = `${scope_filter[0]}:${scope_filter[1]}:${scope_filter[2]}`;
                        }
                        break;
                }
            } else if (typeof scope_filter === 'string') {
                filter = scope_filter;
            }

            if (filter !== null) {
                url_scope[key] = filter;
            } else if (scope_filter !== null) {
                url_scope[key] = scope_filter;
            }
        }

        if (scope.pagination.per_page) {
            url_scope._per_page = scope.pagination.per_page;
        }
        if (scope.pagination.page) {
            url_scope._page = scope.pagination.page;
        }
        let string = '';
        if (size(url_scope) > 0) {
            string += '?' + qs.stringify(url_scope, {encode: false});
        }
        return string;
    };

    /**
     * Generate scope object from data
     *
     * @param {(string|Object)} scope_data
     * @param {?string} scope_data._search
     * @param {?string} scope_data._sorts
     * @param {?number} scope_data._page
     * @param {?number} scope_data._per_page
     * @returns {Scope}
     */
    static buildScopeFromQuery(scope_data) {
        if (typeof scope_data === 'string') {
            scope_data = qs.parse(scope_data);
        }
        let scope_object = {};
        let scope_filters = {};
        let scope_pagination = {};
        for (let scope in scope_data) {
            let this_scope = scope_data[scope];
            switch(scope) {
                case '_search':
                    scope_object['search'] = this_scope;
                    break;
                case '_sorts':
                    let sorts = this_scope.split(',');
                    scope_object.sorts = {};
                    for (let sort in sorts) {
                        let this_sort = sorts[sort];
                        let direction = this_sort.charAt(0) === '-' ? Base.Sort.DESC : Base.Sort.ASC;
                        if (direction === Base.Sort.DESC) {
                            this_sort = this_sort.slice(1);
                        }
                        scope_object.sorts[this_sort] =  direction;
                    }
                    break;
                case '_per_page':
                    scope_pagination['per_page'] = parseInt(this_scope);
                    break;
                case '_page':
                    scope_pagination['page'] = parseInt(this_scope);
                    break;
                default:
                    scope_filters[scope] = this_scope;
                    break;
            }
        }
        if (size(scope_filters) > 0) {
            let filters = {};
            let operators = Object.values(Base.Operators);
            let modifiers = Object.values(Base.Modifiers);
            for (let key in scope_filters) {
                let scope_filter = scope_filters[key];

                let position = scope_filter.indexOf(':'),
                    operator = null,
                    modifier = null,
                    value = null;
                if (position > 0) {
                    operator = scope_filter.substring(0, position);
                    if (!includes(operators, operator)) {
                        operator = null;
                    } else {
                        scope_filter = scope_filter.substring(position + 1);
                    }
                }

                position = scope_filter.indexOf(':');
                if (position > 0) {
                    modifier = scope_filter.substring(0, position);
                    if (!includes(modifiers, modifier)) {
                        modifier = null;
                    } else {
                        scope_filter = scope_filter.substring(position + 1);
                    }
                }

                switch(operator) {
                    case 'in':
                    case 'not-in':
                    case 'between':
                    case 'not-between':
                        let values = scope_filter.split('|');
                        let filter = [];
                        for (let value of values) {
                            value = value.trim();
                            if (value === '(NULL)') {
                                value = null;
                            }
                            filter.push(value);
                        }
                        scope_filter = filter;
                        break;
                    default:
                        scope_filter = scope_filter.trim();
                        if (scope_filter === '(NULL)') {
                            scope_filter = null;
                        }
                        break;
                }
                if (operator && modifier) {
                    value = [operator, modifier, scope_filter]
                } else if (operator) {
                    value = [operator, scope_filter]
                } else {
                    value = scope_filter;
                }
                filters[key] = value;
            }

            if (size(filters) > 0) {
                scope_object['filters'] = filters;
            }
        }
        if (size(scope_pagination) > 0) {
            scope_object['pagination'] = scope_pagination;
        }
        return scope_object;
    };

    /**
     * Get data types
     *
     * @readonly
     *
     * @returns {{DATA: number, AJAX: number}}
     */
    static get DataTypes() {
        return {
            AJAX: 1,
            DATA: 2
        };
    };

    /**
     * Get paging
     *
     * @readonly
     *
     * @returns {{NUMBERS: number, SIMPLE: number, SIMPLE_NUMBERS: number, FULL: number, FULL_NUMBERS: number, FIRST_LAST_NUMBERS: number}}
     */
    static get Paging() {
        return {
            NUMBERS: 1,
            SIMPLE: 2,
            SIMPLE_NUMBERS: 3,
            FULL: 4,
            FULL_NUMBERS: 5,
            FIRST_LAST_NUMBERS: 6
        };
    };

    /**
     * Get sort
     *
     * @readonly
     *
     * @returns {{ASC: string, DESC: string}}
     */
    static get Sort() {
        return {
            ASC: 'asc',
            DESC: 'desc'
        };
    };

    /**
     * Get align
     *
     * @readonly
     *
     * @returns {{LEFT: number, CENTER: number, RIGHT: number}}
     */
    static get Align() {
        return {
            LEFT: 1,
            CENTER: 2,
            RIGHT: 3
        };
    };

    /**
     * Get operators
     *
     * @readonly
     *
     * @returns {{GREATER_THAN_EQUAL: string, NOT_BETWEEN: string, IN: string, EQUAL: string, GREATER_THAN: string, NOT_EQUAL: string, BETWEEN: string, LESS_THAN_EQUAL: string, NOT_IN: string, LESS_THAN: string, NULL: string, NOT_NULL: string}}
     */
    static get Operators() {
        return {
            EQUAL: 'eq',
            NOT_EQUAL: 'not-eq',
            LESS_THAN: 'lt',
            LESS_THAN_EQUAL: 'lte',
            GREATER_THAN: 'gt',
            GREATER_THAN_EQUAL: 'gte',
            IN: 'in',
            NOT_IN: 'not-in',
            BETWEEN: 'between',
            NOT_BETWEEN: 'not-between',
            NULL: 'null',
            NOT_NULL: 'not-null'
        };
    };

    /**
     * Get modifiers
     *
     * @readonly
     *
     * @returns {{DATE: string, DATETIME: string, TIME: string}}
     */
    static get Modifiers() {
        return {
            DATETIME: 'datetime',
            DATE: 'date',
            TIME: 'time'
        };
    };

    /**
     *
     * @returns {{SELECT: number, DATE: number, FREEFORM: number, BOOLEAN: number}}
     * @constructor
     */
    static get FilterValueTypes() {
        return {
            SELECT: 1,
            DATE: 2,
            FREEFORM: 3,
            BOOLEAN: 4,
            INTEGER: 5
        };
    };

    /**
     * Get table scope
     *
     * @readonly
     *
     * @returns {Scope}
     */
    get scope() {
        return this.state.table_scope;
    };

    /**
     * Get bulk actions button so that it can be disabled
     *
     * @returns {jQuery}
     */
    get bulkActionsButton() {
        return this.elem.bulk_actions_button;
    };

    /**
     * Merge header settings with state header
     *
     * @param {Object} header
     */
    setHeader(header) {
        Object.assign(this.state.header, header);
    };

    /**
     * Merge toolbar settings with state toolbar
     *
     * @param {Object} toolbar
     */
    setToolbar(toolbar) {
        Object.assign(this.state.toolbar, toolbar);
    };

    /**
     * Set state columns and column keys
     *
     * @param {Object} columns
     */
    setColumns(columns) {
        this.state.column_keys = Object.keys(columns);
        this.state.original_columns = columns;
        this.state.original_column_keys = Object.keys(columns);

        // get any pre-saved visibility settings
        let visibility = {};
        if (this.state.column_visibility.length > 0) {
            for (let item of this.state.column_visibility) {
                let this_item = item.split(':'),
                    value = this_item[1] === 'true' ? true : false;
                visibility[this_item[0]] = value
            }
        }

        // if column order is preset (from cookie or other setting) then set the order from the column order state
        // otherwise set the order based on the object passed into the table library
        if (this.state.column_order.length > 0) {
            // if preset contains checkbox and the checkbox state is off then we need to remove it
            if (!this.state.checkbox && this.state.column_order[0] === 'checkbox') {
                this.state.column_order.shift();
            }
            // if preset doesn't contain checkbox and the checkbox state is on then we need to add it
            if (this.state.checkbox && this.state.column_order[0] !== 'checkbox') {
                this.state.column_order.unshift('checkbox');
            }

            let new_columns = {};
            for (let item of this.state.column_order) {
                let this_column = columns[item];
                if (this_column === undefined) {
                    continue;
                }
                if (visibility[item] !== undefined) {
                    this_column.original_visiblility = this_column.visible !== undefined ? this_column.visible : true;
                    this_column.visible = visibility[item];
                } else {
                    let visibility = this_column.visible !== undefined ? this_column.visible : true;
                    this_column.original_visiblility = visibility;
                    this_column.visible = visibility;
                }
                new_columns[item] = this_column;
            }
            this.state.columns = new_columns;
            return;
        }
        // if no presets exist then loop through the columns as originally set
        let new_columns = {};
        for (let item in columns) {
            let this_column = columns[item];

            let visibility = this_column.visible !== undefined ? this_column.visible : true;
            this_column.original_visiblility = visibility;
            this_column.visible = visibility;

            new_columns[item] = this_column;
        }
        this.state.columns = new_columns;
    };

    /**
     * Set filter options for filter menu
     *
     * @param {Object} filter_options
     */
    setFilterOptions(filter_options) {
        this.state.filter_options = filter_options;
    };

    /**
     * Set state row actions
     *
     * @param {Object} actions
     */
    setRowActions(actions) {
        this.state.row_actions = actions;
    };

    /**
     * Set state buttons
     *
     * @param {Object} buttons
     */
    setButtons(buttons) {
        this.state.buttons_default = buttons;
    };

    /**
     * Set state bulk actions
     *
     * @param {Object} bulk_actions
     */
    setBulkActions(bulk_actions) {
        this.state.bulk_actions = bulk_actions;
    };

    /**
     * Set state data to DATA type
     *
     * Relies on client side data to be passed into the table, therefore server side settings are set to false
     *
     * @param {(Object[]|Function|Promise)} data
     */
    setData(data) {
        this.state.data = {
            type: Base.DataTypes.DATA,
            source: data
        };
        this.state.server_side = false;
        this.state.server_search = false;
    };

    /**
     * Set state data to AJAX type
     *
     * Relies on DataTables to make the request with build in Ajax function
     *
     * @param {Function} resource - Api resource function that returns a request instance
     * @param {Function} callback - additional settings for resource request
     */
    setAjax(resource, callback) {
        this.state.data = {
            type: Base.DataTypes.AJAX,
            source: resource,
            callback: callback
        };
    };

    /**
     * Set table state and scope
     *
     * Pagination, filters, sorts, search can be applied to scope
     *
     * @param {Object} config
     */
    setState(config) {
        if (typeof config.pagination === 'object') {
            if (typeof config.pagination.per_page === 'number' && includes(this.state.page_lengths, config.pagination.per_page)) {
                this.state.table_scope.pagination.per_page = config.pagination.per_page;
            }

            if (typeof config.pagination.page === 'number') {
                this.state.table_scope.pagination.page = config.pagination.page;
            }
        }

        if (config.filters !== undefined) {
            let filters = {};
            for (let key in config.filters) {
                let config_filter = config.filters[key];

                let filter = null;
                // this is for null operators only
                if (config_filter === 'null' || config_filter === 'not-null') {
                    filter = [config_filter];
                } else if (lang.isArray(config_filter)) {
                    let operator = null,
                        modifier = null,
                        value = null;
                    if (typeof config_filter[0] === 'string' && Operators[config_filter[0]] !== undefined) {
                        operator = config_filter[0];
                    }
                    if (typeof config_filter[1] === 'string' && Modifiers[config_filter[1]]) {
                        modifier = config_filter[1];
                    } else {
                        value = config_filter[1];
                    }
                    if (config_filter[2] !== undefined) {
                        value = config_filter[2];
                    }

                    if (!operator || !value) {
                        continue;
                    }
                    if (modifier) {
                        filter = [operator, modifier, value];
                    } else {
                        filter = [operator, value];
                    }
                } else if (typeof config_filter === 'string') {
                    filter = config_filter;
                }

                if (filter !== null) {
                    filters[key] = filter;
                }
            }
            if (size(filters) > 0) {
                this.state.table_scope.filters = filters;
            }
        }

        if (config.sorts !== undefined) {
            let sorts = {};
            for (let sort in config.sorts) {
                let column = this.state.columns[sort];
                if (column !== undefined && SortTypes[config.sorts[sort]] !== undefined && column.orderable !== false) {
                    sorts[sort] = config.sorts[sort]
                }
            }
            if (size(sorts) > 0) {
                this.state.table_scope.sorts = sorts;
            }
        }

        if (config.search !== undefined && config.search !== null) {
            let search_term = config.search.trim();
            if (search_term.length > 0) {
                this.state.table_scope.search = search_term;
            }
        }
    };

    /**
     * Switch search bar icon
     *
     * @param {string} icon_name - new icon name
     */
    changeSearchIcon(icon_name) {
        this.elem.search_icon.parent().removeClass(this.state.search_icon).addClass(icon_name);
        this.state.search_icon = icon_name;
        this.elem.search_icon[0].setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#remix-icon--system--${icon_name}-line`);
    };

    /**
     * Get rows from table
     *
     * @returns {object[]}
     */
    getRows() {
        return this.state.table_api.rows().data();
    };

    /**
     * Get nodes from table
     *
     * @returns {object}
     */
    getNodes() {
        return this.state.table_api.rows().nodes();
    };

    /**
     * Get row id with custom id format
     *
     * @param {number} id - row id
     * @returns {string} - id with prefixed fx
     */
    getRowId(id) {
        return `#fx-${this.state.idx}-${id}`;
    };

    /**
     * Get checked rows from table
     *
     * @returns {array}
     */
    getCheckedRows() {
        let row_data = this.getRows();
        let checked_ids = [];
        for (let i = 0; i < row_data.length; i++) {
            let row_id = row_data[i].id,
                checkbox = this.getCheckbox(`fx-${this.state.idx}-${row_id}`);
            if (checkbox === undefined || !checkbox.checked) {
                continue;
            }
            checked_ids.push(row_id)
        }
        return checked_ids;
    }

    /**
     * Get specific checkbox in row
     *
     * @param {string} id
     * @returns {object}
     */
    getCheckbox(id) {
        return this.state.checkboxes[`row_${id}`];
    };

    /**
     * Get the check all checkbox in table header
     *
     * @returns {object}
     */
    getCheckAllCheckbox() {
        return this.state.checkboxes.select_all;
    };

    /**
     * Toggle checkboxes
     *
     * @param {boolean|null} status
     */
    toggleCheckAll(status = null) {
        if (!status) {
            status = !this.elem.check_all.is(':checked') ?? false;
        }
        this.elem.check_all.prop('checked', status).trigger('change');
    }

    /**
     * Get row from table using id
     *
     * @param {number} id - row id
     * @returns {(Object|null)}
     */
    getRowById(id) {
        let row = this.state.table_api.row(this.getRowId(id));
        if (row.length === 0) {
            return null;
        }
        return row;
    };

    /**
     * Get row from table using row element
     *
     * @param {Object} selector
     * @returns {(null|Object)}
     */
    getRow(selector) {
        let row = this.state.table_api.row(selector);
        if (row.length === 0) {
            return null;
        }
        return row;
    };

    /**
     * Toggle table child row
     *
     * If child row isn't showing, add config title and content then append to wrapper template and show
     *
     * @param {object} row
     * @param {object} config
     * @param {boolean} force_close
     */
    toggleChildRow(row, config, force_close = false) {
        let row_item = $(row.node());
        let method = '';
        if (row.child.isShown() || force_close) {
            row_item.removeClass('child-shown');
            row.child.hide();
            method = 'removeClass';
        } else {
            row_item.addClass('child-shown');
            let template = $(edit_tpl({
                title: config.title,
                content: config.content,
                footer: config.footer !== undefined ? config.footer : null,
                disable_save: config.disable_save !== undefined ? config.disable_save : false
            }));
            this.elem.row_cancel = template.fxFind('cancel');
            this.elem.row_save = template.fxFind('save');
            this.elem.row_delete = template.fxFind('delete');
            this.elem.row_content = template.fxFind('content');
            row.child(template, 'child-row').show();
            Tooltip.initAll(this.elem.row_content);
            method = 'addClass';
            this.fire('child_row_rendered', row, this.elem.row_content);

            this.elem.row_cancel.on('click.fx', (e) => {
                e.preventDefault();
                this.fire('row_cancel', row);
                return false;
            });
            this.elem.row_save.on('click.fx', (e) => {
                e.preventDefault();
                this.fire('row_save', row, this.elem.row_content);
                return false;
            });
            this.elem.row_delete.on('click.fx', (e) => {
                e.preventDefault();
                this.fire('row_delete', row, this.elem.row_content);
                return false;
            });
        }

        let rows = this.getNodes();

        for (let i = 0; i < rows.length; i++) {
            let $row = $(rows[i]);
            if ($row.hasClass('child-shown')) {
                continue;
            }
            $row[method]('t-disabled');
        }
    };

    /**
     * Get row data
     *
     * @param {Object} row - row selector
     * @returns {Object}
     */
    getRowData(row) {
        let table_row = this.getRow(row);
        if (table_row !== null) {
            return table_row.data();
        }
    };

    /**
     * Add row to the table and redraw
     *
     * @param {Object} data
     * @param {boolean} [draw=true]
     */
    addRow(data, draw = true) {
        this.state.table_api.row.add(data);
        if (draw) {
            this.draw();
        }
    };

    /**
     * Remove row from the table and redraw
     *
     * @param {number} id
     * @param {boolean} [draw=true]
     */
    deleteRow(id, draw = true) {
        let row = this.getRowById(id);
        if (row === null) {
            return;
        }
        row.remove();
        if (draw) {
            this.draw();
        }
    };

    /*
    * Empty table rows
    *
    * @param {boolean} [draw=true]
    */
    clearTable(draw = true) {
        let table = this.state.table_api;
        table.clear();
        if (draw) {
            table.draw();
        }
    };

    /**
     * Update row data in the table and redraw
     *
     * @param {Object} data
     * @param {(Object|null)} row - datatables row object
     * @param {boolean} [draw=true]
     */
    updateRow(data, row = null, draw = true) {
        if (row === null) {
            row = this.getRowById(data.id);
        }
        row.data(data);
        if (draw) {
            this.draw();
        }
    };

    /**
     * Redraw the table
     *
     * @param {boolean} [reset_paging=true] - reset the page number of the table when redrawn
     */
    draw(reset_paging = true) {
        this.state.table_api.draw(reset_paging);
    };

    /**
     * Return or set state row menu
     *
     * @returns {Object} {row_menu}
     */
    getRowMenu() {
        if (this.state.row_menu === null) {
            let row_menu = {
                elem: $(row_menu_tpl()),
                actions: {}
            };
            for (let action in this.state.row_actions) {
                let row_action = this.state.row_actions[action];
                row_action.visible = row_action.visible === undefined ? true : row_action.visible;
                if (typeof row_action.visible === 'boolean' && !row_action.visible) {
                    continue;
                }
                row_action.negate = row_action.negate || false;
                row_action.confirm = row_action.confirm || false;
                row_action.disabled = row_action.disabled || false;
                row_action.elem = $(row_menu_action_tpl({
                    action,
                    label: row_action.label,
                    disabled: row_action.disabled,
                    confirm: row_action.confirm,
                    negate: row_action.negate
                }));
                row_menu.elem.append(row_action.elem);
                row_menu.actions[action] = row_action;
            }
            $('body').append(row_menu.elem);
            this.state.row_menu = row_menu;

            let that = this;
            this.state.row_menu.elem.fxEventWatcher(['click', 'touchstart'], 'action', function(e) {
                let $this = $(this);
                if ($this.hasClass('t-disabled')) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
                let action = that.state.row_menu.actions[$this.data('action')].action;
                if (typeof action !== 'function') {
                    return true;
                }
                e.preventDefault();
                e.stopPropagation();
                let data = that.getRowData(that.state.table_api.row(that.getRowId(that.state.row_menu_id)));
                action(data);
                that.hideRowMenu();
                return false;
            });
        }
        return this.state.row_menu;
    };

    /**
     * Use popper to show row menu using row data
     *
     * @param {Object} element - row menu element to bind menu
     * @param {Object} data
     */
    showRowMenu(element, data) {
        let row_menu = this.getRowMenu();

        for (let action in row_menu.actions) {
            let row_action = row_menu.actions[action],
                visible = row_action.visible,
                disabled = row_action.disabled;
            row_action.elem.removeAttr('href target').show().removeClass('t-disabled');
            if (typeof row_action.visible === 'function') {
                visible = visible(data, this);
            }
            if (!visible) {
                row_action.elem.hide();
                continue;
            }
            if (typeof row_action.disabled === 'function') {
                disabled = disabled(data, this);
            }
            if (disabled) {
                row_action.elem.addClass('t-disabled');
            }
            if (row_action.link !== undefined) {
                let href = row_action.link.href;
                if (typeof href === 'function') {
                    href = href(data, this);
                }
                row_action.elem.attr('href', href);
                if (row_action.link.target !== undefined) {
                    row_action.elem.attr('target', row_action.link.target);
                }
            }
        }

        row_menu.elem.show();
        this.state.row_menu_id = data.id;
        let modifiers = {};
        if (this.state.has_fixed_columns) {
            modifiers.preventOverflow = {
                boundariesElement: $(element).parents('.DTFC_ScrollWrapper')[0]
            };
        }
        this.state.row_menu_popper = new Popper(element, row_menu.elem[0], {
            placement: 'left-start',
            modifiers
        });
        this.state.row_menu_open = true;
    };

    /**
     * Hide row menu and destroy popper instance
     */
    hideRowMenu() {
        if (!this.state.row_menu_open) {
            return;
        }
        this.state.row_menu.elem.hide();
        this.state.row_menu_popper.destroy();
        this.state.row_menu_popper = null;
        this.state.row_menu_open = false;
        this.state.row_menu_id = null;
    };

    /**
     * Toggle row menu using state row menu open status
     */
    toggleRowMenu(element, data) {
        if (this.state.row_menu_open) {
            this.hideRowMenu();
        } else {
            this.showRowMenu(element, data);
        }
    };

    /**
     * Return or set state bulk actions menu
     *
     * @returns {Object} {row_menu}
     */
    getBulkActionsMenu() {
        if (this.state.bulk_actions_menu === null) {
            let bulk_menu = {
                elem: $(bulk_action_menu_tpl()),
                actions: {}
            };

            for (let action in this.state.bulk_actions) {
                let bulk_action = this.state.bulk_actions[action];
                bulk_action.visible = bulk_action.visible === undefined ? true : bulk_action.visible;
                if (typeof bulk_action.visible === 'boolean' && !bulk_action.visible) {
                    continue;
                }
                bulk_action.negate = bulk_action.negate || false;
                bulk_action.confirm = bulk_action.confirm || false;
                bulk_action.disabled = bulk_action.disabled || false;
                bulk_action.elem = $(bulk_action_tpl({
                    action,
                    label: bulk_action.label,
                    disabled: bulk_action.disabled,
                    confirm: bulk_action.confirm,
                    negate: bulk_action.negate
                }));
                bulk_menu.elem.append(bulk_action.elem);
                bulk_menu.actions[action] = bulk_action;
            }
            $('body').append(bulk_menu.elem);
            this.state.bulk_actions_menu = bulk_menu;

            let that = this;
            this.state.bulk_actions_menu.elem.fxEventWatcher(['click', 'touchstart'], 'action', function(e) {
                let $this = $(this);
                if ($this.hasClass('t-disabled')) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
                let action = that.state.bulk_actions_menu.actions[$this.data('action')].action;
                if (typeof action !== 'function') {
                    return true;
                }
                e.preventDefault();
                e.stopPropagation();
                let ids = [];
                for (let row in that.state.checkboxes) {
                    if (that.state.checkboxes[row].checked) {

                    }
                }
                // let data = that.getRowData(that.state.table_api.row(that.getRowId(that.state.row_menu_id)));
                action(ids);
                that.hideBulkActionsMenu();
                return false;
            });
        }
        return this.state.bulk_actions_menu;
    };

    /**
     * Use popper to show bulk menu
     *
     * @returns {Object} element - row menu element to bind menu
     * @returns {Object} data
     */
    showBulkActionsMenu() {
        let bulk_menu = this.getBulkActionsMenu();
        let data = null;

        for (let action in bulk_menu.actions) {
            let bulk_action = bulk_menu.actions[action],
                visible = bulk_action.visible,
                disabled = bulk_action.disabled;
            bulk_action.elem.removeAttr('href target').show().removeClass('t-disabled');
            if (typeof bulk_action.visible === 'function') {
                visible = visible(data, this);
            }
            if (!visible) {
                bulk_action.elem.hide();
                continue;
            }
            if (typeof bulk_action.disabled === 'function') {
                disabled = disabled(data, this);
            }
            if (disabled) {
                bulk_action.elem.addClass('t-disabled');
            }
            if (bulk_action.link !== undefined) {
                let href = bulk_action.link.href;
                if (typeof href === 'function') {
                    href = href(data, this);
                }
                bulk_action.elem.attr('href', href);
                if (bulk_action.link.target !== undefined) {
                    bulk_action.elem.attr('target', bulk_action.link.target);
                }
            }
        }

        bulk_menu.elem.show();

        this.state.bulk_menu_popper = new Popper(this.elem.bulk_actions_button[0], bulk_menu.elem[0], {
            placement: 'bottom-start'
        });
        this.state.bulk_actions_menu_open = true;
    };

    /**
     * Hide bulk menu and destroy popper instance
     */
    hideBulkActionsMenu() {
        if (!this.state.bulk_actions_menu_open) {
            return;
        }
        this.state.bulk_actions_menu.elem.hide();
        this.state.bulk_menu_popper.destroy();
        this.state.bulk_menu_popper = null;
        this.state.bulk_actions_menu_open = false;
    };

    /**
     * Toggle bulk actions menu using state bulk menu open status
     */
    toggleBulkActionsMenu() {
        if (this.state.bulk_actions_menu_open) {
            this.hideBulkActionsMenu();
        } else {
            this.showBulkActionsMenu();
        }
    };

    /**
     * Get operators based on filter value type
     *
     * @returns {object}
     */
    getOperatorTypes() {
        return{
            [Base.FilterValueTypes.SELECT]: [
                {
                    label: 'is equal to',
                    value: 'eq',
                },
                {
                    label: 'is not equal to',
                    value: 'not-eq',
                },
                {
                    label: 'is any of',
                    value: 'in',
                },
                {
                    label: 'is none of',
                    value: 'not-in',
                },
                {
                    label: 'is blank',
                    value: 'null',
                },
                {
                    label: 'is not blank',
                    value: 'not-null',
                }
            ],
            [Base.FilterValueTypes.BOOLEAN]: [
                {
                    label: 'is',
                    value: 'eq',
                }
            ],
            [Base.FilterValueTypes.DATE]: [
                // {
                //     label: 'is equal to',
                //     value: 'eq',
                // },
                // {
                //     label: 'is not equal to',
                //     value: 'not-eq',
                // },
                {
                    label: 'is before',
                    value: 'lt',
                },
                {
                    label: 'is after',
                    value: 'gt',
                },
                {
                    label: 'is between',
                    value: 'between',
                }
            ],
            [Base.FilterValueTypes.INTEGER]: [
                // {
                //     label: 'is equal to',
                //     value: 'eq',
                // },
                // {
                //     label: 'is not equal to',
                //     value: 'not-eq',
                // },
                {
                    label: 'is less than',
                    value: 'lt',
                },
                {
                    label: 'is greater than',
                    value: 'gt',
                }
            ]
        };
    };

    /**
     * Find current operator and value if existing_filter is passed into row
     *
     * @param {Array|string} existing_filter
     */
    getExistingOperatorValue(existing_filter) {
        let existing_filter_operator = null,
            existing_filter_value = null;
        if (Array.isArray(existing_filter)) {
            switch(existing_filter.length) {
                case 1:
                    existing_filter_operator = existing_filter[0];
                    break;
                case 2:
                    if (Array.isArray(existing_filter[1])) {
                        existing_filter_operator = existing_filter[0];
                        existing_filter_value = existing_filter[1];
                    } else if (typeof existing_filter[0] === 'string') {
                        existing_filter_operator = existing_filter[0];
                        existing_filter_value = existing_filter[1];
                    }
                    break;
                case 3:
                    if (Array.isArray(existing_filter[2])) {
                        existing_filter_operator = existing_filter[0];
                        existing_filter_value = `${existing_filter[1]}:${existing_filter[2]}`;
                    } else if (typeof existing_filter[1] === 'string') {
                        existing_filter_operator = existing_filter[0];
                        existing_filter_value = `${existing_filter[1]}:${existing_filter[2]}`;
                    }
                    break;
            }
        } else if (typeof existing_filter === 'string') {
            existing_filter_value = existing_filter;
        }
        return [existing_filter_operator, existing_filter_value]
    };

    addFilterMenuRow(existing_filter_name = null, existing_filter = null) {
        // check if the last row is already blank, if so we don't need to run this function
        let stored_rows = this.state.filter_menu.rows,
            row_index = 0,
            stored_rows_keys = Object.keys(stored_rows),
            last_row = stored_rows[stored_rows_keys[stored_rows_keys.length - 1]];

        if (last_row !== undefined) {
            row_index = last_row.buttons.delete_row.data('index') + 1;
        }

        if (stored_rows_keys.length > 0 && last_row.inputs.name_input.val() === '') {
            return;
        }

        let row_elem = $(filter_row_tpl({
                index: row_index
            })),
            name_input = row_elem.fxFind('name'),
            operator_input = row_elem.fxFind('operator'),
            value_field = row_elem.fxFind('value-field'),
            delete_row = row_elem.fxFind('delete');

        initSelectPlaceholder(name_input);

        // Loop through filter options to build name dropdown and select current one if it's passed into row
        for (let item in this.state.filter_options) {
            let this_option = this.state.filter_options[item],
                selected = existing_filter_name === item,
                // check if item has been used and don't allow it to be chosen again
                disabled = this.state.filter_name_options_used.includes(item);
            if (this_option.visible !== undefined && !this_option.visible) {
                continue;
            }
            var name_option = $('<option/>')
                .attr('value', item)
                .text(this_option.label)
                .attr('data-type', this_option.type)
                .prop('selected', selected)
                .prop('disabled', disabled);
            name_input.append(name_option);
        }

        let [existing_filter_operator, existing_filter_value] = this.getExistingOperatorValue(existing_filter);

        let that = this;
        name_input.on('focusin.fx', function (e) {
            let this_elem = $(e.target);
            let value = this_elem.val();
            that.enableFilterName(value);
        });
        name_input.on('focusout.fx', function (e) {
            let this_elem = $(e.target);
            let value = this_elem.val();
            that.disableFilterName(value);
        });

        // If name dropdown changes fill in operator dropdown and add value dropdown or value field
        name_input.on('change.fx', function (e) {
            e.stopPropagation();
            e.preventDefault();
            if (existing_filter_name === null) {
                that.addFilterMenuRow();
            }

            let this_elem = $(e.target);
            let value = this_elem.val();
            let this_row = this_elem.parent().parent().parent();
            let filter_options = that.state.filter_options[value];

            that.disableFilterName(value);

            // Find operator options for type and add them to the dropdown
            let operator_select_input = this_row.fxFind('operator');
            operator_select_input.empty();

            let value_required = false;
            // we don't allow null or not null if field is required
            if (filter_options.field_required !== undefined) {
                value_required = filter_options.field_required
            }
            let operator_types = that.getOperatorTypes();
            for (let item in operator_types[filter_options.type]) {
                let this_operator = operator_types[filter_options.type][item],
                    selected = existing_filter_name === value && existing_filter_operator === this_operator.value;
                if (value_required && (this_operator.value === Base.Operators.NULL || this_operator.value === Base.Operators.NOT_NULL)) {
                    continue;
                }
                let operator_option = $('<option/>')
                    .attr('value', this_operator.value)
                    .text(this_operator.label)
                    .prop('selected', selected);
                operator_select_input.append(operator_option);
            }
            operator_select_input.addClass('t-show');
            operator_input.trigger('change');
        });

        // Add value options or date input when the operator is chosen
        operator_input.on('change.fx', function (e) {
            e.stopPropagation();
            e.preventDefault();

            let this_elem = $(e.target),
                value = this_elem.val(),
                this_row = this_elem.parent().parent().parent(),
                this_row_filter_name = this_row.fxFind('name').val(),
                filter_options = that.state.filter_options[this_row_filter_name];

            value_field.empty();

            // this is a catch for null or not null operators, we don't append a value input to the value field
            if (value === Base.Operators.NULL || value === Base.Operators.NOT_NULL) {
                that.applyFilterMenu();

            // if filter contains options then use the value dropdown or the multiple select 2 value dropdown
            } else if (filter_options.options !== undefined && Object.keys(filter_options.options).length > 0) {
                // check value of the operator field and if it's IN or NOT IN then show the multiple select 2 select
                let allow_multiple = value === Base.Operators.IN || value === Base.Operators.NOT_IN;
                let value_wrapper = $(filter_value_select_tpl({
                    multiple: allow_multiple
                }));
                let value_select_input = value_wrapper.fxFind('value');

                if (allow_multiple) {
                    value_select_input.select2();
                }

                let blank_option = $('<option/>')
                    .attr('value', '')
                    .text('-- Select One --');
                value_select_input.append(blank_option);

                // Loop through value options and add to dropdown
                for (let value in filter_options.options) {
                    let this_value = filter_options.options[value],
                        selected = false;

                    // if existing filter value is set then we need to check it against the current looped value and
                    // select if it matches
                    let is_regex = /^[a-z,0-9,-]{36,36}$/;

                    if (existing_filter_value !== null && existing_filter_name === this_row_filter_name) {
                        // if existing filter value is an array then we need to turn the looped value into a string and see if it's in the array
                        if (Array.isArray(existing_filter_value)) {
                            let testing_value = Number.isInteger(parseInt(this_value.value)) ? this_value.value.toString() : this_value.value;
                            selected = $.inArray(testing_value, existing_filter_value) > -1;
                        // if existing filter value is an integer we need to parse it as an integer and check if value matches, also need to verify it doesn't include - which could be a UUID
                        } else if (!is_regex.test(existing_filter_value) && Number.isInteger(parseInt(existing_filter_value))) {
                            selected = parseInt(existing_filter_value) === this_value.value;
                        // if existing filter value is a string we need to check if value matches
                        } else {
                            selected = existing_filter_value === this_value.value;
                        }
                    }
                    let value_option = $('<option/>')
                        .attr('value', this_value.value)
                        .text(this_value.label)
                        .prop('selected', selected);
                    value_select_input.append(value_option);
                }
                value_field.append(value_wrapper);

                value_wrapper.on('change.fx', function (e) {
                    let value = $(e.target).val();
                    if (Array.isArray(value)) {
                        if (value.length > 0) {
                            that.applyFilterMenu();
                        }
                    } else if (value !== '') {
                        that.applyFilterMenu();
                    }
                });

                if (value_select_input.val() !== '') {
                    value_select_input.trigger('change');
                }

            // if operator is a between or not between then use the range date picker
            } else if (value === Base.Operators.BETWEEN || value === Base.Operators.NOT_BETWEEN) {
                let value_wrapper = $(filter_value_date_range_tpl()),
                    value_input = value_wrapper.fxFind('value');

                let pickr_config = {
                    mode: "range",
                    dateFormat: "m/d/Y",
                    use_default_date: false,
                    altFormat: 'm/d/Y',
                    onChange: (selected_dates) => {
                        if (selected_dates.length === 2) {
                            let start = moment(selected_dates[0]).format('YYYY-MM-DD'),
                                end = moment(selected_dates[1]).format('YYYY-MM-DD');
                            value_input.data('value', start + ',' + end);
                        }
                    }
                };

                if (existing_filter_value !== null && Array.isArray(existing_filter_value) && existing_filter_name === this_row_filter_name) {
                    let start = moment(existing_filter_value[0]).utc().format('MM/DD/YYYY'),
                        end = moment(existing_filter_value[1]).utc().format('MM/DD/YYYY');
                    pickr_config['defaultDate'] = [start, end];
                }

                let value_picker = FormInput.init(value_input, {
                    pickr_config
                });

                value_field.append(value_wrapper);
                value_wrapper.on('change.fx', function (e) {
                    if ($(e.target).val() !== '') {
                        that.applyFilterMenu();
                    }
                });

            // otherwise we know it's a date operator and use the normal date picker
            } else if (filter_options.type === Base.FilterValueTypes.DATE) {
                let value_wrapper = $(filter_value_date_tpl()),
                    value_input = value_wrapper.fxFind('value');

                let pickr_config = {
                    use_default_date: false
                }

                if (existing_filter_value !== null && existing_filter_name === this_row_filter_name) {
                    let formatted_date = moment(existing_filter_value).utc().format('YYYY-MM-DD');
                    pickr_config['use_default_date'] = true;
                    pickr_config['defaultDate'] = formatted_date;

                    value_input.val(existing_filter_value);
                }

                let value_picker = FormInput.init(value_input, {
                    pickr_config
                });
                value_field.append(value_wrapper);

                value_wrapper.on('change.fx', function (e) {
                    if ($(e.target).val() !== '') {
                        that.applyFilterMenu();
                    }
                });

                if (value_input.val() !== '') {
                    value_input.trigger('change');
                }

            } else if (filter_options.type === Base.FilterValueTypes.INTEGER) {
                let value_wrapper = $(filter_value_input_tpl()),
                    value_input = value_wrapper.fxFind('value');
                    Inputmask({
                        regex: "\\d*"
                    }).mask(value_input);

                if (existing_filter_value !== null && existing_filter_name === this_row_filter_name) {
                    value_input.val(existing_filter_value);
                }
                value_field.append(value_wrapper);

                value_wrapper.on('change.fx', function (e) {
                    if ($(e.target).val() !== '') {
                        that.applyFilterMenu();
                    }
                });

                if (value_input.val() !== '') {
                    value_input.trigger('change');
                }
            }
        });

        delete_row.on('click.fx', function (e) {
            e.stopPropagation();
            e.preventDefault();
            let this_elem = $(e.target),
                index = this_elem.data('index') === undefined ? this_elem.parent().data('index') : this_elem.data('index'),
                this_row = that.state.filter_menu.rows[index],
                last_key = parseInt(Object.keys(that.state.filter_menu.rows)[Object.keys(that.state.filter_menu.rows).length-1]);

            if (index === last_key) {
                return;
            }

            that.enableFilterName(this_row.inputs.name_input.val());
            this_row.elem.remove();
            delete that.state.filter_menu.rows[index];
            that.applyFilterMenu();
        });

        let row = {
            elem: row_elem,
            inputs: {
                name_input,
                operator_input,
                value_field
            },
            buttons: {
                delete_row
            }
        };

        this.state.filter_menu.body.append(row.elem);
        this.state.filter_menu.rows[row_index] = row;

        if (existing_filter_name !== null) {
            name_input.trigger('change');
        }
    };

    /**
     * Add filter name to the filter name options used array and loop through rows to disable this item
     *
     * @param {string} filter_name
     */
    disableFilterName(filter_name) {
        // add new filter name to the array of used filters
        this.state.filter_name_options_used.push(filter_name);

        // loop through rows and disable the one passed in and isn't the currently selected one
        for (let row in this.state.filter_menu.rows) {
            let name_input = this.state.filter_menu.rows[row].inputs.name_input,
                option = name_input.find(`[value="${filter_name}"]`);
            if (name_input.val() === filter_name) {
                continue;
            }
            option.prop('disabled', true);
        }
    };

    /**
     * Remove filter name to the filter name options used array and loop through rows to enable this item
     *
     * @param {string} filter_name
     */
    enableFilterName(filter_name) {
        // remove filter name from the used filter array
        let index = this.state.filter_name_options_used.indexOf(filter_name);
        // only remove from array when item is found
        if (index > -1) {
            this.state.filter_name_options_used.splice(index, 1);
        }

        // loop through rows and enable the one passed in
        for (let row in this.state.filter_menu.rows) {
            let name_input = this.state.filter_menu.rows[row].inputs.name_input,
                option = name_input.find(`[value="${filter_name}"]`);
            option.prop('disabled', false);
        }
    };

    /**
     * Apply filter menu
     *
     * Loop through filter rows and run draw on the table with the new filters set on the state
     */
    applyFilterMenu() {
        // this prevents the apply filter from running while the filter menu is opening
        if (!this.state.filter_menu_open) {
            return;
        }
        let filter_menu = this.state.filter_menu;
        let filters = {};
        for (let row in filter_menu.rows) {
            let this_row = filter_menu.rows[row];

            if (this_row.inputs.name_input.val() !== '') {
                let filter_name,
                    filter_operator,
                    filter_value = '',
                    value_input = this_row.inputs.value_field.fxFind('value');

                filter_name = this_row.inputs.name_input.val();
                let stored_filter_config = this.state.filter_options[filter_name];

                if (this_row.inputs.operator_input.val() !== '') {
                    filter_operator = this_row.inputs.operator_input.val();
                }
                if (Array.isArray(value_input.val())) {
                    filter_value = value_input.val().length > 0 ? value_input.val() : '';
                } else if (value_input.val() !== '') {
                    filter_value = value_input.val();
                }
                // if filter operator is between then we can assume it's a date
                if (filter_operator === Base.Operators.BETWEEN && value_input.data('value') !== undefined) {
                    filter_value = value_input.data('value').split(',');
                    filter_value[0] = moment.tz(filter_value[0], 'YYYY-MM-DD', this.state.timezone).startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
                    filter_value[1] = moment.tz(filter_value[1], 'YYYY-MM-DD', this.state.timezone).endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]')

                // if filter operator is greater than or less than then we can assume it's a date
                } else if (stored_filter_config.type === Base.FilterValueTypes.DATE && (filter_operator === Base.Operators.GREATER_THAN || filter_operator === Base.Operators.LESS_THAN) && value_input.val() !== '') {
                    filter_value =  moment.tz(value_input.val(), 'YYYY-MM-DD', this.state.timezone).startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
                } else {
                    filter_value = value_input.val();
                }

                if (filter_operator === Base.Operators.NULL || filter_operator === Base.Operators.NOT_NULL) {
                    filters[filter_name] = [filter_operator];
                } else if (filter_name !== undefined && filter_operator !== undefined && (filter_value !== '')) {
                    filters[filter_name] = [filter_operator, filter_value];
                }
            }
        }
        this.state.table_scope.filters = filters;
        this.draw();
    };

    /**
     * Return or set state filter menu
     *
     * @returns {Object} {row_menu}
     */
    getFilterMenu() {
        if (this.state.filter_menu === null) {
            let result_text = this.state.total_results !== null ? (this.state.total_results > 1 ? 'Results' : 'Result') : '';
            let total_results = this.state.total_results !== null && size(this.state.table_scope.filters) > 0 ? `${this.state.total_results.toLocaleString()} ${result_text}` : '';
            let filter_menu = {
                elem: $(filter_menu_tpl({
                    name: this.state.header.filter_name,
                    total_results
                })),
                filters: this.state.filter_options,
                rows: {}
            };
            filter_menu.body = filter_menu.elem.fxFind('filter-body');
            filter_menu.clear = filter_menu.elem.fxFind('clear');
            filter_menu.close = filter_menu.elem.fxFind('close');
            filter_menu.other_filters_container = filter_menu.elem.fxFind('other-filters-container');
            filter_menu.other_filters = filter_menu.elem.fxFind('other-filters');
            filter_menu.total_results = filter_menu.elem.fxFind('total-results');

            if (size(this.state.table_scope.filters) > 0) {
                filter_menu.total_results.show();
            } else {
                filter_menu.total_results.hide();
            }

            filter_menu.clear.fxClick((e) => {
                e.stopPropagation();
                e.preventDefault();
                // only clear current filters if there are filters set
                if (size(this.state.table_scope.filters) > 0) {
                    this.state.table_scope.filters = {};
                }
                this.draw(true);
                this.hideFilterMenu();
            });

            filter_menu.close.fxClick((e) => {
                this.hideFilterMenu();
            });

            $('body').append(filter_menu.elem);
            this.state.filter_menu = filter_menu;
        }
        return this.state.filter_menu;
    };

    /**
     * Use popper to show filter menu
     *
     * @returns {Object} element - row menu element to bind menu
     * @returns {Object} data
     */
    showFilterMenu() {
        let filter_menu = this.getFilterMenu(),
            other_filters = '';

        // loop through existing filters in the scope to add them to the rows, otherwise add a new row
        if (size(this.state.table_scope.filters) > 0) {
            for (let item in this.state.table_scope.filters) {
                // separate any filter passed into the scope that hasn't been defined
                if (this.state.filter_options[item] === undefined) {
                    let key = item.replace('_', ' '),
                        value = this.state.table_scope.filters[item];
                    other_filters = `${other_filters}<div>${key}: ${value}</div>`
                    continue;
                }
                this.addFilterMenuRow(item, this.state.table_scope.filters[item]);
            }
            this.addFilterMenuRow();
        } else {
            this.addFilterMenuRow();
        }

        if (other_filters !== '') {
            filter_menu.other_filters.html(other_filters);
            filter_menu.other_filters_container.addClass('t-show');
        }

        filter_menu.elem.show();
        this.elem.open_filter.addClass('t-open');

        this.state.filter_menu_popper = new Popper(this.elem.search_container[0], filter_menu.elem[0], {
            placement: 'bottom-start'
        });
        this.state.filter_menu_open = true;
    };

    /**
     * Hide filter menu and destroy popper instance
     */
    hideFilterMenu() {
        if (!this.state.filter_menu_open) {
            return;
        }
        this.state.filter_name_options_used = [];
        this.state.filter_menu.body.empty();
        this.state.filter_menu.rows = {};
        this.elem.open_filter.removeClass('t-open');
        this.state.filter_menu.elem.hide();
        this.state.filter_menu_popper.destroy();
        this.state.filter_menu_popper = null;
        this.state.filter_menu_open = false;
        this.state.filter_menu.other_filters.empty();
        this.state.filter_menu.other_filters_container.removeClass('t-show');
    };

    /**
     * Toggle filter menu using state filter menu open status
     */
    toggleFilterMenu() {
        if (this.state.filter_menu_open) {
            this.hideFilterMenu();
        } else {
            this.showFilterMenu();
        }
    };

    /**
     * Fire table settings changed event
     */
    fireTableSettingsChanged() {
        this.fire('table_settings_changed', this.buildTableSettingsConfig());
    };

    /**
     * Build table settings config to push when the table settings changed event is fired
     *
     * This data pushes to the cookie to save the users settings
     *
     * @returns {{order: [], visibility: *[]}}
     */
    buildTableSettingsConfig () {
        let column_visibility = [];
        for (let item in this.state.columns) {
            let column = this.state.columns[item],
                visible = column.visible !== undefined ? column.visible : true;
            column_visibility.push(`${item}:${visible}`);
        }
        return {
            order: this.state.column_order,
            visibility: column_visibility
        };
    };

    getVisibleColumnsCount() {
        let visible_columns = [];

        for (let item in this.state.columns) {
            let column = this.state.columns[item],
                visible = column.visible !== undefined ? column.visible : true;
            if (!visible) {
                continue;
            }
            visible_columns.push(item);
        }
        return visible_columns.length;
    };

    /**
     * Build row and push to the table settings menu
     *
     * @param {object} data
     */
    addTableSettingsMenuRow(data) {
        let row = {
            elem: $(table_settings_menu_row_tpl({
                id: this.state.column_order.indexOf(data.key),
                key: data.key,
                label: data.label,
                visible: data.visible === undefined ? true : data.visible,
                disabled: this.state.checkbox
            }))
        };

        row.column_off = row.elem.fxFind('column-off');
        row.column_on = row.elem.fxFind('column-on');

        row.column_off.fxClick((e) => {
            e.stopPropagation();
            e.preventDefault();

            if (this.getVisibleColumnsCount() === 1) {
                return;
            }

            row.elem.addClass('t-column-off');
            // find the table column to turn it off from the table api
            let column = this.state.table_api.column(row.elem.attr('data-id'));
            column.visible(false);

            // update the stored column config with new setting
            this.state.columns[row.elem.data('key')].visible = false;

            this.fireTableSettingsChanged();
        });

        row.column_on.fxClick((e) => {
            e.stopPropagation();
            e.preventDefault();
            row.elem.removeClass('t-column-off');

            // find the table column to turn it off from the table api
            let column = this.state.table_api.column(row.elem.attr('data-id'));
            column.visible(true);

            // update the stored column config with new setting
            this.state.columns[row.elem.data('key')].visible = true;

            this.fireTableSettingsChanged();
        });
        this.state.table_settings_menu.body.append(row.elem);
    };

    /**
     * Loop through the settings rows to build the new column order
     *
     * Push the order to the table api and save with the table settings
     */
    handleTableSettingsMenuReorder() {
        let items = this.state.table_settings_menu.body.fxFind('row-item'),
            column_index_order = [];

        // loop through the row items and add the column order to the array
        for (let item of items) {
            let $item = $(item);
            column_index_order.push($item.attr('data-id'));
        }
        // If the table has row actions we have to add that as an additional item in the order of columns
        if (this.state.row_actions !== null) {
            column_index_order.push(`${column_index_order.length}`);
        }

        // push the new column order to the table api
        this.state.table_api.colReorder.order(column_index_order);

        // loop through the items again and update them with their new index order
        let new_idx = 0;
        let column_key_order = [];
        for (let item of items) {
            let $item = $(item),
                index = new_idx++;
            $item.attr('data-id', index);
            column_key_order.push($item.data('key'));
        }

        // store the column order to the column order array for the cookie setting
        this.state.column_order = column_key_order;
        this.fireTableSettingsChanged();
    };

    resetTableSettingsMenu() {
        let items = this.state.table_settings_menu.body.fxFind('row-item'),
            column_index_order = [];

        // loop through the row items and reset column visibility back to original state
        for (let item of items) {
            let $item = $(item),
                index = $item.attr('data-id'),
                key = $item.attr('data-key'),
                original_visiblility = this.state.columns[key].original_visiblility;

            this.state.columns[key].visible = original_visiblility;

            let column = this.state.table_api.column(index);
            column.visible(original_visiblility);
        }

        // loop through the original column keys and determine where it exists in the current array to set the
        // order back to default state
        for (let item of this.state.original_column_keys) {
            column_index_order.push(this.state.column_order.indexOf(item));
        }

        // If the table has row actions we have to add that as an additional item in the order of columns
        if (this.state.row_actions !== null) {
            column_index_order.push(column_index_order.length);
        }

        // push the new column order to the table api
        this.state.table_api.colReorder.order(column_index_order);

        // store the column order to the column order array for the cookie setting
        this.state.column_order = this.state.original_column_keys;

        this.fireTableSettingsChanged();

        this.hideTableSettingsMenu();
    };

    /**
     * Return or set state table settings menu
     *
     * @returns {Object} {row_menu}
     */
    getTableSettingsMenu() {
        if (this.state.table_settings_menu === null) {
            let table_settings_menu = {
                elem: $(table_settings_menu_tpl({
                    table_options: this.state.table_settings_map_view || this.state.table_settings_bulk_actions,
                    map_view: this.state.table_settings_map_view,
                    map_view_checked: this.state.table_settings_map_view_status,
                    bulk_actions: this.state.table_settings_bulk_actions,
                    bulk_actions_checked: this.state.table_settings_bulk_actions_status
                }))
            };
            Tooltip.initAll(table_settings_menu.elem);

            table_settings_menu.body = table_settings_menu.elem.fxFind('table-settings-body');
            table_settings_menu.reset = table_settings_menu.elem.fxFind('reset');
            table_settings_menu.close = table_settings_menu.elem.fxFind('close');

            // if table settings map view is enabled then find the element and setup the toggle
            if (this.state.table_settings_map_view) {
                table_settings_menu.map_view = table_settings_menu.elem.fxFind('map-view');
                FormInput.init(table_settings_menu.map_view);

                table_settings_menu.map_view.on('change.fx', (e) => {
                    this.hideTableSettingsMenu();
                    this.fire('map_view_toggle',$(e.target).is(':checked'));
                });
            }
            // if table settings bulk actions is enabled then find the element and setup the toggle
            if (this.state.table_settings_bulk_actions) {
                table_settings_menu.bulk_actions = table_settings_menu.elem.fxFind('bulk-actions');
                FormInput.init(table_settings_menu.bulk_actions);

                table_settings_menu.bulk_actions.on('change.fx', (e) => {
                    this.hideTableSettingsMenu();
                    this.fire('bulk_actions_toggle', $(e.target).is(':checked'));
                });
            }

            // Start sortable instance for the settings menu
            Sortable.create(table_settings_menu.body[0], {
                handle: '.c-tsmbii-move-icon', // can't use data attribute since sortable doesn't support them
                animation: 150,
                ghostClass: 't-ghosted',
                filter: '.t-disabled',
                onSort: () => {
                    this.handleTableSettingsMenuReorder();
                }
            });

            table_settings_menu.reset.fxClick((e) => {
                e.stopPropagation();
                e.preventDefault();
                this.resetTableSettingsMenu();
            });

            table_settings_menu.close.fxClick((e) => {
                this.hideTableSettingsMenu();
            });

            $('body').append(table_settings_menu.elem);
            this.state.table_settings_menu = table_settings_menu;
        }
        return this.state.table_settings_menu;
    };


    /**
     * Use popper to show table settings menu
     *
     * @returns {Object} element - row menu element to bind menu
     * @returns {Object} data
     */
    showTableSettingsMenu() {
        let table_settings_menu = this.getTableSettingsMenu();

       // loop through the column order config and add the row elements to the settings menu
        for (let item of this.state.column_order) {
            let this_item = this.state.columns[item];
            // if it's a checkbox column or row action column we need to skip these for table ordering
            if (this_item === undefined || (this_item.is_checkbox !== undefined && this_item.is_checkbox) || (this_item.is_row_action !== undefined && this_item.is_row_action)) {
                continue;
            }
            if (this.state.columns[item] !== undefined) {
                this.addTableSettingsMenuRow(this.state.columns[item]);
            }
        }

        table_settings_menu.elem.show();
        this.elem.table_settings.addClass('t-open');

        this.state.table_settings_menu_popper = new Popper(this.elem.table_settings[0], table_settings_menu.elem[0], {
            placement: 'bottom-end'
        });
        this.state.table_settings_menu_open = true;
    };

    /**
     * Hide table settings menu and destroy popper instance
     */
    hideTableSettingsMenu() {
        if (!this.state.table_settings_menu_open) {
            return;
        }
        this.state.table_settings_menu.body.empty();
        this.elem.table_settings.removeClass('t-open');
        this.state.table_settings_menu.elem.hide();
        this.state.table_settings_menu_popper.destroy();
        this.state.table_settings_menu_popper = null;
        this.state.table_settings_menu_open = false;
    };

    /**
     * Toggle table settings menu using state filter menu open status
     */
    toggleTableSettingsMenu() {
        if (this.state.table_settings_menu_open) {
            this.hideTableSettingsMenu();
        } else {
            this.showTableSettingsMenu();
        }
    };

    /**
     * Return or set state header menu
     *
     * @returns {Object} {header_menu}
     */
    getHeaderMenu() {
        if (this.state.header_menu === null) {
            let header_menu = {
                elem: $(header_menu_tpl()),
                actions: {}
            };
            for (let action in this.state.buttons) {
                let header_action = this.state.buttons[action];
                if (typeof header_action.visible === 'boolean' && !header_action.visible) {
                    continue;
                }
                header_action.elem = $(header_menu_action_tpl({
                    action,
                    label: header_action.label,
                    disabled: header_action.disabled
                }));
                header_menu.elem.append(header_action.elem);
                header_menu.actions[action] = header_action;
            }
            $('body').append(header_menu.elem);
            this.state.header_menu = header_menu;

            let that = this;
            this.state.header_menu.elem.fxEventWatcher(['click', 'touchstart'], 'action', function(e) {
                e.preventDefault();
                e.stopPropagation();

                let $this = $(this);
                if ($this.hasClass('t-disabled')) {
                    return;
                }
                that.state.header_menu.actions[$this.data('action')].action(e);
                that.hideHeaderMenu();
                return false;
            });
        }
        return this.state.header_menu;
    };

    /**
     * Use popper to show header menu
     */
    showHeaderMenu() {
        let header_menu = this.getHeaderMenu();

        header_menu.elem.show();
        this.state.header_menu_popper = new Popper(this.elem.button_action[0], header_menu.elem[0], {
            placement: 'left-start'
        });
        this.state.header_menu_open = true;
    };

    /**
     * Hide header menu and destroy popper instance
     */
    hideHeaderMenu() {
        this.state.header_menu.elem.hide();
        this.state.header_menu_popper.destroy();
        this.state.header_menu_popper = null;
        this.state.header_menu_open = false;
    };

    /**
     * Toggle header menu using state header menu open status
     */
    toggleHeaderMenu() {
        if (this.state.header_menu_open) {
            this.hideHeaderMenu();
        } else {
            this.showHeaderMenu();
        }
    };

    /**
     * Return state buttons - includes the button dom element
     *
     * @returns {Object}
     */
    getButtons() {
        return this.state.buttons;
    };

    /**
     * Hide header search in mobile version
     */
    hideHeaderSearch() {
        this.elem.search_container.removeClass('search-open');
        this.elem.header_wrapper.removeClass('search-open');
        this.elem.root.removeClass('search-open');
        if (this.state.paging_enabled) {
            this.elem.pagination.removeClass('search-open');
        }

        this.state.header_search_open = false;
    };

    /**
     * Show header search in mobile version
     */
    showHeaderSearch() {
        this.elem.header_wrapper.addClass('search-open');
        setTimeout(() => {
            this.elem.search_container.addClass('search-open');
        }, 200);
        this.elem.root.addClass('search-open');

        this.state.header_search_open = true;
    };

    /**
     * Toggle header search using state search bar open status
     */
    toggleSearchBar() {
        if (this.state.header_search_open) {
            this.hideHeaderSearch();
        } else {
            this.showHeaderSearch();
        }
    };

    /**
     * Start search timer to auto search
     */
    startSearchTimer() {
        this.clearSearchTimer();
        this.state.search_timer = setTimeout(() => {
            this.startSearch();
        }, 1000);
    };

    /**
     * Clear search timer if user continues to type
     */
    clearSearchTimer() {
        if (this.state.search_timer === null) {
            return;
        }
        clearTimeout(this.state.search_timer);
        this.state.search_timer = null;
    };

    /**
     * Initiate the search and handle using state server search setting
     *
     * Includes a searching function to change search capabilities on the front end
     */
    startSearch() {
        let search_term = this.elem.search_bar.val().trim();
        if (search_term.length === 0) {
            return;
        }

        let config = {
            term: search_term,
            fetch_data: false,
            row_search: false
        };

        if (typeof this.state.config.searching === 'function') {
            config = this.state.config.searching(config, this);
        }
        this.state.table_scope.search = config.term;

        if (this.state.server_search) {
            this.draw();
            // this.state.table_api.destroy();
            // this.elem.root.find('.c-t-table-wrapper').hide();
            // // only if search results do not contain paginate
            // if (!this.state.server_paginate) {
            //     this.state.server_side = false;
            // }
            // this.buildTable().then(() => {
            //     if (this.state.data.type === Base.DataTypes.AJAX) {
            //         return;
            //     }
            //     this.fetchData();
            // });
            // this.elem.root.find('.c-t-table-wrapper').show();
        } else {
            if (config.fetch_data) {
                this.fetchData().then(() => {
                    if (config.row_search) {
                        this.state.table_api.search(config.term).draw();
                    }
                });
            } else if (config.row_search) {
                this.state.table_api.search(config.term).draw();
            }
        }

        if (this.state.mobile_size) {
            this.elem.search_bar.blur();
        }
    };

    /**
     * Build request for Api Module
     *
     * @param {module:Api.Request} request
     * @param {Scope} scope
     * @returns {module:Api.Request}
     */
    buildRequest(request) {
        let scope = this.state.table_scope;
        if (scope.search !== null) {
            request.search(scope.search);
        }
        if (scope.sorts !== null) {
            request.sorts(scope.sorts);
        }
        if (scope.filters !== null) {
            let filters = {};
            for (let filter in scope.filters) {
                let this_filter = scope.filters[filter];
                let filter_set = null;
                if (lang.isArray(this_filter)) {
                    filter_set = Api.Filter.make(...this_filter);
                } else {
                    filter_set = this_filter;
                }
                if (filter_set) {
                    filters[filter] = filter_set;
                }
            }
            if (size(filters) > 0) {
                request.filters(filters);
                this.elem.open_filter.addClass('t-show-badge');
                this.elem.open_filter.attr('data-content',size(filters));
            } else {
                this.elem.open_filter.removeClass('t-show-badge');
                this.elem.open_filter.attr('data-content',0);
            }
        }
        if (scope.pagination) {
            request.perPage(scope.pagination.per_page).page(scope.pagination.page);
        }
        return request;
    };

    /**
     * Set default sort order using table scope sorts
     *
     * @returns {Array} - passed into table initialization
     */
    setDefaultOrder() {
        // set sorts
        let default_sorts = [];

        for (let sort in this.state.table_scope.sorts) {
            let this_sort = this.state.table_scope.sorts[sort];
            let direction = null;
            if (SortTypes[this_sort] !== undefined) {
                direction = SortTypes[this_sort];
            }

            let key_sort = null;
            for (let key in this.state.column_keys) {
                let column = this.state.column_keys[key];

                if (column === sort) {
                    key_sort = key;
                    break;
                }
            }
            if (key_sort !== null && direction !== null) {
                default_sorts.push([key_sort, direction]);
            }
        }
        return default_sorts;
    };

    /**
     * Update table scope order with the DataTables set order
     */
    changeScopeOrder() {
        if (this.state.table_api === null) {
            return;
        }
        let column_order = this.state.table_api.order();
        let order = {};
        if (column_order.length > 0) {
            for (let item of column_order) {
                order[this.state.column_keys[parseInt(item[0])]] = item[1];
            }
        }
        this.state.table_scope.sorts = order;
    };

    /**
     * Configure columns for DataTables
     *
     * @returns {Array} - columns with specific options
     */
    configureColumns() {
        let columns_options = [];
        let columns = {};
        if (this.state.checkbox) {
            Object.assign(columns, {
                checkbox: {
                    is_checkbox: true,
                    orderable: false,
                    searchable: false,
                    align: Base.Align.CENTER,
                    value: (data, type, full, meta) => {
                        return checkbox_tpl({
                            id: data.id
                        });
                    }
                }
            });
        }
        Object.assign(columns, this.state.columns);
        if (this.state.row_actions !== null) {
            Object.assign(columns, {
                row_action: {
                    is_row_action: true,
                    label: '',
                    orderable: false,
                    width: '1%',
                    value: () => {
                        return row_action_tpl();
                    }
                }
            });
        }
        if (this.state.row_edit) {
            Object.assign(columns, {
                row_action: {
                    label: '',
                    orderable: false,
                    width: '1%',
                    value: () => {
                        return row_edit_tpl();
                    }
                }
            });
        }
        if (this.state.checkbox) {
            // reset columns
            this.state.columns = columns;
            this.state.column_keys = Object.keys(columns);
        }
        let index = 0;
        let column_order = [];
        for (let column in columns) {
            let this_column = columns[column];

            columns[column]['index'] = index++;
            let column_opt = {
                'defaultContent': '--'
            };

            if (lang.isUndefined(this_column.key)) {
                this_column['key'] = column;
                column_opt['name'] = column;
            }

            // Orderable
            if (lang.isBoolean(this_column.orderable)) {
                column_opt['orderable'] = this_column.orderable;
            }

            //Alignment
            if (!lang.isUndefined(this_column.align) && includes(Base.Align, this_column.align)) {
                column_opt['className'] = AlignTypes[this_column.align];
            }

            //Width
            if (!lang.isUndefined(this_column.width)) {
                column_opt['width'] = this_column.width;
            }

            // Class Name
            if (!lang.isUndefined(this_column.class_name) && lang.isUndefined(column_opt['className'])) {
                column_opt['className'] = this_column.class_name;
            }

            // Set Data or Render for Custom Values
            column_opt['data'] = this_column.key;
            if (!lang.isUndefined(this_column.value)) {
                column_opt['data'] = null;
                column_opt['render'] = (data, type) => {
                    return this_column.value(data, type);
                };
            }

            // Visible
            if (lang.isBoolean(this_column.visible)) {
                column_opt['visible'] = this_column.visible;
            }

            column_order.push(this_column['key']);

            // Push Settings to Columns Options
            columns_options.push(column_opt);
        }
        if (this.state.column_order.length === 0) {
            this.state.column_order = column_order;
        }
        return columns_options;
    };

    /**
     * Configure Ajax for DataTables
     *
     * @param data
     * @param callback - for DataTables to load the data
     * @param settings
     */
    async configureAjax(data, callback, settings) {
        let request = this.state.data.source();
        if (lang.isFunction(this.state.data.callback)) {
            this.state.data.callback(request);
        }
        request = this.buildRequest(request);

        try {
            let data = await request.all();
            if (isPlainObject(data)) {
                data = data.data;
            }
            let result = data.entities;
            let pagination = data.response.meta('pagination');
            if (!lang.isNull(pagination)) {
                this.total_results = pagination.total_results;
                this.records_filtered = pagination.total_results;
            } else {
                this.total_results = result.length;
                this.records_filtered = this.state.table_api.page.len();
            }
            // setting this to the state so that we can pull it when the filter opens
            this.state.total_results = this.total_results;
            if (this.state.filter_menu !== null) {
                this.state.filter_menu.total_results.hide();

                let result_text = this.total_results !== null ? (this.total_results > 1 ? 'Results' : 'Result') : '';
                let total_results = size(this.state.table_scope.filters) > 0 ? `${this.total_results.toLocaleString()} ${result_text}` : '';
                this.state.filter_menu.total_results.text(total_results);
                if (size(this.state.table_scope.filters) > 0 ) {
                    this.state.filter_menu.total_results.show();
                }
            }

            let object = {
                'data': [],
                'recordsTotal': this.total_results,
                'recordsFiltered': this.records_filtered
            };
            object['data'] = result.map(entity => {
                return entity.data;
            });
            callback(object);
        } catch (error) {
            throw new Error('Data could not be retrieved.' + error.code + ' - ' + error.message);
        }
    };

    /**
     * Build table config that is passed into the table initialization
     *
     * @returns {Object}
     */
    buildConfig() {
        if (this.state.table_scope.search !== null && !this.state.server_paginate) {
            this.state.server_side = false;
        }

        let config = {
            serverSide: this.state.server_side,
            pagingType: this.state.paging_type,
            columns: this.configureColumns(),
            colReorder: true,
            drawCallback: (settings) => {
                this.fire('table_drawn', settings);
            },
            rowCallback: (row, data) => {
                this.fire('row_drawn', row, data);
            },
            rowId: (data) => {
                if (data.id === undefined) {
                    return undefined;
                }
                return `fx-${this.state.idx}-${data.id}`;
            },
            order: this.setDefaultOrder(),
            paging: this.state.paging_enabled,
            info: this.state.paging_enabled,
            processing: true,
            language: {
                processing: '',
                search: "",
                searchPlaceholder: "Search",
                lengthMenu: "_MENU_",
                emptyTable: this.state.lang.no_rows,
                info: "_START_ - _END_ of _TOTAL_",
                infoEmpty: "0 - 0 of 0",
                infoFiltered: "",
                paginate: {
                    previous: '',
                    next: ''
                }
            },
            select: {
                style:    'multi',
                selector: 'td:nth-child(1)'
            },
            responsive: this.state.responsive
        };
        if (this.state.page_length_all) {
            config.lengthMenu = [[...this.state.page_lengths, -1], [...this.state.page_lengths, 'All']];
        } else {
            config.lengthMenu = this.state.page_lengths;
        }

        if (this.state.scrolling_enabled) {
            config.scrollY = this.state.height;
            config.scrollX = true;
            config.scrollCollapse = true;
        }

        let dom = '<"c-t-processing"><"c-t-table-wrapper"t>';
        if (this.state.paging_enabled) {
            config.pageLength = this.state.table_scope.pagination.per_page;
            config.displayStart = Math.ceil((this.state.table_scope.pagination.page - 1) * this.state.table_scope.pagination.per_page);
            dom += '<"c-t-footer"l<"c-tf-pagination"p><"c-tf-info"i>>';
        }

        config.dom = dom;

        let fixed_config = {};
        if (this.state.config.fixed_columns && !this.state.mobile_size) {
            fixed_config['leftColumns'] = this.state.config.fixed_columns;
            this.state.fixed_left_columns = true;
        }
        if (this.state.row_actions) {
            fixed_config['leftColumns'] = fixed_config['leftColumns'] ? fixed_config['leftColumns'] : 0;
            fixed_config['rightColumns'] = 1;
        }
        if (!lang.isEmpty(fixed_config)) {
            config['fixedColumns'] = fixed_config;
            this.state.has_fixed_columns = true;
        }

        if (this.state.checkbox) {
            config['createdRow'] = (row, data, dataIndex) => {
                let this_row = $(row);
                this.elem.checkbox = this_row.fxFind('row-check');
                this.state.checkboxes[`row_${this_row.attr('id')}`] = FormInput.init(this.elem.checkbox);
            };
        }

        if (this.state.data.type === Base.DataTypes.AJAX) {
            config['ajax'] = (...args) => {
                this.configureAjax(...args);
            }
        } else if (this.state.data.type === Base.DataTypes.DATA) {
            config['page'] = this.state.table_scope.pagination.page;
        }
        return config;
    };

    /**
     * Set search term on table
     *
     * @param {?string} term
     * @param {boolean} [draw=true] - determines if table is redrawn
     */
    setSearch(term, draw = true) {
        this.state.table_api.search(term);
        if (draw) {
            this.draw();
        }
    };

    /**
     * Show or hide processing element
     *
     * @param {boolean} status
     */
    toggleProcessing(status) {
        this.elem.processing.toggle(status);
        this.state.processing = status;
    };

    /**
     * Adjust column widths to fit content
     */
    adjustColumns(draw = true) {
        if (this.state.table_api === null) {
            return;
        }
        this.state.table_api.columns.adjust();
        if (draw) {
            this.draw(false);
        }
    };

    handleFooter() {
        this.elem.footer.toggleClass('t-small', this.elem.container.width() < 520);
    };

    /**
     * Render table, setup header, and bind all events as necessary
     */
    bootTable() {
        let unique_class = this.state.class;
        if (this.state.checkbox) {
            unique_class = `${unique_class} t-checkbox`;
        }
        let columns = {};
        if (this.state.checkbox) {
            let checkbox_header = checkbox_header_tpl();
            Object.assign(columns, {
                checkbox: {
                    label: checkbox_header
                }
            });
        }
        let visible_columns = {},
            visible_column_keys = [];
        for (let column in this.state.columns) {
            // if (this.state.columns[column].visible !== undefined && !this.state.columns[column].visible) {
            //     continue;
            // }
            visible_columns[column] = this.state.columns[column];
            visible_column_keys.push(column);
        }
        this.state.columns = visible_columns;
        this.state.column_keys = visible_column_keys;
        Object.assign(columns, this.state.columns);
        if (this.state.row_actions !== null || this.state.row_edit) {
            let table_settings = this.state.use_table_settings ? header_table_settings_button() : '';
            Object.assign(columns, {
                row_action: {
                    label: table_settings
                }
            });
        }
        this.elem.root = $(base_tpl({
            class: unique_class,
            columns: columns
        }));
        if (this.state.load_tooltip) {
            Tooltip.initAll(this.elem.root);
        }
        this.elem.container.append(this.elem.root);

        if (this.state.checkbox) {
            this.elem.check_all = this.elem.root.fxFind('check-all');
            this.state.checkboxes.select_all = FormInput.init(this.elem.check_all);
            this.elem.check_all.on('change.fx', (e) => {
                let rows = this.getNodes();
                let checked = this.state.checkboxes.select_all.checked;
                for (let i = 0; i < rows.length; i++) {
                    let row = $(rows[i]),
                        this_checkbox = this.state.checkboxes[`row_${row.attr('id')}`];
                    if (this_checkbox.disabled) {
                        continue;
                    }
                    this_checkbox.setChecked(checked);
                }
                if (checked) {
                    this.fire('select_all_checked');
                } else {
                    this.fire('select_all_unchecked');
                }
            });
        }

        this.elem.table = this.elem.root.fxFind('table');

        $.extend($.fn.dataTableExt.oStdClasses, {
            'sLength': 'dataTables_length c-tf-view f-field',
            'sPageButton': 'paginate_button c-tfp-page-button'
        });

        this.elem.flashbar = this.elem.root.fxFind('flashbar');

        if (this.state.table_scope.search) {
            this.state.search_icon = this.state.search_icon_types.CLEAR;
        }

        this.state.buttons = {};
        for (let button in this.state.buttons_default) {
            let this_button = this.state.buttons_default[button];
            this_button.key = button;
            this_button.visible = this_button.visible !== undefined ? this_button.visible : true;
            this_button.disabled = this_button.disabled !== undefined ? this_button.disabled : false;
            this_button.type_class = this_button.type_class !== undefined ? this_button.type_class : 't-tertiary';
            this_button.icon = this_button.icon !== undefined && this_button.type_class === 't-tertiary-icon' ? this_button.icon : null;

            if (this_button.visible) {
                this_button.elem = $(button_tpl(this_button));
                this.state.buttons[button] = this_button;
            }
        }

        const that = this;
        if (this.state.header.name !== null || size(this.state.buttons) > 0 || this.state.header.search) {
            let name = this.state.header.name !== null ? this.state.header.name : false;
            // Add header
            this.elem.header = $(header_tpl({
                name,
                search: this.state.header.search,
                search_placeholder: this.state.header.search_placeholder,
                value: this.state.table_scope.search !== null ? this.state.table_scope.search : '',
                icon_class: this.state.search_icon,
                icon: `remix-icon--system--${this.state.search_icon}-line`,
                bulk_actions: this.state.bulk_actions !== null,
                buttons: size(this.state.buttons) > 0,
                filter_enabled: size(this.state.filter_options) > 0
            }));

            this.elem.search_bar = this.elem.header.fxFind('search-bar');
            this.elem.search_icon = this.elem.header.fxFind('icon');
            this.elem.search_bar.on('keydown', (e) => {
                switch (e.keyCode) {
                    case KeyCodes.HOME:
                    case KeyCodes.END:
                    case KeyCodes.PAGE_UP:
                    case KeyCodes.PAGE_DOWN:
                    case KeyCodes.UP:
                    case KeyCodes.DOWN:
                    case KeyCodes.LEFT:
                    case KeyCodes.RIGHT:
                    case KeyCodes.TAB:
                    case KeyCodes.ESCAPE:
                        break;
                    case KeyCodes.ENTER:
                        this.clearSearchTimer();
                        this.startSearch();
                        break;
                    default:
                        if (e.altKey || e.ctrlKey || e.metaKey) {
                            break;
                        }
                        if (this.state.search_icon !== this.state.search_icon_types.CLEAR) {
                            // Set search bar to allow clear
                            this.changeSearchIcon(this.state.search_icon_types.CLEAR);
                        }
                        if (!this.state.mobile_size) {
                            this.startSearchTimer();
                        }
                        break;
                }
            });

            this.elem.header.fxFind('icon-wrapper').on('click.fx', (e) => {
                if (this.state.search_icon === this.state.search_icon_types.SEARCH) {
                    return;
                }
                // Reset search bar
                this.changeSearchIcon(this.state.search_icon_types.SEARCH);
                this.elem.search_bar.val('');
                this.state.table_scope.search = null;

                // if data is sent to the table and search is cleared we clear the search term and fire an event back
                // to the data source
                if (this.state.data.type === Base.DataTypes.DATA) {
                    this.setSearch('');
                    this.fire('search_clear');
                    // else if table uses ajax and server side pagination is not used when search is cleared we destroy the
                    // table so that server side pagination can take back over
                } else if (this.state.data.type === Base.DataTypes.AJAX && this.state.server_side === false) {
                    // Reset Table
                    this.state.table_api.destroy();
                    this.elem.container.find('.c-t-table-wrapper').hide();
                    this.state.server_side = true;
                    this.buildTable().then(() => {
                        if (this.state.data.type === Base.DataTypes.AJAX) {
                            return;
                        }
                        this.fetchData();
                    });
                    this.elem.container.find('.c-t-table-wrapper').show();
                    // else if table uses ajax and server side pagination is used we clear the search and redraw the table
                } else {
                    this.setSearch('');
                    this.state.table_api.search('');
                    this.draw();
                }
            });

            this.elem.bulk_actions_button = this.elem.header.fxFind('bulk-actions');
            this.elem.bulk_actions_button.fxClick(function (e) {
                e.stopPropagation();
                e.preventDefault();
                that.toggleBulkActionsMenu();
            });

            this.elem.button_action = this.elem.header.fxFind('button-action');
            this.elem.button_action.fxClick(function (e) {
                e.stopPropagation();
                e.preventDefault();
                that.toggleHeaderMenu();
            });

            this.elem.open_filter = this.elem.header.fxFind('open-filter');
            this.elem.open_filter.fxClick(function (e) {
                e.stopPropagation();
                e.preventDefault();
                that.toggleFilterMenu();
            });

            this.elem.header.fxFind('search-action').fxClick(function (e) {
                e.stopPropagation();
                e.preventDefault();
                that.toggleSearchBar();
            });

            this.elem.root.fxClickWatcher('table-settings', function(e) {
                e.stopImmediatePropagation();
                e.preventDefault();
                that.elem.table_settings = $(e.currentTarget);
                that.toggleTableSettingsMenu();
            })

            this.elem.header_container = this.elem.container.fxFind('header');
            this.elem.header_container.prepend(this.elem.header);
            this.elem.header_wrapper = this.elem.header_container.fxFind('header-wrapper');
            this.elem.search_container = this.elem.header_wrapper.fxFind('search-wrapper');

            for (let button in this.state.buttons) {
                this.elem.container.fxFind('buttons-wrapper').append(this.state.buttons[button].elem);
            }

            // Bind action to buttons
            this.elem.root.fxFind('buttons-wrapper').on('click.fx', 'button', (e) => {
                let action = this.state.buttons[$(e.currentTarget).data('name')].action;
                if (typeof action === 'function') {
                    action(e);
                }
            });
        }

        // Add toolbar
        if (this.state.toolbar.filter || this.state.toolbar.settings) {
            this.elem.container.fxFind('toolbar-wrapper').append(toolbar_tpl({
                filter: this.state.toolbar.filter,
                settings: this.state.toolbar.settings
            }));
        }

        const getTables = () => {
            if (this.elem.tables === undefined) {
                let selectors = [];
                if (!this.state.has_fixed_columns) {
                    selectors.push('table');
                } else {
                    selectors.push('.dataTables_scrollBody table', '.DTFC_LeftBodyWrapper table', '.DTFC_RightBodyWrapper table');
                }
                this.elem.tables = this.elem.root.find(selectors.join(', '));
            }
            return this.elem.tables;
        };

        // Set hover class on rows when hovering
        this.elem.root.on(`mouseenter.fx.table.${this.state.idx}`, 'tbody tr', function() {
            if (!that.state.table_api.data().any()) {
                return;
            }
            let tr_index = $(this).index() + 1;
            getTables().each(function() {
                $(this).find(`tr:eq(${tr_index})`).addClass('hover');
            });
        }).on(`mouseleave.fx.table.${this.state.idx}`, 'tbody tr', function() {
            let tr_index = $(this).index() + 1;
            getTables().each(function() {
                $(this).find(`tr:eq(${tr_index})`).removeClass('hover');
            });
        });

        $(document).fxEvent(['click'], function (e) {
            if (that.state.row_menu_open) {
                that.hideRowMenu();
            }
            if (that.state.header_menu_open) {
                that.hideHeaderMenu();
            }
            if (that.state.bulk_actions_menu_open) {
                that.hideBulkActionsMenu();
            }
            if (that.state.filter_menu_open && !that.state.processing) {
                // do a hit test to see if user clicks in the filter menu or outside
                // we only close the filter menu if they click outside
                let target = e.target;
                let $target = $(e.target);
                let parent = that.state.filter_menu.elem,
                    is_ignore_item =
                        $target.hasClass('flatpickr-calendar') ||
                        $target.hasClass('flatpickr-weekdaycontainer') ||
                        $target.hasClass('flatpickr-weekday') ||
                        $target.hasClass('flatpickr-months') ||
                        $target.hasClass('flatpickr-innerContainer') ||
                        $target.hasClass('flatpickr-prev-month') ||
                        $target.hasClass('flatpickr-month') ||
                        $target.hasClass('flatpickr-next-month') ||
                        $target.hasClass('cur-month') ||
                        $target.hasClass('cur-year') ||
                        $target.hasClass('arrowUp') ||
                        $target.hasClass('arrowDown') ||
                        $target.hasClass('select2-selection__choice__remove');

                // check if target is the flatpicker or select 2 element that we need to ignore
                if (is_ignore_item) {
                    return;
                }
                if (!parent[0].contains(target)) {
                    that.hideFilterMenu();
                }
            }

            if (that.state.table_settings_menu_open) {
                // do a hit test to see if user clicks in the table settings menu or outside
                // we only close the table settings menu if they click outside
                let target = e.target;
                let parent = that.state.table_settings_menu.elem;
                if (!parent[0].contains(target)) {
                    that.hideTableSettingsMenu();
                }
            }
        }, {namespace: `table.${this.state.idx}`});

        $(window).fxEvent(['resize'], debounce(() => {
            this.adjustColumns();
            this.handleFooter();
        }, 500), {namespace: `table.${this.state.idx}`});

        this.state.booted = true;
    };

    /**
     * Build Datatable with settings
     */
    buildTable() {
        return new Promise((resolve, reject) => {
            if (!this.state.booted) {
                this.bootTable();
            }

            const that = this;
            this.elem.table.on('preInit.dt', (e, settings) => {
                let api = new $.fn.dataTable.Api(settings);
                api.page.len(this.state.table_scope.pagination.per_page);
                this.state.table_api = api;
            }).on('search.dt', () => {
                // search event
            }).on('preXhr.dt', () => {
                this.changeScopeOrder();
            }).on('page.dt', () => {
                this.state.table_scope.pagination.page = this.state.table_api.page.info().page + 1;
            }).on('order.dt', () => {
                if (this.state.data.type === Base.DataTypes.DATA) {
                    this.changeScopeOrder();
                }
                this.adjustColumns(false);
            }).on('length.dt', (e, settings, len) => {
                this.state.table_scope.pagination.per_page = len;
            }).on('preDraw.dt', () => {
                if (this.elem.processing === undefined || this.elem.processing.length === 0) {
                    this.elem.processing = this.elem.root.find('.c-t-processing');
                }
                this.toggleProcessing(true);
            }).on('draw.dt', () => {
                if (this.state.checkbox) {
                    this.elem.check_all.prop('checked', false).trigger('change');
                }
                this.fire('scope_change', this.state.table_scope);
                this.elem.tables = undefined;
                if (!this.state.fixed_left_columns && this.state.scrolling_enabled && this.elem.table_scroller !== undefined) {
                    this.elem.table_scroller.removeClass('scroll');
                }
                if (this.state.row_actions !== null) {
                    if (this.elem.right_body_liner === undefined || this.elem.right_body_liner.length === 0) {
                        this.elem.right_body_liner = this.elem.root.find('.DTFC_RightBodyLiner');
                    }
                    this.elem.right_body_liner.css('overflow-y', 'hidden');
                }
                this.elem.footer = this.elem.container.find('.c-t-footer');
                this.handleFooter();
                this.toggleProcessing(false);
            }).on('init.dt', () => {
                resolve();
            }).on('click.fx', 'tbody tr', function(e) {
                if (!$(e.target).is('a')) {
                    that.fire('row_click', that.getRowData(this));
                }
            }).on('change.fx', 'tbody tr .f-f-input', function() {
                let row = $(this).parents('tr'),
                    checkbox = that.getCheckbox(row.attr('id'));
                if (checkbox === undefined) {
                    return;
                }
                if (checkbox.checked) {
                    that.fire('row_checked', that.getRowData(row));
                } else {
                    that.fire('row_unchecked', that.getRowData(row));
                }
            }).fxClickWatcher('row-action', function(e) {
                e.stopImmediatePropagation();
                e.preventDefault();
                that.toggleRowMenu(this, that.getRowData($(this).parents('tr')[0]));
            }).DataTable(this.buildConfig());

            if (this.state.table_scope.search !== null) {
                this.showHeaderSearch();
            }

            this.elem.table_wrapper = this.elem.root.find('.c-t-table-wrapper');

            if (this.state.row_actions !== null) {
                this.elem.root.addClass('t-has-row-actions');
            }
            if (this.state.row_edit) {
                this.elem.root.addClass('t-has-row-edit');
            }
            if (this.state.scrolling_enabled) {
                this.elem.root.addClass('t-has-scrolling');
                this.elem.table_scroller = this.elem.root.find('.dataTables_scroll');
                this.elem.root.find('.dataTables_scrollBody').on('scroll', function() {
                    if (!that.state.fixed_left_columns && !that.elem.table_scroller.hasClass('scroll')) {
                        that.elem.table_scroller.addClass('scroll');
                    }
                    if (that.state.row_menu_open) {
                        that.hideRowMenu();
                    }
                });
            }

            if (this.state.paging_enabled) {
                this.elem.pagination = this.elem.root.find('.c-tf-pagination');
            }
        });
    };

    /**
     * Set table data using client side data and redraw
     *
     * @param data
     */
    setTableData(data) {
        this.state.table_api.clear();
        this.state.table_api.rows.add(data).draw();
        if ((this.state.table_api.page.info().page + 1) !== this.state.table_scope.pagination.page) {
            this.state.table_api.page(this.state.table_scope.pagination.page - 1).draw(false);
        }
        this.adjustColumns();
    };

    changeColumnName(original_name, new_name) {
        this.state.table_api.columns().header().each((column) => {
            let column_string = $(column).text();
            if (column_string.includes(original_name)) {
                $(column).html(new_name);
            }
        });
    };

    /**
     * Fetch data from client side
     *
     * @returns {(Function|Promise|Object[])}
     */
    fetchData() {
        return new Promise((resolve, reject) => {
            let data = this.state.data.source;
            if (typeof data === 'function') {
                data = data(this.scope, this);
            }
            if (typeof data === 'object' && typeof data.then === 'function') {
                data.then(data => {
                    this.setTableData(data);
                    resolve();
                });
            } else { // data is an array
                this.setTableData(data);
                resolve();
            }
        });
    };

    /**
     * Setup table and fetch data as necessary
     *
     * @returns {Promise<module:Table.Base>}
     */
    async setup() {
        await this.buildTable();
        if (this.state.data.type !== Base.DataTypes.AJAX) {
            await this.fetchData();
        }
        return this;
    };

    /**
     * Build table
     *
     * Runs setup and catches any errors, used if waiting for API to be available is not necessary
     */
    build() {
        this.setup().catch(e => console.error(e));
    };

    /**
     * Detach all events and remove table from DOM
     */
    destroy() {
        // hide and remove header menu if needed
        if (this.state.header_menu !== null) {
            if (this.state.header_menu_open) {
                this.hideHeaderMenu();
            }
            this.state.header_menu.elem.remove();
        }
        // hide and remove row menu if needed
        if (this.state.row_menu !== null) {
            if (this.state.row_menu_open) {
                this.hideRowMenu();
            }
            this.state.row_menu.elem.remove();
        }

        // if data tables is initialized, then we destroy and remove from dom
        if (this.state.table_api !== null) {
            this.state.table_api.destroy(true);
        }

        if (this.state.booted) {
            $(window).fxEventDestroy(['resize'], {namespace: `table.${this.state.idx}`});
            $(document).fxEventDestroy(['click'], {namespace: `table.${this.state.idx}`});
            if (this.state.has_fixed_columns) { // clean up fixed column plugin
                $(window).off('resize.DTFC');
            }
            this.elem.root.remove();
        }
    };

    on(event, closure) {
        if (typeof this.events[event] === 'undefined') {
            this.events[event] = [];
        }
        this.events[event].push(closure);
        return this;
    };

    fire(event, ...args) {
        if (typeof this.events[event] === 'undefined') {
            return;
        }
        this.events[event].forEach((closure) => {
            closure(...args);
        });
    };

    /**
     * Trim column data to specified length
     *
     * @param {*} data
     * @param {number} length
     * @param {boolean} [word_break=true]
     * @param {boolean} [esc_html=false]
     * @returns {*}
     */
    trimColumn(data, length, word_break = true, esc_html = false) {
        if (typeof data !== 'number' && typeof data !== 'string') {
            return data;
        }
        data = data.toString();
        if (data.length < length) {
            return data;
        }
        let shortened = data.substr(0, length - 1);

        // find the last white space character in the string
        if (word_break) {
            shortened = shortened.replace(/\s([^\s]*)$/, '');
        }
        if (esc_html) {
            shortened = escape(shortened);
        }
        return `<span title="${escape(data)}">${shortened}&#8230;</span>`;
    };
}

Base.__index = 0;

const PagingTypes = {
        [Base.Paging.NUMBERS]: 'numbers',
        [Base.Paging.SIMPLE]: 'simple',
        [Base.Paging.SIMPLE_NUMBERS]: 'simple_numbers',
        [Base.Paging.FULL]: 'full',
        [Base.Paging.FULL_NUMBERS]: 'full_numbers',
        [Base.Paging.FIRST_LAST_NUMBERS]: 'first_last_numbers'
    },
    SortTypes = {
        [Base.Sort.ASC]: 'asc',
        [Base.Sort.DESC]: 'desc'
    },
    Operators = {
        [Base.Operators.EQUAL]: 'eq',
        [Base.Operators.NOT_EQUAL]: 'not-eq',
        [Base.Operators.LESS_THAN]: 'lt',
        [Base.Operators.LESS_THAN_EQUAL]: 'lte',
        [Base.Operators.GREATER_THAN]: 'gt',
        [Base.Operators.GREATER_THAN_EQUAL]: 'gte',
        [Base.Operators.IN]: 'in',
        [Base.Operators.NOT_IN]: 'not-in',
        [Base.Operators.BETWEEN]: 'between',
        [Base.Operators.NOT_BETWEEN]: 'not-between',
        [Base.Operators.NULL]: 'null',
        [Base.Operators.NOT_NULL]: 'not-null'
    },
    Modifiers = {
        [Base.Modifiers.DATETIME]: 'datetime',
        [Base.Modifiers.DATE]: 'date',
        [Base.Modifiers.TIME]: 'time'
    },
    AlignTypes = {
        [Base.Align.LEFT]: 'align-left',
        [Base.Align.CENTER]: 'align-center',
        [Base.Align.RIGHT]: 'align-right'
    },
    KeyCodes = {
        BACKSPACE: 8,
        COMMA: 188,
        DELETE: 46,
        DOWN: 40,
        END: 35,
        ENTER: 13,
        ESCAPE: 27,
        HOME: 36,
        LEFT: 37,
        PAGE_DOWN: 34,
        PAGE_UP: 33,
        PERIOD: 190,
        RIGHT: 39,
        SPACE: 32,
        TAB: 9,
        UP: 38
    };

module.exports = Base;
