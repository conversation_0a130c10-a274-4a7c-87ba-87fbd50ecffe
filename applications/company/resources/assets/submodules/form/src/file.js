'use strict';

import EventEmitter from 'events';
import filesize from 'filesize';

import {Delete as DeleteModal} from './modals/file_delete';

/**
 * @memberof module:Form
 */
export class File {
    /**
     * Constructor
     *
     * @param {module:Form/Group/Field.File} field - File field instance
     * @param {Object} file - File entity
     * @param {string} file.id - File uuid
     * @param {string} file.file_id - Linked file uuid
     * @param {Object} file.file - Linked file data
     * @param {string} file.file.name - File name
     * @param {string} file.file.extension - File extension
     * @param {number} file.file.size - File size (in bytes)
     * @param {Object} file.file_media_urls - Container for media urls of file
     * @param {string} file.file_media_urls.original - Path to full file
     * @param {string} file.file_media_urls.field_thumbnail - Path to thumbnail
     */
    constructor(field, file) {
        /**
         * @private
         */
        this.state = {
            field: field,
            id: file.id,
            file_id: file.file_id,
            name: file.file.name,
            description: file.file.description,
            size: file.file.size,
            url: file.file_media_urls.original,
            thumbnail_url: file.file_media_urls.field_thumbnail,
            events: new EventEmitter
        };
    };

    /**
     * Get delete modal instance
     *
     * If modal doesn't exist, it will be created and cached for future calls
     *
     * @readonly
     *
     * @returns {Delete}
     */
    static get delete_modal() {
        if (this._delete_modal === undefined) {
            this._delete_modal = new DeleteModal;
        }
        return this._delete_modal;
    };

    /**
     * Get field
     *
     * @readonly
     *
     * @returns {module:Form/Group/Field.File}
     */
    get field() {
        return this.state.field;
    };

    /**
     * Get id
     *
     * @readonly
     *
     * @returns {string}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get file id
     *
     * @readonly
     *
     * @returns {string}
     */
    get file_id() {
        return this.state.file_id;
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Get size
     *
     * @readonly
     *
     * @returns {number}
     */
    get size() {
        return this.getSize();
    };

    /**
     * Get url
     *
     * @returns {string}
     */
    get url() {
        return this.state.url;
    };

    /**
     * Get thumbnail url
     *
     * @readonly
     *
     * @returns {string}
     */
    get thumbnail_url() {
        return this.state.thumbnail_url;
    };

    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     * @returns {module:Form.File}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Get size (optionally formatted)
     *
     * @param {boolean} [formatted=true] - Determines if size is formatted to be human readable
     *
     * @returns {(number|string)}
     */
    getSize(formatted = false) {
        let size = this.state.size;
        if (formatted) {
            size = filesize(size);
        }
        return size;
    };

    /**
     * Start delete process by opening confirmation modal
     *
     * @param {boolean} [from_lightbox=false] - Determines if call came from a lightbox
     */
    startDelete(from_lightbox = false) {
        File.delete_modal.open(this, from_lightbox);
    };

    /**
     * Delete file
     *
     * Just emits an event, other systems handle the actually deletion of a file by adding their own listener
     *
     * @emits module:Form/File~deleted
     */
    delete() {
        return new Promise((resolve, reject) => {
            this.field.deleteFile(this.id).then(() => {
                this.state.events.emit('deleted');
                this.destroy();
                resolve();
            }, (error) => {
                reject(error);
            });
        });
    };

    /**
     * Destroy file
     *
     * Just emits an event, other systems use this to remove files from their display
     *
     * @emits module:Form/File~destroy
     */
    destroy() {
        this.state.events.emit('destroy');
    };
}
