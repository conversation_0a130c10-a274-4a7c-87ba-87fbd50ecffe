/**
 * @module Api
 */

'use strict';

const Request = require('./request');

const Constants = {
    NULL: '(NULL)',
    AppNotification: {
        AssociationType: {
            CUSTOMER: 1,
            PROPERTY: 2,
            PROJECT: 3,
            LEAD: 4,
            TASK: 5,
            BID: 6,
        },
        Type: {
            INFO: 1,
            MAINTENANCE: 2,
            ERROR: 3
        },
        Placement: {
            GLOBAL: 1,
            NOTIFICATION_CENTER: 2,
            BANNER: 3,
        },
    },
    AppNotificationDistribution: {
        Status: {
            UNREAD: 1,
            SEEN: 2,
            READ: 3,
            COMPLETED: 4,
        }
    },
    AdditionalCosts: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    BidContent: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        },
        Type: {
            DISCLAIMER: 1,
            WAIVER: 2,
            WARRANTY: 3,
            CONTRACT: 4,
            ACKNOWLEDGEMENT: 5,
            INTRO: 6
        }
    },
    BidItems: {
        FollowUpNotificationStatus: {
            DISABLED: 1,
            ENABLED: 2,
            COMPLETED: 3
        },
        Status: {
            INCOMPLETE: 1,
            SUBMITTED: 5,
            FINALIZED: 2,
            ACCEPTED: 3,
            CANCELLED: 4,
            REJECTED: 6
        },
        Type: {
            GUIDED: 1,
            MANUAL: 2
        }
    },
    Company: {
        Status: {
            ACTIVE: 1,
            SUSPENDED: 2,
            DORMANT: 3,
            SETUP: 4,
            TRIAL: 5
        }
    },
    CompanyCustomReportResults: {
        Status: {
            GENERATING: 1,
            GENERATED: 2,
            EXPIRED: 3,
            FAILED: 4
        }
    },
    CompanyFormItems: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        },
        SourceFormType: {
            SYSTEM: 1,
            COMPANY: 2
        }
    },
    CompanyFormCategories: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        },
        Type: {
            BID: 1
        }
    },
    CompanyInvoices: {
        Status: {
            READY: 1,
            PENDING: 2,
            PAID: 3,
            FAILED: 4,
            REFUNDING: 5,
            REFUNDED: 6,
            VOIDING: 7,
            VOIDED: 8
        },
        Type: {
            GENERAL: 1,
            SUBSCRIPTION: 2
        }
    },
    CompanyPaymentMethods: {
        Type: {
            ACH: 1,
            CREDIT_CARD: 2
        }
    },
    CompanySubscriptions: {
        IntervalUnit: {
            DAY: 1,
            MONTH: 2,
            YEAR: 3
        },
        PriceAdjustments: {
            Type: {
                ADD_ONS: 1,
                DISCOUNTS: 2,
                FEES: 3
            },
            AmountType: {
                TOTAL: 1,
                PERCENTAGE: 2
            }
        },
        Status: {
            ACTIVE: 1,
            REPLACED: 2,
            CANCELLED: 3
        }
    },
    Drawings: {
        Status: {
            FINALIZED: 2
        }
    },
    EmailTemplates: {
        Source: {
            SYSTEM: 1,
            CUSTOM: 2
        },
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        },
        Type: {
            NEW_CUSTOMER: 1,
            SALES_APPOINTMENT: 2,
            SALES_APPOINTMENT_REMINDER: 3,
            CUSTOMER_BID: 4,
            BID_ACCEPTED: 5,
            BID_REJECTED: 6,
            INSTALLATION_APPOINTMENT: 7,
            INSTALLATION_APPOINTMENT_REMINDER: 8,
            WARRANTIES: 9,
            INVOICE: 10,
            BID_FOLLOW_UP: 11,
            NEW_LEAD: 12
        }
    },
    Events: {
        Source: {
            CUSTOMER_ADD: 1,
            PROPERTY_ADD: 2,
            PROJECT_ADD: 3,
            PROJECT_RESCHEDULE: 4,
            CALENDAR_RESCHEDULE: 5
        },
        Status: {
            ACTIVE:1,
            CANCELLED: 2,
            REPLACED: 3,
            COMPLETED: 4
        },
        Type: {
            SALES: 1,
            INSTALLATION: 2
        }
    },
    FormItems: {
        Type: {
            BID: 1
        }
    },
    GoogleCalendar: {
        OwnerType: {
            USER: 1,
            COMPANY: 2
        }
    },
    Leads: {
        Status: {
            NEW: 1,
            WORKING: 2,
            CONVERTED: 3,
            DEAD: 4
        },
        Priority: {
            HOT: 1,
            WARM: 2,
            COLD: 3,
            DEAD: 4
        },
        Origin: {
            STANDARD: 1,
            WEBSITE_LEADS_FORM: 2,
        }
    },
    ProjectTypes: {
        Status: {
            ACTIVE: 1,
            INACTIVE: 2,
            ARCHIVED: 3
        }
    },
    ResultTypes: {
        Status: {
            ACTIVE: 1,
            INACTIVE: 2,
            ARCHIVED: 3
        },
        Type: {
            PROJECT: 1,
        }
    },
    Materials: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    Media: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    ProductCategories: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    ProductItems: {
        PricingTypes: {
            BASIC: 1,
            COMPONENT: 2
        },
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    ProductItemPrices: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    ProductItemMaterials: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    ProductItemAdditionalCosts: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    Projects: {
      Status: {
          ACTIVE: 1,
          CANCELLED: 2,
          CLOSED: 3
      },
      Priority: {
          HIGH: 1,
          MEDIUM: 2,
          LOW: 3,
      }
    },
    Tasks: {
        Status: {
            ACTIVE: 1,
            COMPLETED: 2
        },
        Type: {
            TODO: 1,
            CALL: 2,
            EMAIL: 3,
            TEXT: 4,
            VISIT: 5
        },
        Priority: {
            LOW: 1,
            MEDIUM: 2,
            HIGH: 3
        },
        ReminderType: {
            AT_DUE_TIME: 1,
            THIRTY_MINUTES_BEFORE: 2,
            ONE_HOUR_BEFORE: 3,
            ONE_DAY_BEFORE: 4,
            ONE_WEEK_BEFORE: 5
        },
        AssociationType: {
            CUSTOMER: 1,
            PROPERTY: 2,
            PROJECT: 3,
            LEAD: 4
            // APPOINTMENT: 4,
            // DRAWING: 5,
            // BID: 6
        }
    },
    Units: {
        Status: {
            ACTIVE: 1,
            ARCHIVED: 2
        }
    },
    Users: {
        Status: {
            INACTIVE: 0,
            ACTIVE: 1,
            INVITED: 2
        }
    },
    Wisetack: {
        Prequals: {
            Status: {
                PENDING: 1,
                INITIATED: 2,
                PREQUALIFIED: 3,
                DECLINED: 4,
                EXPIRED: 5,
            }
        },
        Transactions: {
            Status: {
               PENDING: 1,
                INITIATED: 2,
                ACTIONS_REQUIRED: 3,
                AUTHORIZED: 4,
                LOAN_TERMS_ACCEPTED: 5,
                LOAN_CONFIRMED: 6,
                SETTLED: 7,
                DECLINED: 8,
                EXPIRED: 9,
                CANCELED: 10,
                REFUNDED: 11,
            },
            ActionsRequired: {
                ADD_DEBIT_CARD: 1,
                ADD_DRIVERS_LICENSE: 2
            }
        }
    }
};

const Resources = {
    AppNotificationDistribution: {
        endpoint: 'app-notification-distribution'
    },
    AppNotification: {
        endpoint: 'app-notification'
    },
    AdditionalCosts: {
        endpoint: 'additional-costs'
    },
    BidContent: {
        endpoint: 'bid/content'
    },
    BidForms: {
        endpoint: 'bid/forms'
    },
    BidItems: {
        endpoint: 'bid/items'
    },
    BidItemSections: {
        endpoint: 'bid/item/sections'
    },
    BidItemSectionForms: {
        endpoint: 'bid/item/section/forms'
    },
    ContentPartials: {
        endpoint: 'content-partials'
    },
    ContentTemplates: {
        endpoint: 'content-templates'
    },
    Companies: {
        endpoint: 'companies'
    },
    CompanyCustomReports: {
        endpoint: 'company/custom-reports'
    },
    CompanyCustomReportResults: {
        endpoint: 'company/custom-report/results'
    },
    CompanyFormCategories: {
        endpoint: 'company/form/categories'
    },
    CompanyFormItems: {
        endpoint: 'company/form/items'
    },
    CompanyInvoices: {
        endpoint: 'company/invoices'
    },
    CompanyPaymentMethods: {
        endpoint: 'company/payment-methods'
    },
    CompanySubscriptions: {
        endpoint: 'company/subscriptions'
    },
    Customers: {
        endpoint: 'customers'
    },
    Drawings: {
        endpoint: 'drawings'
    },
    EmailTemplates: {
        endpoint: 'email-templates'
    },
    Files: {
        endpoint: 'files'
    },
    FormItemEntries: {
        endpoint: 'form/item/entries'
    },
    ImportProducts: {
        endpoint: 'import/products'
    },
    ImportMaterials: {
        endpoint: 'import/materials'
    },
    ImportAdditionalCosts: {
        endpoint: 'import/additional-costs'
    },
    IndustryProductItems: {
        endpoint: 'industry/product/items'
    },
    Leads: {
        endpoint: 'leads'
    },
    LeadForms: {
      endpoint: 'lead-forms'
    },
    LeadFormsAction: {
        endpoint: 'lead-forms-action'
    },
    Materials: {
        endpoint: 'materials'
    },
    Media: {
        endpoint: 'media'
    },
    ProductCategories: {
        endpoint: 'product/categories'
    },
    ProductItems: {
        endpoint: 'product/items'
    },
    Projects: {
        endpoint: 'projects'
    },
    ProjectInfo: {
        endpoint: 'project/info'
    },
    ProjectTypes: {
        endpoint: 'project/types'
    },
    ProjectContacts: {
        endpoint: 'project/contacts'
    },
    ProjectEvents: {
        endpoint: 'project/events'
    },
    ProjectFiles: {
        endpoint: 'project/files'
    },
    ProjectNotes: {
        endpoint: 'project/notes'
    },
    Properties: {
        endpoint: 'properties'
    },
    SystemFormCategories: {
        endpoint: 'system/form/categories'
    },
    SystemFormItems: {
        endpoint: 'system/form/items'
    },
    Tasks: {
        endpoint: 'tasks'
    },
    Units: {
        endpoint: 'units'
    },
    Users: {
        endpoint: 'users'
    },
    WisetackPrequals: {
        endpoint: 'financing/prequals'
    },
    WisetackTransactions: {
        endpoint: 'financing/transactions'
    },
};

class Api {
    constructor(resources) {
        this.state = {
            library_version: 1,
            resource_version: 1
        };
        for (let name in resources) {
            this.register(name, resources[name]);
        }
    };

    libraryVersion(version) {
        this.state.library_version = version;
        return this;
    };

    newRequest(config) {
        return new Request(Object.assign({
            library_version: this.state.library_version,
            resource_version: this.state.resource_version,
            endpoint: ''
        }, config));
    };

    register(resource, config) {
        this[resource] = () => {
            return this.newRequest(config);
        }
    };
}

function factory() {
    return new Api(Resources);
}

module.exports = {
    Constants: Constants,
    ResourceFactory: factory,
    Resources: factory(),
    Filter: require('./filter'),
    Relation: require('./relation'),
    Request: Request,
    Response: require('./response'),
    Collection: require('./collection'),
    Entity: require('./entity'),
    BatchQueue: require('./batch/queue'),
    /**
     * @type {module:Api/Batch/Request}
     */
    BatchRequest: require('./batch/request')
};
